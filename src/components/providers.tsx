"use client";

import { SessionProvider } from "next-auth/react";
import { ThemeProvider } from "next-themes";
import { ReactNode } from "react";
import { TanstackQueryProvider } from "./providers/query-provider";
import { HydrationBoundaryProvider } from "./providers/hydration-boundary";
import { TRPCProvider } from "./providers/trpc-provider";

export function Providers({
  children,
  dehydratedState = {}
}: {
  children: ReactNode;
  dehydratedState?: unknown;
}) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="dark"
      enableSystem={false}
      forcedTheme="dark"
    >
      <SessionProvider>
        <TanstackQueryProvider>
          <HydrationBoundaryProvider state={dehydratedState}>
            <TRPCProvider>{children}</TRPCProvider>
          </HydrationBoundaryProvider>
        </TanstackQueryProvider>
      </SessionProvider>
    </ThemeProvider>
  );
}

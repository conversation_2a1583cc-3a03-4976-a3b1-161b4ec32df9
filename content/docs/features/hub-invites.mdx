---
title: <PERSON>b Invites
description: Learn how to create and manage hub invitation codes for your private hub.
---

import { Callout } from "fumadocs-ui/components/callout";
import { Tabs, Tab } from "fumadocs-ui/components/tabs";

## Creating Invite Codes

<Tabs items={["Slash Command", "Prefix Command"]}>
  <Tab value="Slash Command">
    ``` /hub invite create hub:YourHubName expiry:24h ```
  </Tab>
  <Tab value="Prefix Command">``` c!hub invite create YourHubName 24h ```</Tab>
</Tabs>

The `expiry` parameter is optional and defaults to 4 hours. Supported formats:

- `1h` (1 hour)
- `24h` (24 hours)
- `7d` (7 days)
- `30d` (30 days)

## Using Invite Codes

Users can join your hub using the invite code:

```
/hub join invite:abcdef1234
```

## Managing Invite Codes

- List active invites: `/hub invite list`
- Delete an invite: `/hub invite delete code:abcdef1234`
- Create temporary invite: `/hub invite create hub:YourHubName expiry:1h`

<Callout type="warning">
  Be careful who you share invite links with. Hub owners and managers are
  responsible for users who join.
</Callout>

## Permissions

| Action          | Required Permission      |
| --------------- | ------------------------ |
| Create invites  | Hub Owner or Hub Manager |
| Delete invites  | Hub Owner or Hub Manager |
| Join via invite | Any user                 |

## Best Practices

1. **Limited Duration**: Use short-lived invites for better control
2. **Selective Sharing**: Share invites only with trusted users
3. **Regular Cleanup**: Delete unused or expired invites
4. **Track Usage**: Monitor who joins through which invites

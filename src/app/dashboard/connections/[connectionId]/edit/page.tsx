"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import { Connection, Hub, ServerData } from "@/lib/generated/prisma/client";
import { ArrowLeft, Loader2, Save, Trash } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { ConnectionNavigationTabs } from "@/components/dashboard/connections/connection-navigation-tabs";

export default function ConnectionEditPage() {
  const { connectionId } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [connection, setConnection] = useState<
    (Connection & { hub: Hub; server: ServerData }) | null
  >(null);
  const [isConnected, setIsConnected] = useState(true);
  const [isCompact, setIsCompact] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  // Fetch connection data
  useEffect(() => {
    const fetchConnection = async () => {
      try {
        setIsLoading(true);

        const response = await fetch(
          `/api/dashboard/connections/${connectionId}`,
        );

        if (!response.ok) {
          throw new Error("Failed to fetch connection data");
        }

        const data = await response.json();
        setConnection(data.connection);
        setIsConnected(data.connection.connected);
        setIsCompact(data.connection.compact || false);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching connection:", error);
        toast({
          title: "Error",
          description: "Failed to load connection data. Please try again.",
          variant: "destructive",
        });
        router.push("/dashboard/servers");
      }
    };

    fetchConnection();
  }, [connectionId, toast, router]);

  const handleSave = async () => {
    try {
      setIsSaving(true);

      const response = await fetch(
        `/api/dashboard/connections/${connectionId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            connected: isConnected,
            compact: isCompact,
          }),
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update connection");
      }

      toast({
        title: "Connection Updated",
        description: "The connection has been updated successfully.",
      });

      router.push("/dashboard/servers?tab=connections");
    } catch (error) {
      console.error("Error updating connection:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to update connection",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (
      !confirm(
        "Are you sure you want to delete this connection? This action cannot be undone.",
      )
    ) {
      return;
    }

    try {
      setIsDeleting(true);

      const response = await fetch(
        `/api/dashboard/connections/${connectionId}`,
        {
          method: "DELETE",
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete connection");
      }

      toast({
        title: "Connection Deleted",
        description: "The connection has been deleted successfully.",
      });

      router.push("/dashboard/servers?tab=connections");
    } catch (error) {
      console.error("Error deleting connection:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to delete connection",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href="/dashboard/servers?tab=connections">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Edit Connection</h1>
        </div>
        <Button
          variant="destructive"
          size="sm"
          onClick={handleDelete}
          disabled={isDeleting}
        >
          {isDeleting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Deleting...
            </>
          ) : (
            <>
              <Trash className="h-4 w-4 mr-2" />
              Delete Connection
            </>
          )}
        </Button>
      </div>
      <ConnectionNavigationTabs connectionId={connectionId as string} currentTab="edit" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
        <Card className="border-gray-800 bg-[#0f1117] md:col-span-2">
          <CardHeader className="px-4 sm:px-6">
            <CardTitle>Connection Settings</CardTitle>
            <CardDescription>
              Configure how this connection works
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6 px-4 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="connected">Connection Enabled</Label>
                <div className="text-sm text-gray-400">
                  Enable or disable this connection
                </div>
              </div>
              <Switch
                id="connected"
                checked={isConnected}
                onCheckedChange={setIsConnected}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="compact">Compact Mode</Label>
                <div className="text-sm text-gray-400">
                  Show messages in a more compact format
                </div>
              </div>
              <Switch
                id="compact"
                checked={isCompact}
                onCheckedChange={setIsCompact}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="channel">Discord Channel</Label>
              <Select disabled>
                <SelectTrigger
                  id="channel"
                  className="bg-[#0a0a0c] border-gray-800"
                >
                  <SelectValue
                    placeholder={`Channel ID: ${connection?.channelId}`}
                  />
                </SelectTrigger>
                <SelectContent className="bg-[#0f1117] border-gray-800">
                  <SelectItem
                    value={connection?.channelId || "deleted channel"}
                  >
                    {`Channel ID: ${connection?.channelId}`}
                  </SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-400">
                To change the channel, you need to use{" "}
                <code>/connection edit</code> command on Discord.
              </p>
            </div>
          </CardContent>
          <CardFooter className="px-4 sm:px-6">
            <Button onClick={handleSave} disabled={isSaving} className="w-full">
              {isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  <span className="hidden sm:inline">Saving...</span>
                  <span className="inline sm:hidden">Saving</span>
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Save Changes</span>
                  <span className="inline sm:hidden">Save</span>
                </>
              )}
            </Button>
          </CardFooter>
        </Card>

        <div className="space-y-4 sm:space-y-6">
          <Card className="border-gray-800 bg-[#0f1117]">
            <CardHeader className="px-4 sm:px-6">
              <CardTitle>Hub</CardTitle>
              <CardDescription>
                The hub this connection links to
              </CardDescription>
            </CardHeader>
            <CardContent className="px-4 sm:px-6">
              <div className="flex items-center gap-3">
                <Image
                  src={connection?.hub.iconUrl || "/pfp1.png"}
                  alt={connection?.hub.name || "Hub"}
                  width={48}
                  height={48}
                  className="rounded-full"
                />
                <div>
                  <div className="font-medium">{connection?.hub.name}</div>
                  <Button
                    variant="link"
                    size="sm"
                    className="h-auto p-0"
                    asChild
                  >
                    <Link href={`/hubs/${connection?.hub.id}`} target="_blank">
                      View Hub
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-gray-800 bg-[#0f1117]">
            <CardHeader className="px-4 sm:px-6">
              <CardTitle>Server</CardTitle>
              <CardDescription>
                The Discord server this connection links to
              </CardDescription>
            </CardHeader>
            <CardContent className="px-4 sm:px-6">
              <div className="flex items-center gap-3">
                <Image
                  src={`https://api.dicebear.com/7.x/identicon/svg?seed=${connection?.server.id}`}
                  alt={connection?.server.name || "Server"}
                  width={48}
                  height={48}
                  className="rounded-full"
                />
                <div>
                  <div className="font-medium">{connection?.server.name}</div>
                  <Button
                    variant="link"
                    size="sm"
                    className="h-auto p-0"
                    asChild
                  >
                    <Link href={`/dashboard/servers/${connection?.server.id}`}>
                      View Server
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

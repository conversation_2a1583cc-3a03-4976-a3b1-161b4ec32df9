"use client";

import { useState, useEffect } from "react";
import { X, HelpCircle, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface OnboardingStep {
  title: string;
  description: string;
  position: "top" | "bottom" | "left" | "right";
  element?: string;
}

const onboardingSteps: OnboardingStep[] = [
  {
    title: "Welcome to InterChat Hubs!",
    description: "Discover and join active Discord chatrooms that connect multiple servers together.",
    position: "bottom",
  },
  {
    title: "Search for Hubs",
    description: "Use the search bar to find hubs by name, description, or browse by popular tags.",
    position: "bottom",
    element: "search-form",
  },
  {
    title: "Explore Hub Cards",
    description: "Each card shows a hub's details, member count, and upvotes. Click to view more or join.",
    position: "right",
    element: "hub-results",
  },
  {
    title: "Join a Hub",
    description: "Connect your Discord server to a hub to chat with other communities.",
    position: "bottom",
  },
];

export function OnboardingTooltip() {
  const [showTooltip, setShowTooltip] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    // Check if this is the user's first visit
    const hasSeenOnboarding = localStorage.getItem("hasSeenHubsOnboarding");
    if (!hasSeenOnboarding) {
      // Show tooltip after a short delay
      const timer = setTimeout(() => setShowTooltip(true), 1500);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleNext = () => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleClose();
    }
  };

  const handleClose = () => {
    setShowTooltip(false);
    localStorage.setItem("hasSeenHubsOnboarding", "true");
  };

  if (!showTooltip) {
    return (
      <Button
        variant="ghost"
        size="icon"
        className="fixed bottom-4 right-4 z-50 bg-primary/10 hover:bg-primary/20 text-primary rounded-full h-10 w-10"
        onClick={() => setShowTooltip(true)}
        aria-label="Show help"
      >
        <HelpCircle className="h-5 w-5" />
      </Button>
    );
  }

  const step = onboardingSteps[currentStep];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <Card className="w-full max-w-md mx-4 border-primary/20 bg-gray-900 text-white shadow-xl">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-xl text-primary">{step.title}</CardTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="h-8 w-8 rounded-full text-gray-400 hover:text-white"
              aria-label="Close onboarding"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription className="text-gray-400">
            Step {currentStep + 1} of {onboardingSteps.length}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-gray-200">{step.description}</p>
        </CardContent>
        <CardFooter className="flex justify-between border-t border-gray-800 pt-4">
          <Button
            variant="ghost"
            onClick={handleClose}
            className="text-gray-400 hover:text-white"
          >
            Skip
          </Button>
          <Button onClick={handleNext} className="bg-primary hover:bg-primary/90">
            {currentStep < onboardingSteps.length - 1 ? (
              <>
                Next <ArrowRight className="ml-2 h-4 w-4" />
              </>
            ) : (
              "Get Started"
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

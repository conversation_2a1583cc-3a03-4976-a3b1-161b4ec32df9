"use server";

import { <PERSON><PERSON> } from "@/components/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import prisma from "@/lib/prisma";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import {
  Calendar,
  Clock,
  Heart,
  Info,
  MessageSquare,
  ScrollText,
  Shield,
  Star,
  Tag,
  Users
} from "lucide-react";
import type { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";
import { notFound } from "next/navigation";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";
import rehypeSanitize from "rehype-sanitize";
import remarkGfm from "remark-gfm";
import { HubDetailSchema } from "../components/HubsSchema";
import JoinButton from "../components/hub-detail/JoinButton";
import UpvoteButton from "../components/hub-detail/UpvoteButton";
import ClientReviewSection from "../components/hub-detail/ClientReviewSection";
import ReviewItem from "../components/hub-detail/ReviewItem";

// Metadata function remains the same
export async function generateMetadata(props: {
  params: Promise<{ hubId: string }>;
}): Promise<Metadata> {
  const { hubId } = await props.params;
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    select: {
      name: true,
      description: true,
      tags: true,
      iconUrl: true,
      bannerUrl: true,
    },
    
  });

  if (!hub) return { title: "Hub Not Found" };

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://interchat.tech";

  return {
    title: `${hub.name} | InterChat Discord Community Hub`,
    description:
      hub.description ||
      "Join this active Discord community hub with InterChat. Connect your server and start chatting with other communities.",
    keywords: [
      "discord community",
      "discord hub",
      "connect discord servers",
      "interchat hub",
      ...(hub.tags.map(t => t.name) || []),
    ],
    openGraph: {
      title: `${hub.name} | InterChat Discord Community Hub`,
      description:
        hub.description ||
        "Join this active Discord community hub with InterChat. Connect your server and start chatting with other communities.",
      type: "website",
      url: `${baseUrl}/hubs/${hubId}`,
      images: [
        {
          url: hub.bannerUrl || hub.iconUrl || `${baseUrl}/InterChatLogo.webp`,
          width: 1200,
          height: 630,
          alt: hub.name,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      images: [hub.bannerUrl || hub.iconUrl || `${baseUrl}/InterChatLogo.webp`],
      title: `${hub.name} | InterChat Discord Community Hub`,
      description:
        hub.description ||
        "Join this active Discord community hub with InterChat.",
      creator: "@737_dev",
      site: "@interchatapp",
    },
    alternates: {
      canonical: `${baseUrl}/hubs/${hubId}`,
    },
  };
}

export default async function HubDetailView(props: {
  params: Promise<{ hubId: string }>;
}) {
  const { hubId } = await props.params;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://interchat.tech";
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    include: {
      connections: {
        where: { connected: true },
        select: {
          id: true,
          serverId: true,
          connected: true,
          compact: true,
          createdAt: true,
          lastActive: true,
          joinRequestsDisabled: true,
          server: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { lastActive: "desc" },
      },
      reviews: {
        include: { user: {select: {name: true, image: true, id: true}} },
        orderBy: { createdAt: "desc" },
        take: 10,
      },
      tags: { select: { name: true } },
      upvotes: true,
      moderators: {
        where: { user: { name: { not: null } } },
        include: { user: {select: {name: true, image: true, id: true}} },
      },
    },
  });

  if (!hub) {
    notFound();
  }

  const formattedDate = new Date(hub.createdAt).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  // Group reviews by rating to show analytics
  const reviewStats = {
    total: hub.reviews?.length || 0,
    average:
      hub.reviews?.reduce((acc, review) => acc + review.rating, 0) /
      (hub.reviews?.length || 1),
    distribution: [5, 4, 3, 2, 1].map((rating) => ({
      rating,
      count: hub.reviews?.filter((r) => r.rating === rating).length || 0,
      percentage: Math.round(
        ((hub.reviews?.filter((r) => r.rating === rating).length || 0) /
          (hub.reviews?.length || 1)) *
          100
      ),
    })),
  };

  // Enhance banner with animations and visual effects
  const bannerContent = hub.bannerUrl ? (
    <div className="relative h-full w-full">
      <Image
        src={hub.bannerUrl}
        alt={`${hub.name} banner`}
        fill
        className="object-cover"
        priority
        unoptimized
      />
      {/* Enhanced gradient overlay with animated particles */}
      <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent" />
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-center opacity-[0.1]" />
      <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-gray-950 to-transparent" />

      {/* Optional visual elements - subtle animated orbs */}
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/10 rounded-full blur-3xl opacity-30 animate-pulse" />
      <div
        className="absolute top-1/2 right-1/3 w-48 h-48 bg-purple-600/10 rounded-full blur-3xl opacity-20 animate-pulse"
        style={{ animationDelay: "1s" }}
      />
    </div>
  ) : (
    // Fallback banner with enhanced design
    <div className="relative h-full w-full bg-gradient-to-br from-gray-900 via-gray-950 to-black overflow-hidden">
      {/* Animated grid pattern */}
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-center opacity-[0.15]" />

      {/* Enhanced glow effects */}
      <div className="absolute top-1/4 left-1/5 w-72 h-72 bg-primary/15 rounded-full blur-3xl opacity-50 animate-pulse" />
      <div
        className="absolute bottom-1/4 right-1/6 w-96 h-96 bg-purple-500/15 rounded-full blur-3xl opacity-40 animate-pulse"
        style={{ animationDelay: "2s" }}
      />
      <div
        className="absolute top-1/2 left-1/2 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl opacity-30 animate-pulse"
        style={{ animationDelay: "3s" }}
      />

      {/* Optional floating particles */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/30 rounded-full animate-float"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animationDuration: `${Math.random() * 10 + 10}s`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>
    </div>
  );

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-b from-gray-950 to-gray-900 text-gray-200">
      {/* Schema.org data - hidden */}
      <HubDetailSchema hub={hub} baseUrl={baseUrl} />

      {/* Main Content */}
      <div className="flex-grow">
        {/* Enhanced Banner Section with taller height and parallax effect */}
        <div className="relative h-[320px] md:h-[400px] w-full overflow-hidden">
          {bannerContent}
        </div>

        {/* Page Content */}
        <div className="container mx-auto px-4 max-w-7xl pb-16">
          {/* Enhanced Header Card */}
          <div className="relative -mt-32 md:-mt-40 mb-12 transform transition-all duration-300">
            <div className="rounded-2xl border border-gray-800/70 bg-gray-900/90 p-6 md:p-8 shadow-xl backdrop-blur-xl">
              <div className="flex flex-col md:flex-row items-start md:items-center gap-6 relative">
                {/* Hub Icon with enhanced styling */}
                <div className="h-28 w-28 md:h-36 md:w-36 flex-shrink-0 rounded-2xl overflow-hidden border-4 border-gray-700/70 bg-gray-800/80 shadow-lg transform hover:scale-105 transition-transform duration-300 group">
                  <div className="relative h-full w-full">
                    <Image
                      src={hub.iconUrl || "/default-hub-icon.png"}
                      alt={hub.name}
                      width={144}
                      height={144}
                      className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-110"
                      unoptimized
                    />
                    {/* Subtle glow effect on hover */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 bg-gradient-to-tr from-primary/20 to-transparent transition-opacity duration-300" />
                  </div>
                </div>

                {/* Hub Info with enhanced typography */}
                <div className="flex-1 min-w-0">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <h1 className="text-3xl md:text-4xl font-bold break-words bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
                      {hub.name}
                    </h1>
                  </div>

                  {/* Brief description preview */}
                  <p className="mt-2 text-gray-300 text-sm md:text-base line-clamp-2">
                    {hub.description?.split("\n")[0] ||
                      "Join this active Discord community hub and connect with like-minded people."}
                  </p>

                  {/* Stats with improved styling */}
                  <div className="mt-4 flex flex-wrap items-center gap-x-4 gap-y-2 text-gray-300">
                    {/* Server Count */}
                    <span className="flex items-center gap-1.5 text-sm bg-gray-800/50 px-3 py-1.5 rounded-full border border-gray-700/30 hover:bg-gray-800 transition-colors">
                      <Users className="h-4 w-4 text-primary" />
                      {hub.connections.length} server
                      {hub.connections.length !== 1 ? "s" : ""}
                    </span>

                    {/* Upvotes */}
                    <span className="flex items-center gap-1.5 text-sm bg-gray-800/50 px-3 py-1.5 rounded-full border border-gray-700/30 hover:bg-gray-800 transition-colors">
                      <Heart className="h-4 w-4 text-rose-500" />
                      {hub.upvotes.length} upvote
                      {hub.upvotes.length !== 1 ? "s" : ""}
                    </span>

                    {/* Activity */}
                    <span className="flex items-center gap-1.5 text-sm bg-gray-800/50 px-3 py-1.5 rounded-full border border-gray-700/30 hover:bg-gray-800 transition-colors">
                      <Clock className="h-4 w-4 text-primary" />
                      {hub.lastActive
                        ? formatDistanceToNow(hub.lastActive, {
                            addSuffix: true,
                          })
                        : "No activity yet"}
                    </span>

                    {/* Tags with hover effect */}
                    {hub.tags && hub.tags.length > 0 && (
                      <span className="flex items-center gap-1.5 text-sm bg-gray-800/50 px-3 py-1.5 rounded-full border border-gray-700/30 hover:bg-gray-800 transition-colors">
                        <Tag className="h-4 w-4 text-primary" />
                        {hub.tags.join(", ")}
                      </span>
                    )}
                  </div>
                </div>

                {/* Action Buttons with enhanced styling */}
                <div className="flex flex-shrink-0 gap-3 mt-4 md:mt-0 self-start md:self-center">
                  {/* Enhanced join button with animation */}
                  <div className="relative group">
                    <JoinButton hubName={hub.name} hubId={hub.id} />
                  </div>

                  {/* Enhanced upvote button */}
                  <UpvoteButton hubId={hub.id} initialUpvotes={hub.upvotes} />
                </div>
              </div>
            </div>
          </div>

          {/* Main Grid (Tabs/Reviews + Sidebar) with improved layout */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column (Tabs and Reviews) */}
            <div className="lg:col-span-2 space-y-8">
              {/* Enhanced Tabs Section */}
              <Tabs defaultValue="overview" className="w-full">
                {/* Tab Triggers with improved styling */}
                <TabsList className="mb-6 inline-flex h-auto items-center justify-start w-full overflow-x-auto rounded-lg bg-gray-800/40 p-1.5 border border-gray-700/40 no-scrollbar">
                  {[
                    { value: "overview", icon: Info, label: "Overview" },
                    { value: "rules", icon: ScrollText, label: "Rules" },
                    {
                      value: "servers",
                      icon: Users,
                      label: "Connected Servers",
                    },
                  ].map((tab) => (
                    <TabsTrigger
                      key={tab.value}
                      value={tab.value}
                      className={cn(
                        "cursor-pointer inline-flex items-center justify-center whitespace-nowrap rounded-md px-4 py-2.5 text-sm font-medium ring-offset-background transition-all",
                        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                        "disabled:pointer-events-none disabled:opacity-50",
                        "data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary data-[state=active]:to-primary-alt data-[state=active]:text-white data-[state=active]:shadow-md", // Active state
                        "data-[state=active]:animate-tab-glow", // Animation for active tab
                        "text-gray-300 hover:bg-gray-700/50 hover:text-white" // Inactive state
                      )}
                    >
                      <tab.icon className="h-4 w-4 mr-2" />
                      {tab.label}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {/* Tab Content Panes with improved styling */}
                <TabsContent
                  value="overview"
                  className={cn(
                    "rounded-xl border border-gray-800/70 bg-gray-900/60 p-6 md:p-8 shadow-lg backdrop-blur-md", // Container with padding
                    "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" // Standard focus styles
                  )}
                >
                  {/* Overview with enhanced typography */}
                  <div className="prose prose-invert max-w-none prose-p:text-gray-300 prose-headings:text-white prose-a:text-primary hover:prose-a:text-primary-alt prose-img:rounded-lg prose-img:shadow-lg">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      rehypePlugins={[rehypeRaw, rehypeSanitize]}
                    >
                      {hub.description || "No description provided."}
                    </ReactMarkdown>
                  </div>
                </TabsContent>

                <TabsContent
                  value="rules"
                  className={cn(
                    "rounded-xl border border-gray-800/70 bg-gray-900/60 p-6 md:p-8 shadow-lg backdrop-blur-md",
                    "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  )}
                >
                  {/* Rules with improved styling */}
                  {hub.rules?.length ? (
                    <div className="space-y-4">
                      {hub.rules.map((rule, index) => (
                        <div
                          key={index}
                          className="flex items-start gap-4 rounded-lg border border-gray-800/60 bg-gray-900/50 p-5 hover:bg-gray-800/30 transition-colors duration-200"
                        >
                          <div className="mt-0.5 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-r from-primary to-primary-alt text-sm font-semibold text-white shadow-sm">
                            {index + 1}
                          </div>
                          <div className="prose prose-sm prose-invert max-w-none prose-p:my-0 text-gray-300">
                            <ReactMarkdown>{rule}</ReactMarkdown>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-10 text-gray-400">
                      <ScrollText className="h-12 w-12 text-gray-500 mb-3 opacity-50" />
                      <p className="text-center text-gray-400">
                        No rules specified for this hub.
                      </p>
                      <p className="text-center text-gray-500 text-sm mt-1">
                        Please follow Discord&apos;s community guidelines.
                      </p>
                    </div>
                  )}
                </TabsContent>

                {/* New tab: Connected Servers */}
                <TabsContent
                  value="servers"
                  className={cn(
                    "rounded-xl border border-gray-800/70 bg-gray-900/60 p-6 md:p-8 shadow-lg backdrop-blur-md",
                    "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  )}
                >
                  {hub.connections.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {hub.connections.slice(0, 10).map((conn) => (
                        <div
                          key={conn.id}
                          className="flex items-center gap-4 rounded-lg border border-gray-800/60 bg-gray-800/30 p-4 hover:bg-gray-800/50 transition-colors duration-200"
                        >
                          <div className="h-12 w-12 rounded-lg overflow-hidden border border-gray-700/50">
                            <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                              <Users className="h-6 w-6 text-gray-300" />
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium text-white truncate">
                              {conn.server?.name || "Discord Server"}
                            </h4>
                            <p className="text-xs text-gray-400 flex items-center mt-1">
                              <Clock className="h-3 w-3 mr-1" />
                              {formatDistanceToNow(new Date(conn.lastActive), {
                                addSuffix: true,
                              })}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-10 text-gray-400">
                      <Users className="h-12 w-12 text-gray-500 mb-3 opacity-50" />
                      <p className="text-center text-gray-400">
                        No servers connected yet
                      </p>
                      <p className="text-center text-gray-500 text-sm mt-1">
                        Be the first to connect your Discord server!
                      </p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>

              {/* Community Activity Section */}
              <div className="rounded-xl border border-gray-800/70 bg-gray-900/60 p-6 md:p-8 shadow-lg backdrop-blur-md">
                {/* Reviews Analytics */}
                <div className="flex flex-col md:flex-row gap-6 mb-8">
                  <div className="bg-gray-800/40 rounded-xl p-5 flex-1 border border-gray-700/40">
                    <div className="flex items-end gap-2 mb-2">
                      <span className="text-4xl font-bold text-white">
                        {reviewStats.average.toFixed(1)}
                      </span>
                      <div className="flex items-center text-amber-400 mb-1">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            className={cn(
                              "h-4 w-4",
                              i < Math.round(reviewStats.average)
                                ? "fill-amber-400"
                                : "fill-gray-700"
                            )}
                          />
                        ))}
                      </div>
                    </div>
                    <p className="text-sm text-gray-400">
                      {reviewStats.total} review
                      {reviewStats.total !== 1 ? "s" : ""}
                    </p>
                  </div>

                  {/* Rating distribution */}
                  <div className="bg-gray-800/40 rounded-xl p-5 flex-1 border border-gray-700/40">
                    <div className="space-y-2">
                      {reviewStats.distribution.map((item) => (
                        <div
                          key={item.rating}
                          className="flex items-center gap-3"
                        >
                          <div className="flex items-center gap-1 w-12">
                            <span className="text-sm font-medium text-gray-300">
                              {item.rating}
                            </span>
                            <Star className="h-3 w-3 text-amber-400 fill-amber-400" />
                          </div>
                          <div className="flex-1 h-2 bg-gray-700 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-gradient-to-r from-amber-500 to-amber-400"
                              style={{ width: `${item.percentage}%` }}
                            />
                          </div>
                          <span className="text-xs font-medium text-gray-400 w-10 text-right">
                            {item.percentage}%
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Review Form */}
                <ClientReviewSection hubId={hub.id} />

                {/* Review list */}
                <div className="space-y-4">
                  {hub.reviews?.length > 0 ? (
                    <>
                      {hub.reviews.map((review) => (
                        <ReviewItem key={review.id} review={review} hubId={hub.id} />
                      ))}
                    </>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-10 text-gray-400">
                      <MessageSquare className="h-12 w-12 text-gray-500 mb-3 opacity-50" />
                      <p className="text-center text-gray-400">
                        No reviews yet
                      </p>
                      <p className="text-center text-gray-500 text-sm mt-1">
                        Be the first to review this hub!
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
            {/* Right Column (Sidebar) with enhanced styling */}
            <div className="lg:col-span-1 space-y-4 sm:space-y-6">
              {/* Join Hub Card */}
              <div className="rounded-xl border border-gray-800/70 bg-gray-900/60 p-4 sm:p-6 shadow-lg backdrop-blur-md">
                <h3 className="text-lg sm:text-xl font-semibold text-white mb-3 sm:mb-5 flex items-center">
                  <Users className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-primary" />
                  Join This Hub
                </h3>

                <div className="space-y-4">
                  <p className="text-sm text-gray-300">
                    Connect your Discord server to this hub and start chatting
                    with other communities!
                  </p>

                  {/* Command example */}
                  <div className="bg-gray-800/50 rounded-lg p-3 border border-gray-700/30">
                    <p className="text-xs text-gray-400 mb-2">
                      Use this command in your Discord server:
                    </p>
                    <div className="flex items-center justify-between bg-gray-900 rounded px-3 py-2 font-mono text-sm">
                      <code className="text-primary-alt">
                        /connect hub:{hub.name}
                      </code>
                    </div>
                  </div>

                  {/* CTA Button */}
                  <Link href={`/dashboard/servers?hubId=${hub.id}`}>
                    <Button className="cursor-pointer w-full py-2.5 px-4 bg-gradient-to-r from-primary to-primary-alt text-white font-medium rounded-lg hover:opacity-90 transition-opacity">
                      Connect Your Server
                    </Button>
                  </Link>
                </div>
              </div>

              {/* Hub Details Card with enhanced styling */}
              <div className="rounded-xl border border-gray-800/70 bg-gray-900/60 p-4 sm:p-6 shadow-lg backdrop-blur-md">
                <h3 className="text-lg sm:text-xl font-semibold text-white mb-3 sm:mb-5 flex items-center">
                  <Info className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-primary" />
                  Hub Details
                </h3>
                <dl className="space-y-2 sm:space-y-3">
                  {/* Created Date */}
                  <div className="flex items-center justify-between rounded-md bg-gray-800/40 p-3 border border-gray-700/30 hover:bg-gray-800/60 transition-colors">
                    <dt className="flex items-center text-sm text-gray-400">
                      <Calendar className="h-4 w-4 mr-2 text-primary/80" />
                      Created
                    </dt>
                    <dd className="text-sm font-medium text-gray-200">
                      {formattedDate}
                    </dd>
                  </div>

                  {/* Last Message */}
                  <div className="flex items-center justify-between rounded-md bg-gray-800/40 p-3 border border-gray-700/30 hover:bg-gray-800/60 transition-colors">
                    <dt className="flex items-center text-sm text-gray-400">
                      <MessageSquare className="h-4 w-4 mr-2 text-primary/80" />
                      Last Message
                    </dt>
                    <dd className="text-sm font-medium text-gray-200">
                      {hub.lastActive
                        ? formatDistanceToNow(hub.lastActive, {
                            addSuffix: true,
                          })
                        : "No activity yet"}
                    </dd>
                  </div>

                  {/* Connected Servers */}
                  <div className="flex items-center justify-between rounded-md bg-gray-800/40 p-3 border border-gray-700/30 hover:bg-gray-800/60 transition-colors">
                    <dt className="flex items-center text-sm text-gray-400">
                      <Users className="h-4 w-4 mr-2 text-primary/80" />
                      Connected Servers
                    </dt>
                    <dd className="text-sm font-medium text-gray-200">
                      {hub.connections.length}
                    </dd>
                  </div>

                  {/* Upvotes */}
                  <div className="flex items-center justify-between rounded-md bg-gray-800/40 p-3 border border-gray-700/30 hover:bg-gray-800/60 transition-colors">
                    <dt className="flex items-center text-sm text-gray-400">
                      <Heart className="h-4 w-4 mr-2 text-rose-500/80" />
                      Upvotes
                    </dt>
                    <dd className="text-sm font-medium text-gray-200">
                      {hub.upvotes.length}
                    </dd>
                  </div>
                </dl>
              </div>

              {/* Moderators Card with enhanced styling */}
              <div className="rounded-xl border border-gray-800/70 bg-gray-900/60 p-4 sm:p-6 shadow-lg backdrop-blur-md">
                <h3 className="text-lg sm:text-xl font-semibold text-white mb-3 sm:mb-5 flex items-center">
                  <Shield className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-primary" />
                  Moderators
                </h3>
                {hub.moderators && hub.moderators.length > 0 ? (
                  <div className="space-y-3">
                    {hub.moderators.map((mod) => (
                      <div
                        key={mod.id}
                        className="flex items-center gap-3 rounded-md p-2 transition-colors hover:bg-gray-800/50"
                      >
                        <div className="h-8 w-8 sm:h-10 sm:w-10 flex-shrink-0 overflow-hidden rounded-full bg-gray-800 border border-gray-700/50">
                          <Image
                            src={mod.user?.image || "/default-avatar.png"}
                            alt={mod.user?.name || "User Avatar"}
                            width={40}
                            height={40}
                            className="object-cover"
                            unoptimized
                          />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="truncate text-xs sm:text-sm font-medium text-gray-200">
                            {mod.user?.name}
                          </p>
                          <p className="truncate text-xs text-gray-400">
                            {mod.role || "Moderator"}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="py-3 text-center text-sm text-gray-400">
                    No moderators listed
                  </p>
                )}
              </div>

              {/* Similar Hubs Card */}
              <div className="rounded-xl border border-gray-800/70 bg-gray-900/60 p-4 sm:p-6 shadow-lg backdrop-blur-md">
                <h3 className="text-lg sm:text-xl font-semibold text-white mb-3 sm:mb-5 flex items-center">
                  <Tag className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-primary" />
                  Similar Hubs
                </h3>

                <div className="space-y-3">
                  {/* Placeholder for similar hubs - would be dynamically populated */}
                  {[1, 2, 3].map((i) => (
                    <div
                      key={i}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-800/40 transition-colors"
                    >
                      <div className="h-10 w-10 rounded-lg overflow-hidden border border-gray-700/50">
                        <Image
                          src={`https://api.dicebear.com/7.x/shapes/svg?seed=hub${i}`}
                          alt={`Similar Hub ${i}`}
                          width={40}
                          height={40}
                          className="object-cover"
                          unoptimized
                        />
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="truncate text-sm font-medium text-gray-200">
                          {
                            [
                              "Gaming Central",
                              "Anime Hangout",
                              "Tech Community",
                            ][i - 1]
                          }
                        </p>
                        <p className="truncate text-xs text-gray-400">
                          {[24, 18, 32][i - 1]} servers
                        </p>
                      </div>
                    </div>
                  ))}

                  <Button className="w-full mt-2 py-2 px-4 bg-gray-800/60 hover:bg-gray-700/60 text-white text-sm font-medium rounded-lg border border-gray-700/30 transition-colors">
                    Explore More Hubs
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

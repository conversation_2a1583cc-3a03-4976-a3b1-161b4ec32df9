"use client";

import { motion } from "motion/react";
import { useEffect, useState } from "react";

export function AnimatedBackground() {
  // Use state to ensure this only renders on the client
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null; // Return nothing on server-side
  }

  return (
    <>
      {/* Background elements */}
      <div className="absolute inset-0 top-[-64px] bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-primary/10 via-transparent to-transparent opacity-80"></div>

      {/* Animated dots - Client-side only */}
      <div className="absolute inset-0 top-[-64px] overflow-hidden opacity-20">
        {Array.from({ length: 15 }).map((_, i) => {
          // Use deterministic values based on index
          const leftPos = (i * 7919) % 100; // Using prime numbers for better distribution
          const topPos = (i * 6997) % 100;
          const duration = (i % 5) + 2; // 2-6 seconds
          const delay = (i % 10) * 0.5; // 0-4.5 seconds delay

          return (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-primary rounded-full transform-gpu"
              style={{
                left: `${leftPos}%`,
                top: `${topPos}%`,
                willChange: "transform, opacity",
              }}
              animate={{
                opacity: [0.2, 1, 0.2],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: duration,
                repeat: Infinity,
                ease: "easeInOut",
                delay: delay,
                repeatDelay: 0.5,
              }}
            />
          );
        })}
      </div>
    </>
  );
}

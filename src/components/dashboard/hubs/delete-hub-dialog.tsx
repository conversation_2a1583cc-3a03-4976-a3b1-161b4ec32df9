"use client";

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface DeleteHubDialogProps {
  hubId: string;
  hubName: string;
}

export function DeleteHubDialog({ hubId, hubName }: DeleteHubDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [confirmText, setConfirmText] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleDelete = async () => {
    if (confirmText !== hubName) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/hubs/${hubId}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          confirmName: confirmText,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete hub");
      }

      toast({
        title: "Hub Deleted",
        description: "Your hub has been permanently deleted.",
      });

      // Close the dialog and redirect to the hubs page
      setIsOpen(false);
      router.push("/dashboard/hubs");
      router.refresh();
    } catch (error) {
      console.error("Error deleting hub:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to delete hub",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const isConfirmValid = confirmText === hubName;

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <Button
          variant="destructive"
          className="bg-red-600 hover:bg-red-700 text-white"
        >
          <Trash className="h-4 w-4 mr-2" />
          Delete Hub
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="bg-gray-900 border border-gray-800">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-red-500">
            Delete Hub
          </AlertDialogTitle>
          <AlertDialogDescription className="text-gray-400">
            This action cannot be undone. This will permanently delete the hub
            <span className="font-semibold text-gray-300"> {hubName} </span>
            and all of its data, including connections, members, and settings.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="py-4">
          <div className="space-y-2">
            <p className="text-sm text-gray-400">
              To confirm, type{" "}
              <span className="font-semibold text-gray-300">{hubName}</span>{" "}
              below:
            </p>
            <Input
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder={`Type "${hubName}" to confirm`}
              className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-red-500/50"
              autoComplete="off"
            />
          </div>

          <div className="mt-4 p-3 bg-red-950/20 border border-red-900/30 rounded-md">
            <h4 className="text-sm font-medium text-red-400">Warning</h4>
            <ul className="mt-2 text-xs text-gray-400 space-y-1">
              <li>• All connections to Discord servers will be removed</li>
              <li>• All members and moderators will lose access</li>
              <li>• All hub settings and rules will be deleted</li>
              <li>• This action is permanent and cannot be reversed</li>
            </ul>
          </div>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel className="bg-gray-800 hover:bg-gray-700 border-gray-700">
            Cancel
          </AlertDialogCancel>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={!isConfirmValid || isDeleting}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              "Delete Hub"
            )}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

import { auth } from "@/auth";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { isUserBlacklisted, isServerBlacklisted } from "@/lib/blacklist";

// Schema for creating a connection
const createConnectionSchema = z.object({
  hubId: z.string(),
  serverId: z.string(),
  channelId: z.string(),
});

// Schema for the response
interface ConnectionResponse {
  id: string;
  hubId: string;
  hubName: string;
  hubIcon: string;
  serverId: string;
  server: { id: string; name: string; iconUrl: string | null };
  channelId: string;
  connected: boolean;
  compact: boolean;
  createdAt: string;
  lastActive: string;
}

// GET all connections for the user
export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;

    // Get all hubs where the user has access to
    const userHubs = await prisma.hub.findMany({
      where: {
        OR: [{ ownerId: userId }, { moderators: { some: { userId } } }],
      },
      select: { id: true },
      
    });

    const hubIds = userHubs.map((hub) => hub.id);

    // Get all connections for these hubs
    const connections = await prisma.connection.findMany({
      where: { hubId: { in: hubIds } },
      include: {
        hub: {
          select: {
            id: true,
            name: true,
            iconUrl: true,
          },
        },
        server: {
          select: {
            id: true,
            name: true,
            iconUrl: true,
          },
        },
      },
      
    });

    return NextResponse.json({ connections: connections });
  } catch (error) {
    console.error("Error fetching connections:", error);
    return NextResponse.json(
      { error: "Failed to fetch connections" },
      { status: 500 }
    );
  }
}

// POST (create) a new connection
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = createConnectionSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 }
      );
    }

    const { hubId, serverId, channelId } = validation.data;
    const userId = session.user.id;

    // Check if the user has permission to manage this hub's connections
    const permissionLevel = await getUserHubPermission(userId, hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json(
        {
          error: "You don't have permission to manage connections for this hub",
        },
        { status: 403 }
      );
    }

    // Check if the user is blacklisted from the hub
    const userBlacklisted = await isUserBlacklisted(userId, hubId);
    if (userBlacklisted) {
      return NextResponse.json(
        { error: "You are blacklisted from this hub" },
        { status: 403 }
      );
    }

    // Check if the server is blacklisted from the hub
    const serverBlacklisted = await isServerBlacklisted(serverId, hubId);
    if (serverBlacklisted) {
      return NextResponse.json(
        { error: "This server is blacklisted from this hub" },
        { status: 403 }
      );
    }

    // Check if the channel is already connected to another hub
    const existingChannelConnection = await prisma.connection.findFirst({
      where: { channelId },
      
    });

    if (existingChannelConnection) {
      return NextResponse.json(
        { error: "This channel is already connected to a hub" },
        { status: 400 }
      );
    }

    // Check if the server is already connected to this specific hub
    const existingHubServerConnection = await prisma.connection.findFirst({
      where: { serverId, hubId },
      
    });

    if (existingHubServerConnection) {
      return NextResponse.json(
        { error: "This server is already connected to this hub" },
        { status: 400 }
      );
    }

    // Get hub details
    const hub = await prisma.hub.findUnique({
      where: { id: hubId },
      select: {
        id: true,
        name: true,
        iconUrl: true,
      },
      
    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }

    // Create the connection
    const connection = await prisma.connection.create({
      data: {
        hubId,
        serverId,
        channelId,
        connected: true,
        compact: false,
        webhookURL: "", // FIXME: This would be set by the bot
        lastActive: new Date(),
      },
    });

    // Get server details
    let serverName = "Unknown Server";
    let serverIcon = null;

    try {
      const serverResponse = await fetch(
        `${request.nextUrl.origin}/api/servers/${serverId}`
      );
      if (serverResponse.ok) {
        const serverData = await serverResponse.json();
        serverName = serverData.server.name;
        serverIcon = serverData.server.iconUrl;
      }
    } catch (error) {
      console.error(`Error fetching server ${serverId}:`, error);
    }

    // Format the response
    const newConnection: ConnectionResponse = {
      id: connection.id,
      hubId,
      hubName: hub.name,
      hubIcon: hub.iconUrl,
      server: { id: serverId, name: serverName, iconUrl: serverIcon },
      channelId,
      serverId,
      connected: true,
      compact: false,
      createdAt: connection.createdAt.toISOString(),
      lastActive: connection.lastActive.toISOString(),
    };

    return NextResponse.json({ connection: newConnection }, { status: 201 });
  } catch (error) {
    console.error("Error creating connection:", error);
    return NextResponse.json(
      { error: "Failed to create connection" },
      { status: 500 }
    );
  }
}

"use client";

import { useQuery } from "@/lib/tanstack-query";
import { fetchApi } from "@/lib/api";
import { useErrorNotification } from "./use-error-notification";
import { Appeal, Hub, Infraction } from "@/lib/generated/prisma/client";

// Types
export interface AppealWithInfraction extends Appeal {
  infraction: Infraction & { hub: Hub };
}

export interface AppealsResponse {
  appeals: AppealWithInfraction[];
  total: number;
}

// Query keys
export const appealKeys = {
  all: ["appeals"] as const,
  lists: () => [...appealKeys.all, "list"] as const,
  list: (filters: { myAppeals?: boolean; page?: number; limit?: number }) => [...appealKeys.lists(), filters] as const,
  details: () => [...appealKeys.all, "detail"] as const,
  detail: (id: string) => [...appealKeys.details(), id] as const,
};

// Fetch appeals
export async function getAppeals(params: {
  myAppeals?: boolean;
  page?: number;
  limit?: number;
}): Promise<AppealsResponse> {
  const queryParams = new URLSearchParams();
  
  if (params.myAppeals) {
    queryParams.append("myAppeals", "true");
  }
  
  if (params.page !== undefined) {
    queryParams.append("page", params.page.toString());
  }
  
  if (params.limit !== undefined) {
    queryParams.append("limit", params.limit.toString());
  }
  
  return fetchApi<AppealsResponse>(`/api/appeals?${queryParams.toString()}`);
}

// Hook for fetching appeals
export function useAppeals(params: {
  myAppeals?: boolean;
  page?: number;
  limit?: number;
}) {
  const query = useQuery({
    queryKey: appealKeys.list(params),
    queryFn: () => getAppeals(params),
  });

  // Handle error notification
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: "Error",
    description: "Failed to load appeals",
  });

  return query;
}

// Hook for fetching my appeals
export function useMyAppeals(page: number = 1, limit: number = 10) {
  return useAppeals({ myAppeals: true, page, limit });
}

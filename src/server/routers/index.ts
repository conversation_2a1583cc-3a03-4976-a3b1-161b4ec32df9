/**
 * Main tRPC router that combines all sub-routers
 */
import { router } from '../trpc';
import { hubRouter } from './hub';
import { userRouter } from './user';
import { serverRouter } from './server';
import { moderationRouter } from './moderation';
import { announcementRouter } from './announcement';

export const appRouter = router({
  hub: hubRouter,
  user: userRouter,
  server: serverRouter,
  moderation: moderationRouter,
  announcement: announcementRouter,
});

// Export type definition of API
export type AppRouter = typeof appRouter;

import { baseOptions } from "@/app/layout.config";
import Footer from "@/components/Footer";
import { HomeLayout } from "fumadocs-ui/layouts/home";
import { RootProvider } from "fumadocs-ui/provider";
import type { ReactNode } from "react";

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <RootProvider>
      <HomeLayout {...baseOptions} className="pt-0">
        {children}
        <Footer />
      </HomeLayout>
    </RootProvider>
  );
}

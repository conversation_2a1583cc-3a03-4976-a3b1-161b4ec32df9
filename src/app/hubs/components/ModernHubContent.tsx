"use server";

import { HubListingSchema } from "./HubsSchema";
import { getSortedHubs } from "../utils";
import prisma from "@/lib/prisma";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";
import { SortOptions } from "../constants";
import { ModernHubGrid } from "./ModernHubGrid";
import { QuickFilterBar } from "./QuickFilterBar";
import { GridPattern } from "@/components/magicui/grid-pattern";

/**
 * Fetch recently created hubs
 */
async function getRecentHubs(): Promise<SimplifiedHub[]> {
  return await prisma.hub.findMany({
    where: { private: false },
    include: {
      connections: {
        where: { connected: true },
        select: { id: true, serverId: true, lastActive: true },
      },
      moderators: {
        include: { user: { select: { id: true, name: true, image: true } } },
      },
      upvotes: true,
      reviews: true,
      tags: { select: { name: true } },
    },
    orderBy: { createdAt: "desc" },
    take: 6,
  });
}

export async function ModernHubContent() {
  // Fetch all hub data in parallel for better performance
  const [mostRecentPopular, mostUpvotedNew, recentHubs, trendingHubs] =
    await Promise.all([
      getSortedHubs({ private: false }, 0, SortOptions.MostRecentPopular),
      getSortedHubs({ private: false }, 0, SortOptions.MostUpvotedNew),
      getRecentHubs(),
      getSortedHubs({ private: false }, 0, SortOptions.Trending),
    ]);

  // Get the base URL for structured data
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://interchat.tech";

  // Create a deduplicated list of all hubs for the schema
  const allUniqueHubs = [
    ...mostRecentPopular.hubs,
    ...mostUpvotedNew.hubs,
    ...recentHubs,
    ...trendingHubs.hubs,
  ].filter(
    (hub, index, self) => index === self.findIndex((h) => h.id === hub.id)
  );

  // Extract all unique tags from hubs for quick filters
  const allTags = new Set<string>();
  allUniqueHubs.forEach((hub) => {
    hub.tags?.forEach((tag) => {
      if (typeof tag === 'string') {
        allTags.add(tag);
      } else if (tag && typeof tag === 'object' && 'name' in tag) {
        allTags.add(tag.name as string);
      }
    });
  });

  // Get the most common tags (up to 10)
  const popularTags = Array.from(allTags).slice(0, 10);

  return (
    <div className="relative bg-gradient-to-b from-gray-950 to-gray-900 pb-20">
      {/* Background pattern */}
      <div className="absolute inset-0 overflow-hidden">
        <GridPattern
          width={40}
          height={40}
          x={-1}
          y={-1}
          className="absolute inset-0 h-full w-full stroke-gray-800/20 [mask-image:radial-gradient(100%_100%_at_top_center,white,transparent)]"
          strokeDasharray="0 4 8"
        />
      </div>

      {/* Structured data for SEO */}
      <HubListingSchema hubs={allUniqueHubs} baseUrl={baseUrl} />

      {/* Quick filter bar */}
      <div className="container mx-auto px-4 py-6">
        <QuickFilterBar popularTags={popularTags} />
      </div>

      {/* Main hub grid */}
      <div className="container mx-auto px-4">
        <ModernHubGrid 
          mostRecentPopular={mostRecentPopular}
          mostUpvotedNew={mostUpvotedNew}
          recentHubs={recentHubs}
          trendingHubs={trendingHubs}
        />
      </div>
    </div>
  );
}

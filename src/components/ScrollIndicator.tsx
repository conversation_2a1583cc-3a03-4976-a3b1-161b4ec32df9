import { memo, useEffect, useState } from "react";
import { ChevronDown } from "lucide-react";

const ScrollIndicator = memo(() => {
  const [showIndicator, setShowIndicator] = useState(true);

  useEffect(() => {
    // Check if there's content to scroll to
    const checkScrollableContent = () => {
      const documentHeight = document.body.scrollHeight;
      const viewportHeight = window.innerHeight;

      // Only show if there's content beyond the viewport
      setShowIndicator(documentHeight > viewportHeight + 100);
    };

    // Check on initial load
    checkScrollableContent();

    // Check when window is resized with debounce for better performance
    let resizeTimer: NodeJS.Timeout;
    const debouncedResize = () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(checkScrollableContent, 100);
    };
    window.addEventListener("resize", debouncedResize);

    // Hide when user starts scrolling with throttle for better performance
    let ticking = false;
    const handleScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          if (window.scrollY > 100) {
            setShowIndicator(false);
          } else {
            // Check again if there's content to scroll to
            checkScrollableContent();
          }
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    // Clean up event listeners
    return () => {
      clearTimeout(resizeTimer);
      window.removeEventListener("resize", debouncedResize);
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  if (!showIndicator) return null;

  return (
    <div
      className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center text-center cursor-default animate-bounce transform-gpu"
      style={{ willChange: "transform" }}
    >
      <p className="text-sm font-medium mb-2 text-gray-600 dark:text-gray-300">
        Scroll to explore
      </p>
      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-white/40 dark:bg-white/10 backdrop-blur-sm border border-white/30 dark:border-white/20 shadow-md">
        <ChevronDown className="h-5 w-5 text-primary" />
      </div>
    </div>
  );
});

ScrollIndicator.displayName = "ScrollIndicator";

export default ScrollIndicator;

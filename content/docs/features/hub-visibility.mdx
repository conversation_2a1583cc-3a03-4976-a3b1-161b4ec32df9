---
title: Hub Visibility
description: Learn how to manage whether your hub is public or private.
---

import { Callout } from 'fumadocs-ui/components/callout';
import { Steps } from 'fumadocs-ui/components/steps';

## Understanding Hub Visibility

By default, all newly created hubs are **private**. This means they are not visible in the hub browser and can only be joined through [invitations](/docs/features/hub-invites).

- **Private Hubs**: Only accessible via [invitations](/docs/features/hub-invites), not listed in the [hub discovery](/hubs)
- **Public Hubs**: Visible to everyone in the [hub discovery](/hubs), can be joined without invitations

## Publishing Your Hub

### Requirements for Publishing

Before you can make your hub public, it must meet all of the following requirements:

<Steps>
  1. **Hub Age**: Your hub must be at least 24 hours old
  2. **Moderation Team**: Your hub must have at least 2 moderators (including yourself)
  3. **Report Logging**: You must set up a log channel for user reports
</Steps>

<Callout type="warning">
  All requirements must be met before you can publish your hub. If any requirement fails, you'll need to address it before trying again.
</Callout>

### Managing Visibility

To change your hub's visibility, use:
```
/hub visibility hub:YourHubName visibility:public
```

To make it private again:
```
/hub visibility hub:YourHubName visibility:private
```

## Best Practices

1. **Start Private**: Keep your hub private while setting up rules and moderation
2. **Regular Moderation**: Ensure active moderators before making your hub public
3. **Review Settings**: Before publishing, review all hub settings

<Callout type="important" emoji="⚖️">
  All hubs must comply with InterChat's Terms of Service and Community Guidelines.
</Callout>
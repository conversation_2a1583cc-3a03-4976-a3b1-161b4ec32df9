import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { z } from "zod";
import { getUserHubPermission } from "@/lib/permissions";
import { PermissionLevel } from "@/lib/constants";

// Schema for updating hub settings
const updateHubSettingsSchema = z.object({
  settings: z.number().int().nonnegative(),
});

export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ hubId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Check if the user has permission to edit this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get the hub
    const hub = await prisma.hub.findUnique({
      where: { id: hubId },
      
    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = updateHubSettingsSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 },
      );
    }

    const { settings } = validation.data;

    // Update the hub settings
    const updatedHub = await prisma.hub.update({
      where: { id: hubId },
      data: {
        settings,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      hub: updatedHub,
      message: "Hub settings updated successfully",
    });
  } catch (error) {
    console.error("Error updating hub settings:", error);
    return NextResponse.json(
      { error: "Failed to update hub settings" },
      { status: 500 },
    );
  }
}

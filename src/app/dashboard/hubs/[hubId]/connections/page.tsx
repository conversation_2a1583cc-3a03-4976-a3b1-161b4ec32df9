import { auth } from "@/auth";
import { ClientConnectionsList } from "./client";
import { Button } from "@/components/ui/button";
import { HubNavigationTabs } from "@/components/dashboard/hubs/hub-navigation-tabs";
import { HydrationBoundaryProvider } from "@/components/providers/hydration-boundary";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { ArrowLeft, Plus } from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { createDehydratedState } from "@/lib/create-dehydrated-state";
// We'll use the same query key format as in the hook, but define it directly here

interface HubConnectionsPageProps {
  params: Promise<{
    hubId: string;
  }>;
}

export default async function HubConnectionsPage({
  params,
}: HubConnectionsPageProps) {
  const hubId  = (await params).hubId;
  const session = await auth();

  if (!session?.user) {
    return notFound();
  }

  // Create dehydrated state for React Query
  const dehydratedState = await createDehydratedState(async (queryClient) => {
    // Prefetch connections for this hub
    await queryClient.prefetchQuery({
      queryKey: ["connections", hubId],
      queryFn: async () => {
        const connections = await prisma.connection.findMany({
          where: { hubId },
          select: {
            id: true,
            serverId: true,
            channelId: true,
            connected: true,
            compact: true,
            createdAt: true,
            lastActive: true,
            joinRequestsDisabled: true,
            invite: true,
            hubId: true,
            server: {
              select: {
                id: true,
                name: true,
                iconUrl: true,
              },
            },
          },
          orderBy: { lastActive: "desc" },
        });
        return connections;
      },
    });
  });

  // Get user's permission level for this hub
  const permissionLevel = await getUserHubPermission(session.user.id, hubId);
  const canModerate = permissionLevel >= PermissionLevel.MODERATOR;
  const canEdit = permissionLevel >= PermissionLevel.MANAGER;

  // Get hub details
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    select: {
      id: true,
      name: true,
      description: true,
      private: true,
      ownerId: true,
    },
  });

  if (!hub) {
    return notFound();
  }

  // Only allow users with at least moderator permissions
  if (permissionLevel < PermissionLevel.MODERATOR) {
    return notFound();
  }

  // Get connections for this hub
  const connections = await prisma.connection.findMany({
    where: { hubId },
    select: {
      id: true,
      serverId: true,
      channelId: true,
      connected: true,
      compact: true,
      createdAt: true,
      lastActive: true,
      joinRequestsDisabled: true,
      invite: true,
      hubId: true,
      server: {
        select: {
          id: true,
          name: true,
          iconUrl: true,
        },
      },
    },
    orderBy: { lastActive: "desc" },
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href={`/dashboard/hubs/${hubId}`}>
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">
            {hub.name} Connections
          </h1>
        </div>
        <div className="flex items-center gap-2 mt-4 sm:mt-0 w-full sm:w-auto">
          {canEdit && (
            <Button
              asChild
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none flex-1 sm:flex-initial"
            >
              <Link href={`/dashboard/servers?hubId=${hubId}`}>
                <Plus className="h-4 w-4 mr-2" />
                Connect Server
              </Link>
            </Button>
          )}
        </div>
      </div>
      <HubNavigationTabs
        hubId={hubId}
        currentTab="connections"
        canModerate={canModerate}
        canEdit={canEdit}
      />
      {/* Use the ClientConnectionsList component with the fetched data */}
      <HydrationBoundaryProvider state={dehydratedState}>
        <ClientConnectionsList
          initialConnections={connections}
          hubId={hubId}
          canManage={canEdit}
        />
      </HydrationBoundaryProvider>
    </div>
  );
}

"use client";

import { HydrationBoundary } from "@/lib/tanstack-query";
import { ReactNode } from "react";

/**
 * HydrationBoundaryProvider component that wraps children with a HydrationBoundary
 * to handle server-side rendering of React Query data.
 */
export function HydrationBoundaryProvider({
  children,
  state,
}: {
  children: ReactNode;
  state?: unknown;
}) {
  return <HydrationBoundary state={state}>{children}</HydrationBoundary>;
}

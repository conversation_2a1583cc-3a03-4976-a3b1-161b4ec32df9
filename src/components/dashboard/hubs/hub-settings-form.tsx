"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import { HubSettingsBits, HubSettingsDescriptions } from "@/lib/constants";
import { Save, Check } from "lucide-react";

interface HubSettingsFormProps {
  hubId: string;
  initialSettings: number;
}

export function HubSettingsForm({
  hubId,
  initialSettings,
}: HubSettingsFormProps) {
  const { toast } = useToast();
  const [settings, setSettings] = useState<number>(initialSettings);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Check if a specific setting bit is enabled
  const isSettingEnabled = (bit: number) => (settings & bit) !== 0;

  // Toggle a specific setting bit
  const toggleSetting = (bit: number) => {
    const newSettings = isSettingEnabled(bit)
      ? settings & ~bit // Disable the bit
      : settings | bit; // Enable the bit

    setSettings(newSettings);
    setHasChanges(newSettings !== initialSettings);
  };

  const handleSaveSettings = async () => {
    if (!hasChanges) return;

    setIsSaving(true);
    try {
      const response = await fetch(`/api/hubs/${hubId}/settings`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          settings,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to save settings");
      }

      toast({
        title: "Settings saved",
        description: "Hub settings have been updated successfully.",
      });

      // Update initial values to reflect the new state
      setHasChanges(false);
    } catch (error) {
      console.error("Error saving settings:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to save settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        {Object.entries(HubSettingsBits).map(([name, bit]) => (
          <div key={name} className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor={`setting-${name}`}>{name}</Label>
              <p className="text-sm text-gray-400">
                {HubSettingsDescriptions[bit]}
              </p>
            </div>
            <Switch
              id={`setting-${name}`}
              checked={isSettingEnabled(bit)}
              onCheckedChange={() => toggleSetting(bit)}
              className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-gray-500"
            />
          </div>
        ))}
      </div>

      <Button
        className="w-full mt-6"
        onClick={handleSaveSettings}
        disabled={!hasChanges || isSaving}
      >
        {isSaving ? (
          <>Saving...</>
        ) : (
          <>
            {hasChanges ? (
              <Save className="h-4 w-4 mr-2" />
            ) : (
              <Check className="h-4 w-4 mr-2" />
            )}
            {hasChanges ? "Save Changes" : "Saved"}
          </>
        )}
      </Button>
    </div>
  );
}

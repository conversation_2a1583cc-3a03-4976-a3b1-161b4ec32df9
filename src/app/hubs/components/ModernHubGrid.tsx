"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";
import { ArrowR<PERSON>, ChevronLeft, ChevronRight, Clock, Flame, Heart, Sparkles, ThumbsUp } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useRef, useState, useTransition } from "react";
import { SortOptions } from "../constants";
import { ModernHubCard } from "./ModernHubCard";
import Image from "next/image";

interface ModernHubGridProps {
  mostRecentPopular: { hubs: SimplifiedHub[]; totalCount: number };
  mostUpvotedNew: { hubs: SimplifiedHub[]; totalCount: number };
  recentHubs: SimplifiedHub[];
  trendingHubs: { hubs: SimplifiedHub[]; totalCount: number };
}

// Section header component with improved styling
function SectionHeader({
  title,
  icon: Icon,
  sort,
  color = "from-purple-400 to-pink-300"
}: {
  title: string;
  icon: React.ElementType;
  sort: SortOptions;
  color?: string;
}) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const handleViewAll = () => {
    const params = new URLSearchParams();
    params.set("sort", sort);
    startTransition(() => {
      router.push(`/hubs/search?${params.toString()}`);
    });
  };

  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-3">
      <div className="flex items-center gap-2">
        <div className="p-2 rounded-lg bg-gray-800/50 border border-gray-700/50">
          <Icon className="h-5 w-5 text-primary" />
        </div>
        <h2 className={`text-xl font-bold bg-gradient-to-r ${color} bg-clip-text text-transparent`}>
          {title}
        </h2>
      </div>
      <Button
        variant="outline"
        size="sm"
        onClick={handleViewAll}
        disabled={isPending}
        className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50 hover:text-white hover:border-gray-600 group"
      >
        {isPending ? "Loading..." : (
          <>
            View All
            <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-0.5 transition-transform" />
          </>
        )}
      </Button>
    </div>
  );
}

// Featured hub card for the top section
function FeaturedHubCard({ hub }: { hub: SimplifiedHub }) {
  return (
    <Link
      href={`/hubs/${hub.id}`}
      className="block group relative overflow-hidden rounded-xl border border-gray-700/50 bg-gray-900 transition-all duration-300 hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-1 hover:border-gray-600/70 h-full"
    >
      <div className="p-4 flex flex-col h-full">
        <div className="flex items-center gap-3 mb-3">
          <div className="relative h-12 w-12 rounded-full border-2 border-gray-700/50 overflow-hidden flex-shrink-0">
            {hub.iconUrl ? (
              <Image
                src={hub.iconUrl}
                alt={`${hub.name} icon`}
                width={48}
                height={48}
                className="object-cover w-full h-full"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-primary/20 text-primary font-bold text-lg">
                {hub.name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
          <div className="min-w-0 flex-1">
            <h3 className="font-bold text-white group-hover:text-primary transition-colors duration-300 line-clamp-1">
              {hub.name}
            </h3>
            <div className="flex items-center gap-2 text-xs text-gray-400">
              <span className="flex items-center gap-1">
                <Heart className="h-3 w-3 text-pink-400" />
                {hub.upvotes?.length || 0}
              </span>
              <span className="flex items-center gap-1">
                <Flame className="h-3 w-3 text-orange-400" />
                {hub.connections?.length || 0} servers
              </span>
            </div>
          </div>
        </div>

        {/* Description with fixed height */}
        <div className="h-12 mb-3">
          <p className="text-sm text-gray-300 line-clamp-2">
            {hub.description}
          </p>
        </div>

        {/* Spacer */}
        <div className="flex-grow" />

        <div className="flex flex-wrap gap-1">
          {hub.tags?.slice(0, 3).map((tag) => (
            <span
              key={typeof tag === 'string' ? tag : tag.name}
              className="px-2 py-0.5 text-xs rounded-full bg-gray-700/50 text-gray-300 border border-gray-700/50"
            >
              {typeof tag === 'string' ? tag : tag.name}
            </span>
          ))}
        </div>
      </div>
    </Link>
  );
}

// Scrollable hub section component
function ScrollableHubSection({
  title,
  icon: Icon,
  sort,
  color,
  hubs,
}: {
  title: string;
  icon: React.ElementType;
  sort: SortOptions;
  color: string;
  hubs: SimplifiedHub[];
}) {
  const scrollContainerRef = useRef<HTMLUListElement>(null);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [maxScroll, setMaxScroll] = useState(0);
  const [showControls, setShowControls] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Set isClient to true on mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Calculate if we need to show scroll controls
  useEffect(() => {
    if (!isClient) return;

    const checkOverflow = () => {
      if (scrollContainerRef.current) {
        const container = scrollContainerRef.current;
        const hasOverflow = container.scrollWidth > container.clientWidth;
        setShowControls(hasOverflow);
        setMaxScroll(container.scrollWidth - container.clientWidth);
      }
    };

    // Check initially
    checkOverflow();

    // Add resize listener to recalculate on window resize
    window.addEventListener('resize', checkOverflow);

    // Cleanup
    return () => window.removeEventListener('resize', checkOverflow);
  }, [isClient]);

  // Update scroll position when scrolling manually
  const handleScroll = () => {
    if (scrollContainerRef.current) {
      setScrollPosition(scrollContainerRef.current.scrollLeft);
    }
  };

  // Scroll left
  const scrollLeft = useCallback(() => {
    if (scrollContainerRef.current) {
      const newPosition = Math.max(0, scrollPosition - scrollContainerRef.current.clientWidth / 2);
      scrollContainerRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth'
      });
      setScrollPosition(newPosition);
    }
  }, [scrollPosition]);

  // Scroll right
  const scrollRight = useCallback(() => {
    if (scrollContainerRef.current) {
      const newPosition = Math.min(
        maxScroll,
        scrollPosition + scrollContainerRef.current.clientWidth / 2
      );
      scrollContainerRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth'
      });
      setScrollPosition(newPosition);
    }
  }, [scrollPosition, maxScroll]);

  // Add keyboard navigation to scroll buttons
  useEffect(() => {
    if (!isClient) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle keyboard navigation when this section is in focus
      if (document.activeElement?.closest(`[aria-label="${title} scrollable section"]`)) {
        if (e.key === 'ArrowLeft') {
          scrollLeft();
          e.preventDefault();
        } else if (e.key === 'ArrowRight') {
          scrollRight();
          e.preventDefault();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isClient, title, scrollLeft, scrollRight]);

  return (
    <div className="space-y-4">
      <SectionHeader
        title={title}
        icon={Icon}
        sort={sort}
        color={color}
      />

      <section
        className="relative focus-within:outline-none focus-within:ring-2 focus-within:ring-primary/30 rounded-xl"
        aria-label={`${title} scrollable section`}
      >
        {/* Left scroll button - only rendered on client */}
        {isClient && showControls && scrollPosition > 0 && (
          <Button
            variant="outline"
            size="icon"
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 h-10 w-10 rounded-full bg-gray-900/80 border-gray-700 text-white shadow-lg transform -translate-x-1/2 hover:bg-gray-800 hover:border-gray-600 focus:ring-primary"
            onClick={scrollLeft}
            aria-label={`Scroll ${title} left`}
          >
            <ChevronLeft className="h-5 w-5" />
            <span className="sr-only">Scroll left</span>
          </Button>
        )}

        {/* Scrollable container */}
        <ul
          ref={scrollContainerRef}
          className="flex overflow-x-auto pb-4 no-scrollbar snap-x snap-mandatory focus:outline-none focus:ring-2 focus:ring-primary/30 rounded-xl list-none m-0 p-0"
          onScroll={handleScroll}
          aria-label={`Scrollable list of ${title}`}
        >
          {hubs.map((hub) => (
            <li
              key={hub.id}
              className="flex-none w-full sm:w-1/2 lg:w-1/4 px-2 first:pl-0 last:pr-0 snap-start h-[220px]"
            >
              <ModernHubCard hub={hub} />
            </li>
          ))}
        </ul>

        {/* Right scroll button - only rendered on client */}
        {isClient && showControls && scrollPosition < maxScroll && (
          <Button
            variant="outline"
            size="icon"
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 h-10 w-10 rounded-full bg-gray-900/80 border-gray-700 text-white shadow-lg transform translate-x-1/2 hover:bg-gray-800 hover:border-gray-600 focus:ring-primary"
            onClick={scrollRight}
            aria-label={`Scroll ${title} right`}
          >
            <ChevronRight className="h-5 w-5" />
            <span className="sr-only">Scroll right</span>
          </Button>
        )}
      </section>
    </div>
  );
}

// Featured hub section with grid layout
function FeaturedHubSection({
  title,
  icon: Icon,
  sort,
  color,
  hubs,
}: {
  title: string;
  icon: React.ElementType;
  sort: SortOptions;
  color: string;
  hubs: SimplifiedHub[];
}) {
  // No client-side state needed for this component

  return (
    <div className="space-y-4">
      <SectionHeader
        title={title}
        icon={Icon}
        sort={sort}
        color={color}
      />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 h-[250px]">
        {hubs.slice(0, 3).map((hub) => (
          <FeaturedHubCard key={hub.id} hub={hub} />
        ))}
      </div>
    </div>
  );
}

export function ModernHubGrid({
  mostRecentPopular,
  mostUpvotedNew,
  recentHubs,
  trendingHubs,
}: ModernHubGridProps) {
  // Define sections with their icons and colors
  const sections = [
    {
      id: "trending",
      title: "Trending Hubs",
      icon: Flame,
      sort: SortOptions.Trending,
      data: trendingHubs.hubs,
      color: "from-orange-400 to-red-300",
    },
    {
      id: "recentPopular",
      title: "Most Popular Hubs",
      icon: Sparkles,
      sort: SortOptions.MostRecentPopular,
      data: mostRecentPopular.hubs,
      color: "from-purple-400 to-pink-300",
    },
    {
      id: "upvotedNew",
      title: "Most Upvoted New Hubs",
      icon: ThumbsUp,
      sort: SortOptions.MostUpvotedNew,
      data: mostUpvotedNew.hubs,
      color: "from-blue-400 to-indigo-300",
    },
    {
      id: "recent",
      title: "Recently Created Hubs",
      icon: Clock,
      sort: SortOptions.Created,
      data: recentHubs,
      color: "from-green-400 to-emerald-300",
    },
  ];

  return (
    <div className="space-y-12">
      {/* Featured hubs section */}
      <FeaturedHubSection
        title="Featured Hubs"
        icon={Heart}
        sort={SortOptions.MostRecentPopular}
        color="from-pink-400 to-rose-300"
        hubs={mostRecentPopular.hubs}
      />

      {/* Scrollable hub sections */}
      {sections.map((section) => (
        <ScrollableHubSection
          key={section.id}
          title={section.title}
          icon={section.icon}
          sort={section.sort}
          color={section.color}
          hubs={section.data}
        />
      ))}
    </div>
  );
}

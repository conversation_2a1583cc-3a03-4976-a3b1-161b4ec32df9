"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { GridPattern } from "@/components/magicui/grid-pattern";

export default function HubContentLoading() {
  return (
    <div className="relative bg-gradient-to-b from-gray-950 to-gray-900 pb-20">
      {/* Background pattern */}
      <div className="absolute inset-0 overflow-hidden">
        <GridPattern
          width={40}
          height={40}
          x={-1}
          y={-1}
          className="absolute inset-0 h-full w-full stroke-gray-800/20 [mask-image:radial-gradient(100%_100%_at_top_center,white,transparent)]"
          strokeDasharray="0 4 8"
        />
      </div>

      {/* Featured Hubs Carousel Skeleton */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-6 w-24" />
        </div>

        <div className="relative overflow-hidden rounded-xl border border-gray-700/50 bg-gray-900 p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Featured hub skeletons */}
            {Array(3).fill(0).map((_, i) => (
              <div key={i} className="flex flex-col gap-4">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-5 w-32 mb-2" />
                    <div className="flex gap-2">
                      <Skeleton className="h-3 w-16" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                </div>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-5/6" />
                <div className="flex gap-2 mt-auto">
                  <Skeleton className="h-5 w-16 rounded-full" />
                  <Skeleton className="h-5 w-16 rounded-full" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Filter Bar Skeleton */}
      <div className="container mx-auto px-4 py-6">
        <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-xl p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-grow flex gap-2">
              <Skeleton className="h-10 flex-grow rounded-md" />
              <Skeleton className="h-10 w-24 rounded-md" />
            </div>
            <div className="flex gap-2">
              <Skeleton className="h-10 w-32 rounded-md" />
              <Skeleton className="h-10 w-32 rounded-md" />
            </div>
          </div>
          <div className="mt-4 flex flex-wrap gap-2">
            <Skeleton className="h-7 w-16 rounded-full" />
            <Skeleton className="h-7 w-20 rounded-full" />
            <Skeleton className="h-7 w-24 rounded-full" />
            <Skeleton className="h-7 w-16 rounded-full" />
            <Skeleton className="h-7 w-20 rounded-full" />
          </div>
        </div>
      </div>

      {/* Hub Sections Skeleton */}
      <div className="container mx-auto px-4">
        <div className="space-y-12">
          {/* Featured section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Skeleton className="h-10 w-10 rounded-lg" />
              <Skeleton className="h-6 w-40" />
              <div className="flex-grow" />
              <Skeleton className="h-8 w-24 rounded-md" />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Array(3).fill(0).map((_, i) => (
                <div key={i} className="rounded-xl border border-gray-700/50 bg-gray-900 p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div>
                      <Skeleton className="h-5 w-32 mb-1" />
                      <div className="flex gap-2">
                        <Skeleton className="h-3 w-16" />
                        <Skeleton className="h-3 w-16" />
                      </div>
                    </div>
                  </div>
                  <Skeleton className="h-4 w-full mb-1" />
                  <Skeleton className="h-4 w-5/6 mb-3" />
                  <div className="flex gap-2">
                    <Skeleton className="h-5 w-16 rounded-full" />
                    <Skeleton className="h-5 w-16 rounded-full" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Hub category sections */}
          {Array(4).fill(0).map((_, sectionIndex) => (
            <div key={sectionIndex} className="space-y-4">
              <div className="flex items-center gap-2">
                <Skeleton className="h-10 w-10 rounded-lg" />
                <Skeleton className="h-6 w-40" />
                <div className="flex-grow" />
                <Skeleton className="h-8 w-24 rounded-md" />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {Array(4).fill(0).map((_, i) => (
                  <div key={i} className="rounded-xl border border-gray-700/50 bg-gray-900 p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center gap-3">
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div>
                          <Skeleton className="h-5 w-24 mb-1" />
                          <div className="flex gap-2">
                            <Skeleton className="h-3 w-12" />
                            <Skeleton className="h-3 w-12" />
                          </div>
                        </div>
                      </div>
                      <Skeleton className="h-7 w-16 rounded-full" />
                    </div>
                    <Skeleton className="h-3 w-full mb-1 mt-2" />
                    <Skeleton className="h-3 w-5/6 mb-3" />
                    <div className="flex gap-2 mb-3">
                      <Skeleton className="h-5 w-12 rounded-full" />
                      <Skeleton className="h-5 w-12 rounded-full" />
                    </div>
                    <Skeleton className="h-8 w-full rounded-md" />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

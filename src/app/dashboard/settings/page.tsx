import { auth } from "@/auth";
import { UserSettingsForm } from "@/components/dashboard/settings/user-settings-form";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { TabsContent } from "@/components/ui/tabs";
import { UnderlinedTabs } from "@/components/dashboard/underlined-tabs";
import prisma from "@/lib/prisma";
import { Bell } from "lucide-react";
import { Metadata } from "next";
import Image from "next/image";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Settings | InterChat Dashboard",
  description: "Manage your InterChat settings and preferences",
};

export default async function SettingsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard/settings");
  }

  // Fetch user data from database to get current settings
  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: {
      mentionOnReply: true,
      locale: true,
    },
    
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <h1 className="text-2xl font-bold tracking-tight">Settings</h1>
      </div>
      <p className="text-gray-400 -mt-4">
        Manage your account settings and preferences
      </p>

      <UnderlinedTabs
        defaultValue="account"
        className="space-y-6"
        tabs={[
          {
            value: "account",
            label: "Account",
            color: "indigo",
          },
          {
            value: "notifications",
            label: "Notifications",
            color: "blue",
          },
        ]}
      >
        <TabsContent value="account" className="space-y-6">
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-xl font-semibold">
                Profile Information
              </CardTitle>
              <CardDescription className="text-gray-400">
                Your account information from Discord
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <Image
                    src={
                      session.user.image ||
                      "https://api.dicebear.com/7.x/shapes/svg?seed=user"
                    }
                    alt={session.user.name || "User"}
                    width={80}
                    height={80}
                    className="rounded-full border-2 border-indigo-500/30"
                  />
                  <div className="absolute bottom-0 right-0 h-4 w-4 rounded-full bg-green-500 border-2 border-gray-900"></div>
                </div>
                <div>
                  <h3 className="text-lg font-medium">{session.user.name}</h3>
                  <p className="text-xs text-gray-500 mt-1">
                    User ID: {session.user.id}
                  </p>
                </div>
              </div>
              <div className="pt-4">
                <p className="text-sm text-gray-400">
                  Your profile information is managed through Discord. To update
                  your profile picture or username, please do so through
                  Discord.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-xl font-semibold">
                Account Preferences
              </CardTitle>
              <CardDescription className="text-gray-400">
                Manage your account settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserSettingsForm
                userId={session.user.id}
                initialMentionOnReply={user?.mentionOnReply ?? true}
                initialLocale={user?.locale ?? null}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-xl font-semibold">
                Notification Settings
              </CardTitle>
              <CardDescription className="text-gray-400">
                Manage how and when you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="hub-notifications">Hub Notifications</Label>
                  <p className="text-sm text-gray-400">
                    Receive notifications about hub activity
                  </p>
                </div>
                <Switch
                  id="hub-notifications"
                  defaultChecked={true}
                  className="data-[state=checked]:bg-blue-600"
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="report-notifications">
                    Report Notifications
                  </Label>
                  <p className="text-sm text-gray-400">
                    Receive notifications when new reports are created
                  </p>
                </div>
                <Switch
                  id="report-notifications"
                  defaultChecked={true}
                  className="data-[state=checked]:bg-indigo-600"
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="connection-notifications">
                    Connection Notifications
                  </Label>
                  <p className="text-sm text-gray-400">
                    Receive notifications about new connections
                  </p>
                </div>
                <Switch
                  id="connection-notifications"
                  defaultChecked={true}
                  className="data-[state=checked]:bg-purple-600"
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="update-notifications">
                    Update Notifications
                  </Label>
                  <p className="text-sm text-gray-400">
                    Receive notifications about InterChat updates
                  </p>
                </div>
                <Switch
                  id="update-notifications"
                  defaultChecked={true}
                  className="data-[state=checked]:bg-green-600"
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full bg-gradient-to-r from-purple-600 to-primary hover:from-purple-600/80 hover:to-primary/80 border-none">
                <Bell className="h-4 w-4 mr-2" />
                Save Notification Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </UnderlinedTabs>
    </div>
  );
}

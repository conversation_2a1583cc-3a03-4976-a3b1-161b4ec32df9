import { FeaturedHubs } from "./FeaturedHubs";
import prisma from "@/lib/prisma";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";

export async function FeaturedHubsContainer() {
  // Fetch the top 5 trending hubs to feature
  const featuredHubs = await getFeaturedHubs();

  return <FeaturedHubs hubs={featuredHubs} />;
}

async function getFeaturedHubs(): Promise<SimplifiedHub[]> {
  try {
    // Get the top 5 hubs with the most connections and good activity
    const hubs = await prisma.hub.findMany({
      where: {
        private: false,
      },
      orderBy: [
        { connections: { _count: "desc" } },
        { upvotes: { _count: "desc" } },
      ],
      take: 5,
      include: {
        tags: { select: { name: true } },
        connections: {
          where: { connected: true },
          select: { id: true, serverId: true, lastActive: true },
        },
        reviews: true,
        moderators: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
        upvotes: true,
      },
    });

    // Transform to SimplifiedHub format with type assertion to handle nullable fields
    return hubs.map((hub) => ({
      id: hub.id,
      lastActive: hub.connections[0]?.lastActive || null,
      appealCooldownHours: hub.appealCooldownHours,
      locked: hub.locked,
      ownerId: hub.ownerId,
      private: hub.private,
      rules: hub.rules,
      settings: hub.settings,
      welcomeMessage: hub.welcomeMessage,
      name: hub.name,
      description: hub.description,
      iconUrl: hub.iconUrl,
      bannerUrl: hub.bannerUrl,
      // TODO: Add these fields to the database
      nsfw: false,
      verified: false,
      partnered: false,
      language: 'en',

      tags: hub.tags,
      connections: hub.connections,
      reviews: hub.reviews,
      moderators: hub.moderators,
      upvotes: hub.upvotes,
      createdAt: hub.createdAt,
      updatedAt: hub.updatedAt,
    })) as SimplifiedHub[];
  } catch (error) {
    console.error("Error fetching featured hubs:", error);
    return [];
  }
}

import { Skeleton } from "@/components/ui/skeleton";
import { Globe, Search, Filter, ArrowDownAZ } from "lucide-react";

export default function LoadingSearchPage() {
  return (
    <>
      {/* Search Header Skeleton */}
      <div className="relative">
        <div className="relative w-full border-b border-gray-800/30 backdrop-blur-sm bg-gray-900/10">
          <div className="container mx-auto px-4 py-8">
            <div className="flex flex-col items-center justify-center mb-6">
              <div className="flex items-center justify-center mb-4">
                <Globe className="w-8 h-8 text-primary mr-3" />
                <div className="h-8 w-48 bg-gray-800 rounded animate-pulse" />
              </div>
              <div className="h-4 w-96 bg-gray-800 rounded animate-pulse mt-2" />
            </div>

            <div className="w-full max-w-3xl mx-auto">
              <div className="flex flex-col md:flex-row gap-3">
                <div className="relative flex-grow">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-600" />
                  <div className="h-12 w-full bg-gray-800 rounded-lg animate-pulse" />
                </div>

                <div className="flex gap-2">
                  <div className="h-12 w-24 bg-gray-800 rounded-lg animate-pulse flex items-center justify-center">
                    <Filter className="w-5 h-5 text-gray-600" />
                  </div>
                  <div className="h-12 w-32 bg-gray-800 rounded-lg animate-pulse flex items-center justify-center">
                    <ArrowDownAZ className="w-5 h-5 text-gray-600" />
                  </div>
                  <div className="h-12 w-24 bg-gray-800 rounded-lg animate-pulse" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Skeleton */}
        <div className="container mx-auto px-4 py-8">
          <div className="space-y-6">
            {/* Grid skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-4">
              {Array(9)
                .fill(null)
                .map((_, i) => (
                  <div
                    key={i}
                    className="bg-gray-800/50 border border-gray-700/50 rounded-xl p-4 space-y-4 animate-pulse overflow-hidden"
                  >
                    <Skeleton className="h-40 w-full rounded-lg" />
                    <Skeleton className="h-6 w-3/4 rounded-md" />
                    <Skeleton className="h-4 w-1/2 rounded-md" />
                    <div className="flex justify-between">
                      <Skeleton className="h-8 w-20 rounded-full" />
                      <Skeleton className="h-8 w-24 rounded-full" />
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

import { auth } from "@/auth";
import { REST } from "@discordjs/rest";
import { type APIRole, Routes } from "discord-api-types/v10";
import { type NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ serverId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { serverId } = await props.params;

    // Validate that the serverId is a valid Discord ID format (numeric)
    if (!/^\d+$/.test(serverId)) {
      return NextResponse.json(
        { error: "Invalid Discord server ID format" },
        { status: 400 }
      );
    }

    // Get bot token from environment variable
    const botToken = process.env.DISCORD_BOT_TOKEN;
    if (!botToken) {
      return NextResponse.json(
        { error: "Bot token not configured" },
        { status: 500 }
      );
    }

    // Create REST instance
    const rest = new REST({ version: "10" }).setToken(botToken);

    // Fetch roles from Discord API
    const discordRoles = await rest.get(Routes.guildRoles(serverId)) as APIRole[];

    // Process and filter roles
    const processedRoles = discordRoles
      // Filter out the @everyone role and bot roles if needed
      .filter((role) => role.name !== "@everyone")
      // Map to a simpler structure
      .map((role) => ({
        id: role.id,
        name: role.name,
        color: role.color,
        position: role.position,
        mentionable: role.mentionable,
      }))
      // Sort by position (higher position roles first)
      .sort((a, b) => b.position - a.position);

    return NextResponse.json({ roles: processedRoles });
  } catch (error) {
    console.error("Error fetching Discord roles:", error);
    return NextResponse.json(
      { error: "Failed to fetch Discord roles" },
      { status: 500 }
    );
  }
}

import { DocsLayout } from "fumadocs-ui/layouts/docs";
import type { ReactNode } from "react";
import { source } from "@/lib/source";
import { RootProvider } from "fumadocs-ui/provider";
import { BaseLayoutProps } from "fumadocs-ui/layouts/shared";
import Image from "next/image";

export const metadata = {
  title: "InterChat Docs",
  description: "InterChat documentation",
  openGraph: {
    title: "InterChat Docs",
    description: "InterChat documentation",
    type: "website",
    images: [
      {
        url: "/InterChatLogo.png",
        width: 300,
        height: 300,
        alt: "InterChat Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "InterChat Docs",
    description: "InterChat documentation",
    images: ["/InterChatLogo.png"],
  },
};

const baseOptions: BaseLayoutProps = {
  themeSwitch: {
    enabled: false,
  },
  nav: {
    title: (
      <>
        {<Image alt="InterChat Logo" src="/interchat.png" height={30} width={30} />}
        <span className="font-medium [.uwu_&]:hidden [header_&]:text-[15px]">
          InterChat
        </span>
      </>
    ),
  },
  githubUrl: "https://github.com/interchatapp/InterChat",
};

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <RootProvider>
      <div className="relative min-h-screen">
        {/* Gradient overlays */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-900/10 to-purple-900/20" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-purple-900/20 via-transparent to-transparent" />

        {/* Content */}
        <div className="relative">
          <DocsLayout tree={source.pageTree} {...baseOptions}>
            {children}
          </DocsLayout>
        </div>
      </div>
    </RootProvider>
  );
}

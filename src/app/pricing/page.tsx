"use client";

import {
  FeatureComparisonTable,
  PricingCardsSection,
  PricingCTA,
  PricingFAQ,
  PricingSchema
} from "@/components/pricing";
import { Sparkles } from "lucide-react";

export default function PricingPage() {
  // const [billingPeriod, setBillingPeriod] = useState<"monthly" | "yearly">(
    // "monthly"
  // );

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-gray-950">
      {/* Schema.org markup for SEO */}
      <PricingSchema />

      {/* Hero Section */}
      <section className="pt-24 pb-16 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl opacity-50" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-primary-alt/10 rounded-full blur-3xl opacity-50" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-3xl mx-auto">
            <div className="inline-block mb-4">
              <div className="flex items-center justify-center bg-white/10 text-white px-6 py-2 rounded-full border border-white/20 backdrop-blur-md shadow-lg">
                <Sparkles className="w-5 h-5 mr-2 text-yellow-400" />
                <span className="font-medium">Choose Your Perfect Plan</span>
              </div>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white leading-tight tracking-tight">
              Simple, Transparent{" "}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-alt to-primary">
                Pricing
              </span>
            </h1>

            <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
              Whether you&apos;re running a small community or managing multiple large
              Discord servers, we have a plan that fits your needs.
            </p>
          </div>
        </div>
      </section>

      {/* Pricing Cards */}
      <PricingCardsSection />

      {/* Feature Comparison Table */}
      <FeatureComparisonTable />

      {/* FAQ Section */}
      <PricingFAQ />

      {/* CTA Section */}
      <PricingCTA />
    </div>
  );
}

"use client";

import { useQuery } from "@/lib/tanstack-query";
import { fetchApi } from "@/lib/api";
import { useErrorNotification } from "./use-error-notification";
import { User } from "./use-hub-members";

// Query keys
export const userKeys = {
  all: ["users"] as const,
  search: (query: string) => [...userKeys.all, "search", query] as const,
};

// Search users
export async function searchUsers(query: string): Promise<User[]> {
  if (!query) return [];
  
  const response = await fetchApi<{ users: User[] }>(`/api/users/search?query=${encodeURIComponent(query)}`);
  return response.users;
}

// Hook for searching users
export function useUserSearch(query: string, options?: { enabled?: boolean }) {
  const enabled = options?.enabled !== undefined ? options.enabled : !!query;
  
  const queryResult = useQuery({
    queryKey: userKeys.search(query),
    queryFn: () => searchUsers(query),
    enabled: enabled && query.length > 0,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Handle error notification
  useErrorNotification({
    isError: queryResult.isError,
    error: queryResult.error,
    title: "Error",
    description: "Failed to search users",
  });

  return queryResult;
}

// Filter users that are not already members
export function useFilteredUserSearch(
  query: string, 
  currentMembers?: { owner?: { id: string }; moderators?: { userId: string }[] },
  options?: { enabled?: boolean }
) {
  const { data: users = [], ...rest } = useUserSearch(query, options);
  
  // Filter out users who are already members
  const filteredUsers = users.filter((user) => {
    // Filter out the owner
    if (currentMembers?.owner?.id === user.id) return false;
    
    // Filter out existing moderators
    return !currentMembers?.moderators?.some((mod) => mod.userId === user.id);
  });
  
  return { data: filteredUsers, ...rest };
}

"use client";

import { Database, Home, Link2 } from "lucide-react";
import { ModernHero } from "../shared/modern-hero";

interface AnimatedServersHeroProps {
  totalServers: number;
  connectedServers: number;
  totalConnections: number;
}

export function AnimatedServersHero({
  totalServers,
  connectedServers,
  totalConnections,
}: AnimatedServersHeroProps) {
  return (
    <ModernHero
      title="Your Discord Servers"
      subtitle="Connect your Discord servers to InterChat hubs"
      stats={[
        {
          icon: <Home className="h-5 w-5 text-blue-400" />,
          value: totalServers,
          label: "Servers",
          color: "bg-none",
        },
        {
          icon: <Database className="h-5 w-5 text-indigo-400" />,
          value: connectedServers,
          label: "Connected",
          color: "bg-none",
        },
        {
          icon: <Link2 className="h-5 w-5 text-purple-400" />,
          value: totalConnections,
          label: "Connections",
          color: "bg-none",
        },
      ]}
      gradientColors={{
        from: "from-blue-900/30",
        via: "via-indigo-900/20",
        to: "to-purple-900/30",
      }}
      particleColors={[
        "bg-blue-500/20",
        "bg-indigo-500/20",
        "bg-purple-500/20",
      ]}
    />
  );
}

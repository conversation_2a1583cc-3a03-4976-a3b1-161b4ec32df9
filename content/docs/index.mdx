---
title: Welcome to InterChat
description: InterChat is a powerful Discord bot that enables cross-server communication, allowing communities to connect and interact in real-time.
icon: House
---

import { Cards, Card } from 'fumadocs-ui/components/card'
import { Steps } from 'fumadocs-ui/components/steps'
import { Callout } from 'fumadocs-ui/components/callout'
import { LinkIcon, HouseIcon, ShieldIcon, ZapIcon } from 'lucide-react'

<div className="flex justify-center mb-8">
  <img src="/InterChatLogo.png" alt="InterChat Logo" width="150" height="150" className="rounded-full" />
</div>

<Callout type="info">
  InterChat is constantly evolving with new features and improvements. Make sure to join our [support server](https://discord.gg/cgYgC6YZyX) to stay updated!
</Callout>

<Callout type="warning">
  InterChat is licensed under the GNU AGPL-3.0 license. Creating "cheap copies" by rebranding without proper attribution is strictly prohibited and will be pursued legally. See our [License Compliance Warning](/docs/LICENSE-DISCLAIMER) for details.
</Callout>

## Key Features

<Cards>
  <Card title="Cross-Server Chat" href="/docs/features/connections" icon={<LinkIcon className='text-primary' />}>
    Connect channels across different Discord servers for real-time messaging
  </Card>
  <Card title="Community Hubs" href="/docs/features/hubs" icon={<HouseIcon className='text-green-500' />}>
    Create topic-focused community hubs to bring servers together
  </Card>
  <Card title="Advanced Moderation" href="/docs/features/moderation" icon={<ShieldIcon className='text-red-500' />}>
    Comprehensive tools to keep your communities safe
  </Card>
  <Card title="Content Filtering" href="/docs/features/moderation" icon={<ZapIcon className='text-yellow-500' />}>
    Customizable content filtering with regex pattern matching
  </Card>
</Cards>

## Quick Start

<Steps>
  ### Add InterChat to your server
  Visit [interchat.tech/invite](https://interchat.tech/invite) to add the bot to your Discord server.

  ### Create or join a hub
  Use the `/hub browse` command to discover public hubs or create your own with `/hub create`.

  ### Connect your channel
  Use the `/connect` command in the channel you want to connect to a hub.

  ### Start chatting!
  That's it! You can now communicate with other servers in the hub.
</Steps>

## Need Help?

If you have any questions or need assistance, check out our [FAQ](/docs/faq) or join our [support server](https://discord.gg/cgYgC6YZyX).

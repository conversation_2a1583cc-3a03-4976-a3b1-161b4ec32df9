import React from "react";
import { <PERSON><PERSON><PERSON>, GlobeIcon, Spa<PERSON>les, Zap } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "./ui/button";

type StepType = {
  number: string;
  title: string;
  description: string | React.ReactNode;
  gradient: string;
  icon: React.ReactNode;
  link: string;
  isExternal: boolean;
};

const steps: StepType[] = [
  {
    number: "01",
    title: "Add InterChat to Discord",
    description:
      "Invite InterChat to your server with a single click. No complex setup required.",
    gradient: "from-emerald-500/20 to-teal-500/30",
    icon: <Sparkles className="w-6 h-6 text-emerald-300" />,
    link: "/invite",
    isExternal: true,
  },
  {
    number: "02",
    title: "Pick Your Hub",
    description: (
      <>
        Find a hub from the{" "}
        <Link
          href="/hubs"
          className="underline text-white hover:text-primary-alt transition-colors"
        >
          Hub Discovery
        </Link>{" "}
        and link a channel to a hub to get started.
      </>
    ),
    gradient: "from-indigo-500/20 to-violet-500/30",
    icon: <GlobeIcon className="w-6 h-6 text-indigo-300" />,
    link: "/hubs",
    isExternal: false,
  },
  {
    number: "03",
    title: "Start Global Conversations",
    description:
      "That's it! Your community can now engage with like-minded servers worldwide.",
    gradient: "from-rose-500/20 to-pink-500/30",
    icon: <Zap className="w-6 h-6 text-rose-300" />,
    link: "/dashboard",
    isExternal: false,
  },
];

export const CTASection = () => {
  return (
    <section className="relative overflow-hidden py-16 bg-gradient-to-br from-primary-alt/95 to-purple-900">
      {/* Simplified background - reduced number of blobs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-violet-500/20 rounded-full blur-3xl" />
      </div>

      {/* Simplified grid pattern overlay */}
      <div className="absolute inset-0 bg-grid-white/[0.05] bg-[size:64px_64px] opacity-20"></div>

      <div className="container mx-auto px-4 relative">
        <div className="text-center mb-8 relative z-10">
          <div className="inline-block mb-8">
            <div className="flex items-center justify-center bg-white/20 text-white px-6 py-3 rounded-full border border-white/20 shadow-lg">
              <Sparkles className="w-5 h-5 mr-3 text-yellow-400" />
              <span className="font-medium tracking-wide text-base">
                Get Started in Three Simple Steps
              </span>
            </div>
          </div>

          <h2 className="text-3xl md:text-5xl font-bold mb-4 text-white leading-tight tracking-tight">
            Ready to Connect Your{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-alt to-primary relative">
              Discord Servers?
            </span>
          </h2>

          <p className="text-base md:text-lg text-blue-100/90 mb-6 max-w-2xl mx-auto leading-relaxed">
            Join thousands of thriving Discord servers already using InterChat
            to build connections and grow their communities globally.
          </p>
        </div>

        <div className="text-center relative z-10">
          <div className="flex flex-col sm:flex-row gap-5 mb-8 justify-center">
            <a href="/invite" target="_blank" rel="noreferrer">
              <Button className="cursor-pointer bg-white text-blue-700 hover:bg-blue-50 transition-all duration-300 font-semibold text-base px-7 py-5 rounded-xl shadow-lg hover:shadow-xl border border-white/80 hover:scale-105">
                <Sparkles className="mr-2 h-5 w-5" />
                Get Started Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </a>
            <Link href="/hubs">
              <Button
                variant="outline"
                className="cursor-pointer bg-transparent border-2 border-white/70 text-white hover:bg-white/10 transition-all duration-300 font-semibold text-base px-7 py-5 rounded-xl hover:border-white hover:scale-105"
              >
                <GlobeIcon className="mr-2 h-5 w-5" />
                Browse Hubs
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-4 md:gap-6 relative z-10">
          {steps.map((step) => (
            <div
              key={step.number}
              className="group"
            >
              <div
                className={`rounded-2xl bg-gradient-to-br ${step.gradient} p-5 border border-white/10 shadow-xl transition-all duration-300 hover:shadow-2xl hover:-translate-y-1 h-full flex flex-col items-center`}
              >
                <div className="mb-1 text-lg font-bold text-white/80 tracking-wider">
                  {step.number}
                </div>
                {step.isExternal ? (
                  <a
                    href={step.link}
                    target="_blank"
                    rel="noreferrer"
                    className="group-hover:no-underline"
                  >
                    <div className="flex items-center justify-center w-12 h-12 bg-white/20 rounded-full mb-3 border border-white/20 shadow-md hover:shadow-white/10 transition-all duration-300 hover:scale-105">
                      {step.icon}
                    </div>
                  </a>
                ) : (
                  <Link href={step.link} className="group-hover:no-underline">
                    <div className="flex items-center justify-center w-12 h-12 bg-white/20 rounded-full mb-3 border border-white/20 shadow-md hover:shadow-white/10 transition-all duration-300 hover:scale-105">
                      {step.icon}
                    </div>
                  </Link>
                )}
                {step.isExternal ? (
                  <a
                    href={step.link}
                    target="_blank"
                    rel="noreferrer"
                    className="group-hover:no-underline"
                  >
                    <h3 className="text-xl font-bold text-white mb-2 hover:text-primary-alt transition-colors duration-200">
                      {step.title}
                    </h3>
                  </a>
                ) : (
                  <Link href={step.link} className="group-hover:no-underline">
                    <h3 className="text-xl font-bold text-white mb-2 hover:text-primary-alt transition-colors duration-200">
                      {step.title}
                    </h3>
                  </Link>
                )}
                <p className="text-blue-100/80 text-base leading-relaxed">
                  {step.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Added social proof */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center justify-center gap-2 bg-white/10 px-6 py-3 rounded-full border border-white/10">
            <span className="text-blue-100/70 text-sm">Trusted by</span>
            <span className="font-semibold text-white">10,000+</span>
            <span className="text-blue-100/70 text-sm">
              Discord servers
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

import { auth } from "@/auth";
import { AutoBreadcrumb } from "@/components/dashboard/breadcrumb";
import { DashboardSidebar } from "@/components/dashboard/sidebar";
import { DashboardTopBar } from "@/components/dashboard/topbar";
import { DashboardLayoutProvider } from "@/components/dashboard/layout-provider";
import { DashboardLayoutContent } from "@/components/dashboard/layout-content";
import { redirect } from "next/navigation";
import type { ReactNode } from "react";
import "./dashboard.css";

export default async function DashboardLayout({
  children,
}: {
  children: ReactNode;
}) {
  const session = await auth();

  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard");
  }

  return (
    <DashboardLayoutProvider>
      <div className="min-h-screen bg-[#0a0a0c]">
        {/* Dashboard layout with sidebar and main content */}
        <div className="flex h-screen overflow-hidden">
          {/* Sidebar */}
          <DashboardSidebar />

          {/* Main content area with dynamic layout */}
          <DashboardLayoutContent user={session.user}>
            {children}
          </DashboardLayoutContent>
        </div>
      </div>
    </DashboardLayoutProvider>
  );
}

"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { formatDistanceToNow } from "date-fns";
import {
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  Check,
  Clock,
  Filter,
  RotateCcw,
  Search,
  Shield,
  X,
} from "lucide-react";
import { AppealsHero } from "@/components/dashboard/appeals/appeals-hero";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

interface AppealUser {
  id: string;
  name: string | null;
  image: string | null;
}

interface AppealInfraction {
  id: string;
  hubId: string;
  type: string;
  status: string;
  reason: string;
  hub: {
    id: string;
    name: string;
    iconUrl: string;
  };
  user: AppealUser | null;
}

interface Appeal {
  id: string;
  infractionId: string;
  userId: string;
  reason: string;
  status: "PENDING" | "ACCEPTED" | "REJECTED";
  createdAt: string;
  updatedAt: string;
  user: AppealUser;
  infraction: AppealInfraction;
}

export default function AppealsPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();

  const [appeals, setAppeals] = useState<Appeal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [status, setStatus] = useState<string | null>(
    searchParams.get("status") || null,
  );
  const [userId, setUserId] = useState<string | null>(
    searchParams.get("userId") || null,
  );
  const [infractionId, setInfractionId] = useState<string | null>(
    searchParams.get("infractionId") || null,
  );
  const [hubId, setHubId] = useState<string | null>(
    searchParams.get("hubId") || null,
  );
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Hub options for filter
  const [hubs, setHubs] = useState<{ id: string; name: string }[]>([]);
  const [loadingHubs, setLoadingHubs] = useState(true);

  // Pagination
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 10;

  const fetchAppeals = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (status) queryParams.append("status", status);
      if (userId) queryParams.append("userId", userId);
      if (infractionId) queryParams.append("infractionId", infractionId);
      if (hubId) queryParams.append("hubId", hubId);
      queryParams.append("page", page.toString());
      queryParams.append("limit", itemsPerPage.toString());
      queryParams.append("moderatorView", "true"); // This ensures we only see appeals for hubs we have access to

      const response = await fetch(`/api/appeals?${queryParams.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch appeals");
      }

      const data = await response.json();
      setAppeals(data.appeals);
      setTotalPages(Math.ceil(data.total / itemsPerPage) || 1);
    } catch (error) {
      console.error("Error fetching appeals:", error);
      setError(
        error instanceof Error ? error.message : "Failed to fetch appeals",
      );
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to fetch appeals",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [page, status, userId, infractionId, hubId, toast]);

  // Fetch user's accessible hubs for the filter
  const fetchHubs = useCallback(async () => {
    try {
      setLoadingHubs(true);
      const response = await fetch("/api/hubs/accessible");

      if (!response.ok) {
        throw new Error("Failed to fetch hubs");
      }

      const data = await response.json();
      setHubs(data.hubs);
    } catch (error) {
      console.error("Error fetching hubs:", error);
      // Don't show toast for this error
    } finally {
      setLoadingHubs(false);
    }
  }, []);

  useEffect(() => {
    fetchAppeals();
    fetchHubs();
  }, [fetchAppeals, fetchHubs]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Reset pagination when searching
    setPage(1);

    // Update URL with search parameters
    const params = new URLSearchParams(window.location.search);

    // Preserve hubId if it exists
    if (!hubId) {
      params.delete("hubId");
    }

    // Clear previous search parameters
    params.delete("userId");
    params.delete("infractionId");

    if (searchQuery.startsWith("user:")) {
      const userId = searchQuery.substring(5).trim();
      setUserId(userId);
      params.set("userId", userId);
      setInfractionId(null);
    } else if (searchQuery.startsWith("infraction:")) {
      const infractionId = searchQuery.substring(11).trim();
      setInfractionId(infractionId);
      params.set("infractionId", infractionId);
      setUserId(null);
    } else {
      // If no prefix, assume it's an infraction ID
      if (searchQuery) {
        setInfractionId(searchQuery);
        params.set("infractionId", searchQuery);
        setUserId(null);
      } else {
        setUserId(null);
        setInfractionId(null);
      }
    }

    // Reset page to 1 when searching
    params.set("page", "1");

    // Update the URL without refreshing the page
    const url = new URL(window.location.href);
    url.search = params.toString();
    window.history.pushState({}, "", url.toString());
  };

  const resetFilters = () => {
    setStatus(null);
    setUserId(null);
    setInfractionId(null);
    setHubId(null);
    setSearchQuery("");
    setPage(1);

    // Clear URL parameters
    router.push("/dashboard/appeals");
  };

  // Count pending and resolved appeals
  const pendingAppeals = appeals.filter(
    (appeal) => appeal.status === "PENDING",
  ).length;
  const resolvedAppeals = appeals.filter(
    (appeal) => appeal.status !== "PENDING",
  ).length;

  return (
    <div className="space-y-6">
      {/* Animated Hero Section */}
      <AppealsHero
        totalAppeals={appeals.length}
        pendingAppeals={pendingAppeals}
        resolvedAppeals={resolvedAppeals}
      />

      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl font-bold tracking-tight">Appeals</h1>
      </div>

      {/* Filters */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm text-gray-400 mb-1 block">Status</label>
              <Select
                value={status || "all"}
                onValueChange={(value) =>
                  setStatus(value === "all" ? null : value)
                }
              >
                <SelectTrigger className="bg-[#0a0a0c] border-gray-800">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent className="bg-[#0f1117] border-gray-800">
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="ACCEPTED">Accepted</SelectItem>
                  <SelectItem value="REJECTED">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm text-gray-400 mb-1 block">Hub</label>
              <Select
                value={hubId || "all"}
                onValueChange={(value) => {
                  const newHubId = value === "all" ? null : value;
                  setHubId(newHubId);

                  // Update URL with the hub parameter
                  const params = new URLSearchParams(window.location.search);
                  if (newHubId) {
                    params.set("hubId", newHubId);
                  } else {
                    params.delete("hubId");
                  }

                  // Reset page to 1 when changing hub
                  setPage(1);
                  params.set("page", "1");

                  // Update URL without refreshing the page
                  const url = new URL(window.location.href);
                  url.search = params.toString();
                  window.history.pushState({}, "", url.toString());
                }}
                disabled={loadingHubs}
              >
                <SelectTrigger className="bg-[#0a0a0c] border-gray-800">
                  <SelectValue placeholder="All Hubs" />
                </SelectTrigger>
                <SelectContent className="bg-[#0f1117] border-gray-800 max-h-[200px]">
                  <SelectItem value="all">All Hubs</SelectItem>
                  {hubs.map((hub) => (
                    <SelectItem key={hub.id} value={hub.id}>
                      {hub.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="md:col-span-2">
              <label className="text-sm text-gray-400 mb-1 block">
                Filter by Infraction or ID
              </label>
              <form onSubmit={handleSearch} className="flex flex-wrap gap-2">
                <div className="flex-1 min-w-[200px]">
                  <Input
                    placeholder="user:123456789 or infraction:abc123"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 w-full"
                  />
                </div>
                <div className="flex gap-2">
                  <Button type="submit" variant="secondary" size="icon">
                    <Search className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={resetFilters}
                    className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Appeals List */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Appeals List</CardTitle>
          <CardDescription>
            {userId
              ? `Showing appeals from user ID: ${userId}`
              : infractionId
                ? `Showing appeals for infraction ID: ${infractionId}`
                : hubId
                  ? `Showing appeals for hub: ${hubs.find((h) => h.id === hubId)?.name || hubId}`
                  : "Showing all appeals"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <AppealSkeleton key={i} />
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 mx-auto text-red-500 mb-4" />
              <h3 className="text-lg font-medium mb-2">
                Error Loading Appeals
              </h3>
              <p className="text-gray-400 mb-4">{error}</p>
              <Button onClick={fetchAppeals}>Try Again</Button>
            </div>
          ) : appeals.length === 0 ? (
            <div className="text-center py-8">
              <Shield className="h-12 w-12 mx-auto text-gray-500 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Appeals Found</h3>
              <p className="text-gray-400 mb-4">
                {userId
                  ? "This user has not submitted any appeals."
                  : infractionId
                    ? "There are no appeals for this infraction."
                    : "There are no appeals to display."}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {appeals.map((appeal) => (
                <AppealCard
                  key={appeal.id}
                  appeal={appeal}
                  onStatusChange={fetchAppeals}
                />
              ))}
            </div>
          )}
        </CardContent>
        {!loading && appeals.length > 0 && (
          <CardFooter className="flex flex-col sm:flex-row justify-between items-center gap-4 border-t border-gray-800/50 pt-4">
            <div className="text-sm text-gray-400 order-2 sm:order-1">
              Page {page} of {totalPages}
            </div>
            <div className="flex gap-2 w-full sm:w-auto order-1 sm:order-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage((p) => Math.max(1, p - 1))}
                disabled={page === 1}
                className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white flex-1 sm:flex-initial"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
                className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white flex-1 sm:flex-initial"
              >
                Next
                <ArrowRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}

interface AppealCardProps {
  appeal: Appeal;
  onStatusChange: () => void;
}

function AppealCard({ appeal, onStatusChange }: AppealCardProps) {
  const { toast } = useToast();
  const [isUpdating, setIsUpdating] = useState(false);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20"
          >
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case "ACCEPTED":
        return (
          <Badge
            variant="outline"
            className="bg-green-500/10 text-green-500 border-green-500/20"
          >
            <Check className="h-3 w-3 mr-1" />
            Accepted
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge
            variant="outline"
            className="bg-red-500/10 text-red-500 border-red-500/20"
          >
            <X className="h-3 w-3 mr-1" />
            Rejected
          </Badge>
        );
      default:
        return null;
    }
  };

  const handleStatusChange = async (newStatus: "ACCEPTED" | "REJECTED") => {
    if (appeal.status !== "PENDING") return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/appeals/${appeal.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || `Failed to ${newStatus.toLowerCase()} appeal`,
        );
      }

      toast({
        title: `Appeal ${newStatus === "ACCEPTED" ? "Accepted" : "Rejected"}`,
        description:
          newStatus === "ACCEPTED"
            ? "The infraction has been appealed."
            : "The appeal has been rejected.",
      });

      // Refresh the appeals list
      onStatusChange();
    } catch (error) {
      console.error(`Error ${newStatus.toLowerCase()}ing appeal:`, error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : `Failed to ${newStatus.toLowerCase()} appeal`,
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="flex flex-col p-4 rounded-md bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm border border-gray-800/50">
      <div className="flex flex-col sm:flex-row sm:items-start justify-between mb-3 gap-3">
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full border-2 border-blue-500/20 overflow-hidden flex-shrink-0">
            <Image
              src={appeal.user.image || "/images/default-avatar.png"}
              alt={appeal.user.name || "User"}
              width={40}
              height={40}
              className="object-cover"
              style={{ width: "100%", height: "100%" }}
            />
          </div>
          <div className="min-w-0">
            <div className="font-medium flex items-center flex-wrap">
              <span className="truncate mr-2">
                {appeal.user.name || "Unknown User"}
              </span>
              <span className="text-gray-500 mx-1 hidden sm:inline">•</span>
              <span className="text-xs text-gray-400 truncate">
                {appeal.userId}
              </span>
            </div>
            <div className="text-xs text-gray-400 flex items-center gap-1">
              <Clock className="h-3 w-3 text-gray-500" />
              Submitted{" "}
              {formatDistanceToNow(new Date(appeal.createdAt), {
                addSuffix: true,
              })}
            </div>
          </div>
        </div>
        <div className="self-start mt-2 sm:mt-0">
          {getStatusBadge(appeal.status)}
        </div>
      </div>
      <div className="mb-3">
        <h4 className="text-sm font-medium text-gray-300 mb-1">
          Appeal Reason:
        </h4>
        <p className="text-sm text-gray-300">{appeal.reason}</p>
      </div>
      <div className="bg-gray-900/30 p-3 rounded-md mb-3">
        <div className="flex items-center gap-2 mb-2">
          <Shield className="h-4 w-4 text-gray-400" />
          <h4 className="text-sm font-medium">Related Infraction</h4>
        </div>
        <div className="flex items-center gap-2 mb-1">
          <span className="text-xs text-gray-400">ID:</span>
          <Link
            href={`/dashboard/infractions/${appeal.infractionId}`}
            className="text-xs text-blue-400 hover:underline"
          >
            {appeal.infractionId}
          </Link>
        </div>
        <div className="flex items-center gap-2 mb-1">
          <span className="text-xs text-gray-400">Hub:</span>
          <span className="text-xs">{appeal.infraction.hub.name}</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-400">Reason:</span>
          <span className="text-xs truncate">{appeal.infraction.reason}</span>
        </div>
      </div>
      {appeal.status === "PENDING" && (
        <div className="flex flex-wrap gap-2 justify-end mt-2">
          <Button
            variant="outline"
            size="sm"
            className="border-red-500/30 text-red-500 hover:bg-red-900/30 hover:text-red-300 hover:border-red-700/50 flex-1 sm:flex-initial"
            onClick={() => handleStatusChange("REJECTED")}
            disabled={isUpdating}
          >
            <X className="h-4 w-4 mr-1" />
            Reject
          </Button>
          <Button
            variant="default"
            size="sm"
            className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-600/80 hover:to-green-700/80 border-none flex-1 sm:flex-initial"
            onClick={() => handleStatusChange("ACCEPTED")}
            disabled={isUpdating}
          >
            <Check className="h-4 w-4 mr-1" />
            Accept
          </Button>
        </div>
      )}
    </div>
  );
}

function AppealSkeleton() {
  return (
    <div className="flex flex-col p-4 rounded-md bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm border border-gray-800/50">
      <div className="flex flex-col sm:flex-row sm:items-start justify-between mb-3 gap-3">
        <div className="flex items-center gap-3">
          <Skeleton className="h-10 w-10 rounded-full flex-shrink-0" />
          <div className="flex-1">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-4 w-24 mt-1" />
          </div>
        </div>
        <Skeleton className="h-6 w-20 self-start mt-2 sm:mt-0" />
      </div>

      <div className="mb-3">
        <Skeleton className="h-4 w-32 mb-1" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4 mt-1" />
      </div>

      <div className="bg-gray-900/50 p-3 rounded-md mb-3 border border-gray-800/50">
        <div className="flex items-center gap-2 mb-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-32" />
        </div>
        <Skeleton className="h-4 w-full mb-1" />
        <Skeleton className="h-4 w-3/4 mb-1" />
        <Skeleton className="h-4 w-5/6" />
      </div>

      <div className="flex gap-2 justify-end mt-2">
        <Skeleton className="h-8 w-20" />
        <Skeleton className="h-8 w-20" />
      </div>
    </div>
  );
}

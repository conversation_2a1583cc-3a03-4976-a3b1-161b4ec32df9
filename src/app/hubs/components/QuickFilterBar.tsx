"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  ArrowUpDown,
  Clock,
  Filter,
  Flame,
  Heart,
  ThumbsUp,
  Users
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useTransition } from "react";
import { SortOptions } from "../constants";

interface QuickFilterBarProps {
  popularTags: string[];
}

export function QuickFilterBar({ popularTags }: QuickFilterBarProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  // Get current sort from URL or default to trending
  const currentSort = searchParams.get("sort") || SortOptions.Trending;

  // Handle tag selection
  const handleTagClick = (tag: string) => {
    startTransition(() => {
      router.push(`/hubs/search?tags=${encodeURIComponent(tag)}`);
    });
  };

  // Handle sort selection
  const handleSortChange = (sort: SortOptions) => {
    startTransition(() => {
      router.push(`/hubs/search?sort=${sort}`);
    });
  };

  // Sort options with icons
  const sortOptions = [
    { value: SortOptions.Trending, label: "Trending", icon: Flame },
    { value: SortOptions.Upvotes, label: "Most Upvoted", icon: Heart },
    { value: SortOptions.MostActive, label: "Most Active", icon: Users },
    { value: SortOptions.Created, label: "Recently Created", icon: Clock },
    { value: SortOptions.MostRecentPopular, label: "Popular", icon: ThumbsUp },
  ];

  // Get current sort option
  const currentSortOption = sortOptions.find(option => option.value === currentSort) || sortOptions[0];

  return (
    <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-xl p-4">
      <div className="flex flex-wrap gap-4 items-center justify-between">
        <div className="text-sm text-gray-300 font-medium">
          <span className="text-primary mr-2">Browse</span>
          or filter hubs by category
          <span className="ml-2 text-xs text-gray-400">(Use search above for keyword search)</span>
        </div>

        {/* Sort dropdown */}
        <div className="flex gap-3">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="h-10 border-gray-700 bg-gray-900/50 hover:bg-gray-800 hover:border-gray-600"
                disabled={isPending}
              >
                {isPending ? (
                  <span className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                ) : (
                  <ArrowUpDown className="h-4 w-4 mr-2" />
                )}
                <span className="flex items-center gap-2">
                  <currentSortOption.icon className="h-4 w-4 text-primary" />
                  {currentSortOption.label}
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 bg-gray-900 border-gray-700">
              {sortOptions.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  onClick={() => handleSortChange(option.value)}
                  className={`flex items-center gap-2 cursor-pointer ${
                    option.value === currentSort ? "bg-primary/10 text-primary" : ""
                  }`}
                >
                  <option.icon className="h-4 w-4" />
                  {option.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Advanced filters button */}
          <Button
            variant="outline"
            className="h-10 border-gray-700 bg-gray-900/50 hover:bg-gray-800 hover:border-gray-600"
            disabled={isPending}
            onClick={() => startTransition(() => router.push("/hubs/search"))}
          >
            {isPending ? (
              <span className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
            ) : (
              <Filter className="h-4 w-4 mr-2" />
            )}
            Advanced Filters
          </Button>
        </div>
      </div>

      {/* Popular tags */}
      <div className="mt-4 flex flex-wrap gap-2 items-center">
        <span className="text-sm text-gray-400 mr-1">Popular:</span>
        {popularTags.map((tag) => (
          <Button
            key={tag}
            variant="outline"
            size="sm"
            className="h-7 px-3 py-0 text-xs border-gray-700/50 bg-gray-800/50 hover:bg-gray-700 hover:text-white hover:border-gray-600"
            disabled={isPending}
            onClick={() => handleTagClick(tag)}
          >
            {tag}
          </Button>
        ))}
      </div>
    </div>
  );
}

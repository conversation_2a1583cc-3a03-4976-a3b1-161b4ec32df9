"use client";

import { useState, useRef, useEffect } from "react";
import { X, Plus, Edit2, Check, HelpCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  MatchPattern,
  parseWordPattern,
  formatWordWithPattern,
  type AntiSwearPattern,
  wordAndMatchPatternToPattern,
  getMatchPatternFromPattern
} from "@/lib/types/anti-swear";

interface WordTagInputProps {
  patterns: AntiSwearPattern[];
  onChange: (patterns: AntiSwearPattern[]) => void;
  className?: string;
}

export function WordTagInput({ patterns, onChange, className }: WordTagInputProps) {
  const [tags, setTags] = useState<AntiSwearPattern[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editValue, setEditValue] = useState("");
  const [editPattern, setEditPattern] = useState<MatchPattern>(MatchPattern.EXACT);
  const inputRef = useRef<HTMLInputElement>(null);
  const editInputRef = useRef<HTMLInputElement>(null);

  // Initialize tags from patterns
  useEffect(() => {
    setTags(patterns);
  }, [patterns]);

  // Update the parent component when tags change
  const updatePatterns = (newTags: AntiSwearPattern[]) => {
    onChange(newTags);
  };

  const addTag = () => {
    if (inputValue.trim()) {
      // Split by commas and process each word
      const words = inputValue.split(',').map(word => word.trim()).filter(word => word.length > 0);

      // Create new patterns for each word
      const newPatterns = words.map(word =>
        wordAndMatchPatternToPattern(word, MatchPattern.EXACT)
      );

      const newTags = [...tags, ...newPatterns];
      setTags(newTags);
      updatePatterns(newTags);
      setInputValue("");
      inputRef.current?.focus();
    }
  };

  const removeTag = (index: number) => {
    const newTags = [...tags];
    newTags.splice(index, 1);
    setTags(newTags);
    updatePatterns(newTags);
  };

  const startEditing = (index: number) => {
    const tag = tags[index];
    setEditingIndex(index);

    const { word, pattern } = parseWordPattern(tag.pattern);
    setEditValue(word);
    setEditPattern(pattern);

    // Focus the edit input after it's rendered
    setTimeout(() => {
      editInputRef.current?.focus();
    }, 10);
  };

  const saveEdit = () => {
    if (editingIndex !== null && editValue.trim()) {
      const newTags = [...tags];

      newTags[editingIndex] = {
        pattern: formatWordWithPattern(editValue.trim(), editPattern)
      };

      setTags(newTags);
      updatePatterns(newTags);
      setEditingIndex(null);
      setEditValue("");
    }
  };

  const cancelEdit = () => {
    setEditingIndex(null);
    setEditValue("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addTag();
    }
  };

  const handleEditKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      saveEdit();
    } else if (e.key === "Escape") {
      cancelEdit();
    }
  };

  const getPatternClass = (pattern: AntiSwearPattern) => {
    const matchPattern = getMatchPatternFromPattern(pattern);
    switch (matchPattern) {
      case MatchPattern.EXACT:
        return "bg-blue-900/40 border-blue-700/50 text-blue-200";
      case MatchPattern.PREFIX:
        return "bg-green-900/40 border-green-700/50 text-green-200";
      case MatchPattern.SUFFIX:
        return "bg-purple-900/40 border-purple-700/50 text-purple-200";
      case MatchPattern.SUBSTRING:
        return "bg-amber-900/40 border-amber-700/50 text-amber-200";
      default:
        return "bg-gray-900/40 border-gray-700/50 text-gray-200";
    }
  };

  const cyclePattern = (currentPattern: MatchPattern) => {
    const patterns = [
      MatchPattern.EXACT,
      MatchPattern.PREFIX,
      MatchPattern.SUFFIX,
      MatchPattern.SUBSTRING,
    ];
    const currentIndex = patterns.indexOf(currentPattern);
    const nextIndex = (currentIndex + 1) % patterns.length;
    return patterns[nextIndex];
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex flex-wrap gap-3 sm:gap-2 p-4 sm:p-3 bg-gray-950/70 rounded-md border border-gray-800/50 min-h-[120px] sm:min-h-[100px] w-full">
        {tags.map((tag, index) => (
          editingIndex === index ? (
            <div key={`editing-${tag.pattern}-${index}`} className="flex flex-wrap items-center gap-1 bg-gray-900 p-2 rounded-md border border-gray-700/50 w-full sm:w-auto">
              <Input
                ref={editInputRef}
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                onKeyDown={handleEditKeyDown}
                className="h-8 sm:h-7 w-full sm:w-32 min-w-0 bg-transparent border-0 focus-visible:ring-0 p-1"
              />

              <div className="flex items-center justify-between w-full sm:w-auto gap-1 mt-1 sm:mt-0">
                <TooltipProvider delayDuration={700}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-8 sm:h-6 w-8 sm:w-6 p-0 rounded-full"
                        onClick={() => setEditPattern(cyclePattern(editPattern))}
                      >
                        <span className="text-xs font-mono">
                          {editPattern === MatchPattern.EXACT ? "abc" :
                           editPattern === MatchPattern.PREFIX ? "abc*" :
                           editPattern === MatchPattern.SUFFIX ? "*abc" : "*abc*"}
                        </span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="top" align="center" className="z-50">
                      <p>Click to cycle through match patterns</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <div className="flex items-center gap-1">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-8 sm:h-6 w-8 sm:w-6 p-0 text-green-400 hover:text-green-300 hover:bg-green-950/30"
                    onClick={saveEdit}
                  >
                    <Check className="h-4 sm:h-3 w-4 sm:w-3" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-8 sm:h-6 w-8 sm:w-6 p-0 text-gray-400 hover:text-gray-300 hover:bg-gray-800/50"
                    onClick={cancelEdit}
                  >
                    <X className="h-4 sm:h-3 w-4 sm:w-3" />
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <Badge
              key={`tag-${tag.pattern}-${index}`}
              variant="outline"
              className={`px-2 py-1.5 sm:py-1 ${getPatternClass(tag)} hover:bg-opacity-60 transition-colors group flex flex-wrap sm:flex-nowrap items-center gap-1 sm:gap-0`}
            >
              <span className="font-mono break-all">{tag.pattern}</span>
              <div className="ml-0 sm:ml-1 flex items-center gap-1 sm:space-x-1 mt-1 sm:mt-0 w-full sm:w-auto justify-end">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-7 sm:h-5 w-7 sm:w-5 p-0 rounded-full opacity-70 hover:opacity-100 hover:bg-gray-800/50"
                  onClick={() => startEditing(index)}
                >
                  <Edit2 className="h-4 sm:h-3 w-4 sm:w-3" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-7 sm:h-5 w-7 sm:w-5 p-0 rounded-full opacity-70 hover:opacity-100 hover:bg-red-950/30 text-red-400"
                  onClick={() => removeTag(index)}
                >
                  <X className="h-4 sm:h-3 w-4 sm:w-3" />
                </Button>
              </div>
            </Badge>
          )
        ))}

        <div className="flex flex-col sm:flex-row w-full sm:w-auto gap-2 sm:gap-0">
          <div className="flex items-center w-full sm:w-auto">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Add words (comma separated)..."
              className="h-9 sm:h-8 w-full sm:w-40 bg-gray-900/50 border-gray-700/50 focus:border-purple-500/50"
            />

            <TooltipProvider delayDuration={700}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-9 sm:h-8 w-9 sm:w-8 p-0 text-purple-400 hover:text-purple-300 hover:bg-purple-950/30"
                    onClick={addTag}
                  >
                    <Plus className="h-5 sm:h-4 w-5 sm:w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top" align="center" className="z-50">
                  <p>Add patterns (comma separated)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider delayDuration={700}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-9 sm:h-8 w-9 sm:w-8 p-0 text-amber-400 hover:text-amber-300 hover:bg-amber-950/30"
                  >
                    <HelpCircle className="h-5 sm:h-4 w-5 sm:w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top" align="start" className="max-w-xs z-50">
                  <div className="space-y-2">
                    <p className="font-semibold">Pattern Matching:</p>
                    <ul className="text-xs space-y-1.5">
                      <li><code className="bg-gray-800 px-1 rounded">word</code> - Exact match only</li>
                      <li><code className="bg-gray-800 px-1 rounded">word*</code> - Matches words that start with this</li>
                      <li><code className="bg-gray-800 px-1 rounded">*word</code> - Matches words that end with this</li>
                      <li><code className="bg-gray-800 px-1 rounded">*word*</code> - Matches words containing this</li>
                    </ul>
                    <p className="text-xs mt-2">You can add multiple words at once by separating them with commas.</p>
                    <p className="text-xs italic">Example: <code className="bg-gray-800 px-1 rounded">bad, worse, *worst*</code></p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>

      <div className="flex items-start sm:items-center text-xs text-gray-400">
        <HelpCircle className="h-4 sm:h-3 w-4 sm:w-3 mr-1 text-amber-400 mt-0.5 sm:mt-0 flex-shrink-0" />
        <span>Tips: Use commas to add multiple words at once. Use * for pattern matching. Tap/hover the help icon above for details.</span>
      </div>
    </div>
  );
}

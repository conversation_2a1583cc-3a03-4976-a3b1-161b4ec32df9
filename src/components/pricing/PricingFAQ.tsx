"use client";

interface FAQItem {
  question: string;
  answer: string;
}

const faqItems: FAQItem[] = [
  {
    question: "How do I upgrade my plan?",
    answer:
      "You can upgrade your plan by clicking on the \"Upgrade Now\" button and following the payment process through Ko-fi. Once your payment is processed, your account will be automatically upgraded with the new features.",
  },
  {
    question: "Can I switch between plans?",
    answer:
      "Yes, you can upgrade or downgrade your plan at any time. When upgrading, you'll get immediate access to the new features. When downgrading, you'll retain your current plan until the end of your billing period.",
  },
  {
    question: "Do you offer refunds?",
    answer:
      "We offer a 7-day money-back guarantee if you're not satisfied with your purchase. Please contact our support team through Discord for assistance with refunds.",
  },
  {
    question: "What payment methods do you accept?",
    answer:
      "We process payments through Ko-fi, which accepts credit cards, PayPal, and other major payment methods. All transactions are secure and encrypted.",
  },
];

export function PricingFAQ() {
  return (
    <section className="py-16 relative">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-gray-400">
            Have questions about our pricing? Find answers to common questions
            below.
          </p>
        </div>

        <div className="space-y-6">
          {faqItems.map((item, index) => (
            <div 
              key={index} 
              className="bg-gray-800/20 rounded-lg p-6 border border-gray-700/30"
            >
              <h3 className="text-xl font-medium text-white mb-3">
                {item.question}
              </h3>
              <p className="text-gray-300">
                {item.answer}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

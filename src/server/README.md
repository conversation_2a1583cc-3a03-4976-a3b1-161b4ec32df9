# tRPC API Documentation

This project uses tRPC to create type-safe APIs with end-to-end typesafety between the client and server.

## Structure

- `src/server/trpc.ts` - Core tRPC setup with context and procedures
- `src/server/routers/` - API routers organized by domain
- `src/utils/trpc.ts` - Client-side tRPC setup
- `src/app/api/trpc/[trpc]/route.ts` - Next.js API handler for tRPC

## Creating a new router

1. Create a new file in `src/server/routers/` for your domain
2. Define your procedures using `publicProcedure` or `protectedProcedure`
3. Add your router to the main `appRouter` in `src/server/routers/index.ts`

Example:

```typescript
// src/server/routers/example.ts
import { z } from 'zod';
import { router, publicProcedure, protectedProcedure } from '../trpc';

export const exampleRouter = router({
  hello: publicProcedure
    .input(z.object({ name: z.string() }))
    .query(({ input }) => {
      return { greeting: `Hello, ${input.name}!` };
    }),
    
  secretData: protectedProcedure
    .query(({ ctx }) => {
      // ctx.session.user is guaranteed to exist
      return { message: `Secret data for ${ctx.session.user.name}` };
    }),
});

// src/server/routers/index.ts
import { router } from '../trpc';
import { exampleRouter } from './example';

export const appRouter = router({
  example: exampleRouter,
  // other routers...
});
```

## Using tRPC on the client

```tsx
"use client";

import { trpc } from "@/utils/trpc";

export function MyComponent() {
  // Query example
  const helloQuery = trpc.example.hello.useQuery({ name: "World" });
  
  // Mutation example
  const createMutation = trpc.example.create.useMutation({
    onSuccess: () => {
      // Handle success
    },
  });
  
  if (helloQuery.isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      <h1>{helloQuery.data?.greeting}</h1>
      <button 
        onClick={() => createMutation.mutate({ name: "New Item" })}
        disabled={createMutation.isPending}
      >
        Create
      </button>
    </div>
  );
}
```

## Benefits of tRPC

- Full type safety between client and server
- Automatic input validation with Zod
- Seamless integration with React Query for data fetching
- No need for code generation or GraphQL schema
- Great developer experience with autocompletion

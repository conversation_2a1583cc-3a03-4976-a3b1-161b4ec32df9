export const HUBS_PER_PAGE = 12;

export enum SortOptions {
  Trending = "trending",
  MostUpvotedNew = "most_upvoted_new",
  MostRecentPopular = "most_recent_popular",
  MostActive = "most_active", // Based on recent message activity
  Created = "created", // Newest first
  Name = "name", // A-Z
  Upvotes = "upvotes", // Total upvotes desc
  Servers = "servers", // Total connected servers desc
  LastActive = "last_active", // Uses lastActive field on Hub
}

export enum ContentFilter {
  All = "all",
  SFW = "sfw",
  NSFW = "nsfw",
}

export enum VerificationStatus {
  All = "all",
  Verified = "verified",
  Partnered = "partnered",
  VerifiedOrPartnered = "verified_or_partnered",
}

export const LANGUAGES = [
  { code: "en", name: "English" },
  { code: "es", name: "Spanish" },
  { code: "fr", name: "French" },
  { code: "de", name: "German" },
  { code: "it", name: "Italian" },
  { code: "pt", name: "Portuguese" },
  { code: "ru", name: "Russian" },
  { code: "ja", name: "Japanese" },
  { code: "ko", name: "Korean" },
  { code: "zh", name: "Chinese" },
  { code: "ar", name: "Arabic" },
  { code: "hi", name: "Hindi" },
  { code: "tr", name: "Turkish" },
  { code: "nl", name: "Dutch" },
  { code: "sv", name: "Swedish" },
  { code: "pl", name: "Polish" },
  { code: "vi", name: "Vietnamese" },
  { code: "th", name: "Thai" },
  { code: "id", name: "Indonesian" },
  { code: "other", name: "Other" },
];

export const REGIONS = [
  { code: "na", name: "North America" },
  { code: "sa", name: "South America" },
  { code: "eu", name: "Europe" },
  { code: "as", name: "Asia" },
  { code: "af", name: "Africa" },
  { code: "oc", name: "Oceania" },
  { code: "global", name: "Global" },
];

// Popular tags for hub discovery
export const POPULAR_TAGS = [
  "gaming",
  "art",
  "technology",
  "music",
  "anime",
  "memes",
  "education",
  "community",
  "programming",
  "design",
  "sports",
  "movies",
  "books",
  "food",
  "travel",
  "photography",
  "science",
  "writing",
  "roleplay",
  "politics",
  "news",
  "fashion",
  "pets",
  "fitness",
];

"use client";

import { memo } from "react";
import { motion } from "motion/react";
import Image from "next/image";

interface Message {
  id: number;
  server: string;
  user: string;
  text: string;
  avatar: string;
  badge?: string;
  timestamp?: string;
  reactions?: {
    emoji: string;
    count: number;
  }[];
}

// Optimized message component with improved styling and mobile support
const ChatMessage = ({ msg }: { msg: Message }) => (
  <motion.div
    initial={{ opacity: 0, y: 5 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0 }}
    transition={{
      duration: 0.2,
      ease: "easeOut",
    }}
    className="flex items-start gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg hover:bg-[#32353b] group transition-colors duration-200"
    layout="position"
  >
    <div className="relative flex-shrink-0">
      <div className="relative">
        {msg.avatar.endsWith(".mp4") ? (
          <video
            src={msg.avatar}
            autoPlay
            loop
            muted
            width={32}
            height={32}
            className="w-8 h-8 sm:w-10 sm:h-10 rounded-full cursor-pointer border-2 border-[#4f545c] hover:border-primary transition-colors"
          />
        ) : (
          <Image
            src={msg.avatar || "/placeholder.svg"}
            alt={msg.user}
            width={32}
            height={32}
            className="w-8 h-8 sm:w-10 sm:h-10 rounded-full cursor-pointer border-2 border-[#4f545c] hover:border-primary transition-colors"
            loading="lazy"
          />
        )}
        <div className="absolute bottom-0 right-0 w-2 h-2 sm:w-3 sm:h-3 bg-green-500 rounded-full border-2 border-[#313338]"></div>
      </div>
    </div>
    <div className="flex-1 min-w-0">
      <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
        <p className="font-bold text-white cursor-pointer hover:underline text-xs sm:text-sm">
          {msg.user}
        </p>
        <span className="text-[10px] sm:text-xs text-gray-400 flex items-center gap-1">
          <span className="inline-block w-1 h-1 bg-gray-500 rounded-full"></span>
          {msg.server}
        </span>
        {msg.badge && (
          <span className="px-1 sm:px-1.5 py-0.5 text-[10px] sm:text-xs bg-primary/20 text-primary rounded-full font-medium">
            {msg.badge}
          </span>
        )}
        {msg.timestamp && (
          <span className="text-[10px] sm:text-xs text-gray-500 ml-auto">{msg.timestamp}</span>
        )}
      </div>
      <p className="text-xs sm:text-sm text-[#dcddde] mt-0.5 sm:mt-1 leading-relaxed">{msg.text}</p>
      {msg.reactions && (
        <div className="mt-1.5 sm:mt-2 flex gap-1 sm:gap-1.5 flex-wrap">
          {msg.reactions.map((reaction, i) => (
            <button
              key={i}
              className="px-1.5 sm:px-2 py-0.5 text-[10px] sm:text-xs bg-[#2f3136] rounded-full hover:bg-primary/20 hover:text-primary transition-colors flex items-center gap-1 border border-[#4f545c]/50"
              aria-label={`React with ${reaction.emoji}`}
            >
              <span>{reaction.emoji}</span>
              <span>{reaction.count}</span>
            </button>
          ))}
          <button className="px-1.5 sm:px-2 py-0.5 text-[10px] sm:text-xs bg-transparent rounded-full hover:bg-[#2f3136] transition-colors flex items-center border border-transparent hover:border-[#4f545c]/50">
            <span className="text-gray-400">+</span>
          </button>
        </div>
      )}
    </div>
  </motion.div>
);

export default memo(ChatMessage);

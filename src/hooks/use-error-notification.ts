"use client";

import { useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";

interface ErrorNotificationProps {
  isError: boolean;
  error: unknown;
  title: string;
  description?: string;
  variant?: "default" | "destructive";
}

/**
 * A hook to show error notifications when an error occurs
 */
export function useErrorNotification({
  isError,
  error,
  title,
  description,
  variant = "destructive",
}: ErrorNotificationProps) {
  const { toast } = useToast();

  useEffect(() => {
    if (isError) {
      toast({
        title,
        description: description || (error instanceof Error ? error.message : "An unknown error occurred"),
        variant,
      });
    }
  }, [isError, error, title, description, variant, toast]);
}

/**
 * A hook to show error notifications for query errors
 */
export function useQueryErrorNotification(
  query: { isError: boolean; error: unknown },
  title: string,
  description?: string
) {
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title,
    description,
  });
}

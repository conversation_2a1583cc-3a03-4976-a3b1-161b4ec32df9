"use client";

import { featureComparisonData } from "@/lib/pricing-constants";
import { Fragment } from "react";

export function FeatureComparisonTable() {
	return (
		<section className="py-16 relative">
			<div className="container mx-auto px-4">
				<div className="text-center mb-12">
					<h2 className="text-3xl font-bold text-white mb-4">
						Feature Comparison
					</h2>
					<p className="text-gray-400 max-w-2xl mx-auto">
						Compare all features across our different plans to find the one that
						best suits your community&apos;s needs.
					</p>
				</div>

				<div className="overflow-x-auto">
					<table className="w-full border-collapse">
						<thead>
							<tr className="border-b border-gray-800">
								<th className="py-4 px-6 text-left text-gray-400 font-medium">
									Features
								</th>
								<th className="py-4 px-6 text-center text-gray-400 font-medium">
									Free
								</th>
								<th className="py-4 px-6 text-center text-primary font-medium">
									Talker
								</th>
								<th className="py-4 px-6 text-center text-amber-500 font-medium">
									Broadcaster
								</th>
							</tr>
						</thead>
						<tbody>
							{featureComparisonData.map((category) => (
								<Fragment key={category.category}>
									<tr className="bg-gray-900/50">
										<td
											colSpan={4}
											className="py-3 px-6 text-left font-medium text-white"
										>
											{category.category}
										</td>
									</tr>
									{category.features.map((feature) => (
										<tr
											key={feature.name}
											className="border-b border-gray-800/50 hover:bg-gray-800/20 transition-colors"
										>
											<td className="py-4 px-6 text-left text-gray-300">
												{feature.name}
											</td>
											<td className="py-4 px-6 text-center text-gray-400">
												{feature.free}
											</td>
											<td className="py-4 px-6 text-center text-gray-300">
												{feature.talker}
											</td>
											<td className="py-4 px-6 text-center text-gray-300">
												{feature.broadcaster}
											</td>
										</tr>
									))}
								</Fragment>
							))}
						</tbody>
					</table>
				</div>
			</div>
		</section>
	);
}

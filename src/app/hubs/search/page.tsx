import { HubGrid } from "../components/hub-grid";
import { getSortedHubs, buildWhereClause } from "../utils";
import { type SortOptions, ContentFilter, VerificationStatus } from "../constants";
import type { Metadata } from "next";
import { HubListingSchema } from "../components/HubsSchema";
import { SearchHeader } from "../components/SearchHeader";

interface PageProps {
  searchParams: Promise<{
    sort?: string;
    search?: string;
    tags?: string;
    contentFilter?: string;
    verificationStatus?: string;
    language?: string;
    region?: string;
    minMembers?: string;
    maxMembers?: string;
  }>;
}

export async function generateMetadata({
  searchParams,
}: PageProps): Promise<Metadata> {
  const params = await searchParams;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://interchat.tech";
  const search = params.search;
  const tags = params.tags?.split(",").filter(Boolean);

  let title = "Search Discord Cross-Server Chat-Rooms | InterChat Hub Explorer";
  let description =
    "Find and join active Discord cross-server communities with InterChat Hubs. Browse by category, popularity, or activity level and connect your server today!";

  // Customize metadata based on search/tags if present
  if (search) {
    title = `${search} Discord Cross-Server Chat-Rooms | InterChat Hub Search Results`;
    description = `Find Discord cross-server communities related to ${search}. Browse and connect your server to active ${search} channels with InterChat.`;
  } else if (tags && tags.length > 0) {
    const tagsList = tags.join(", ");
    title = `${tagsList} Discord Cross-Server Chat-Rooms | InterChat Community Search`;
    description = `Discover Discord cross-server communities tagged with ${tagsList}. Connect your server to these active hubs and grow your community.`;
  }

  return {
    title,
    description,
    keywords: [
      "discord hub search",
      "discord chatroom",
      "find discord communities",
      "active discord servers",
      "discord server browser",
      "connect discord channels",
      "interchat hubs",
      ...(search ? [`${search} discord`, `${search} communities`] : []),
      ...(tags || []),
    ],
    openGraph: {
      title,
      description,
      type: "website",
      url: `${baseUrl}/hubs/search${search ? `?search=${encodeURIComponent(search)}` : ""}${tags ? `?tags=${tags.join(",")}` : ""}`,
      images: [
        {
          url: `${baseUrl}/features/HubDiscovery.png`,
          width: 1200,
          height: 630,
          alt: "InterChat Hub Search",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      images: [`${baseUrl}/features/HubDiscovery.png`],
      title: title.length > 70 ? `${title.substring(0, 67)}...` : title,
      description:
        description.length > 200
          ? `${description.substring(0, 197)}...`
          : description,
      creator: "@737_dev",
      site: "@interchatapp",
    },
    alternates: {
      canonical: `${baseUrl}/hubs/search`,
    },
  };
}

export default async function SearchPage(props: PageProps) {
  const searchParams = await props.searchParams;
  const sort = (searchParams.sort || "trending") as SortOptions;
  const search = searchParams.search;
  const tags = searchParams.tags?.split(",").filter(Boolean);

  // New filter parameters
  const contentFilter = (searchParams.contentFilter as ContentFilter) || ContentFilter.All;
  const verificationStatus = (searchParams.verificationStatus as VerificationStatus) || VerificationStatus.All;
  const language = searchParams.language;
  const region = searchParams.region;
  const minMembers = searchParams.minMembers ? Number.parseInt(searchParams.minMembers, 10) : undefined;
  const maxMembers = searchParams.maxMembers ? Number.parseInt(searchParams.maxMembers, 10) : undefined;

  // Build where clause with all filter options
  const filterOptions = {
    search,
    tags,
    contentFilter,
    verificationStatus,
    language,
    region,
    minMembers,
    maxMembers
  };

  const whereClause = buildWhereClause(filterOptions);
  const { hubs, totalCount } = await getSortedHubs(whereClause, 0, sort);

  // Get the base URL for structured data
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://interchat.tech";

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-950 via-gray-950 to-gray-900">
      {/* Search Header Component */}
      <div className="relative z-10">
        <SearchHeader />
      </div>

      <div className="container mx-auto px-4 py-8 relative">
        <HubListingSchema hubs={hubs} baseUrl={baseUrl} />
        <HubGrid
          initialHubs={hubs}
          pagination={{
            page: 1,
            totalPages: Math.ceil(totalCount / 12),
            totalItems: totalCount,
            hasMore: hubs.length < totalCount,
          }}
        />
      </div>
    </div>
  );
}

"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Zap } from "lucide-react";
import Link from "next/link";

export function PricingCTA() {
  return (
    <section className="py-16 relative overflow-hidden">
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full max-w-6xl">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary/20 via-purple-900/10 to-blue-900/20 rounded-full blur-3xl opacity-50" />
        </div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="bg-gradient-to-br from-gray-900/80 to-gray-950/80 backdrop-blur-sm border border-gray-800/50 rounded-2xl p-8 md:p-12 max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Enhance Your Discord Community?
          </h2>
          <p className="text-gray-300 text-lg mb-8 max-w-2xl mx-auto">
            Join thousands of Discord communities already using InterChat to
            connect and grow their servers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/invite">
              <Button className="bg-primary hover:bg-primary/90 text-white px-8 py-6 rounded-xl shadow-lg shadow-primary/20 hover:shadow-xl hover:shadow-primary/30 transition-all duration-300 hover:-translate-y-1">
                Get Started for Free
                <Zap className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/hubs">
              <Button
                variant="outline"
                className="bg-transparent border-2 border-gray-700 hover:border-white text-white px-8 py-6 rounded-xl hover:bg-white/5 transition-all duration-300 hover:-translate-y-1"
              >
                Explore Hubs
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}

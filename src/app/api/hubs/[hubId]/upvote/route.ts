import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

type Props = {
  params: Promise<{ hubId: string }>;
};

export async function POST(request: NextRequest, { params }: Props) {
  const session = await auth();

  // The ID is stored in token.sub for JWT strategy
  const userId = session?.user?.id;

  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { hubId } = await params;

  try {
    // Check if the hub exists
    const hub = await prisma.hub.findUnique({
      where: { id: hubId },
      select: { id: true },
      
    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }
    const existingUpvote = await prisma.hubUpvote.findUnique({
      where: {
        hubId_userId: {
          hubId,
          userId, // Using the extracted userId
        },
      },
      
    });

    if (existingUpvote) {
      // Remove upvote
      await prisma.hubUpvote.delete({
        where: {
          hubId_userId: {
            hubId,
            userId, // Using the extracted userId
          },
        },
      });
      return NextResponse.json({ upvoted: false });
    } else {
      // Add upvote
      await prisma.hubUpvote.create({
        data: {
          hubId,
          userId, // Using the extracted userId
        },
      });
      return NextResponse.json({ upvoted: true });
    }
  } catch (error) {
    console.error("Error handling hub upvote:", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 },
    );
  }
}

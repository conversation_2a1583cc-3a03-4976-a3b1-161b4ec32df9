---
title: Connections
description: Connect channels to hubs with InterChat
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Steps } from 'fumadocs-ui/components/steps'

# Connections

Connections are the links between your Discord channels and InterChat hubs, enabling cross-server communication.

## Understanding Connections

A connection links a specific channel in your Discord server to a hub. Once connected:

- Messages sent in your channel will be relayed to all other channels in the hub
- Messages from other connected channels will appear in your channel
- Webhooks are used to deliver messages, preserving usernames and avatars

<Callout type="info">
  Each channel can only be connected to one hub at a time, but your server can have multiple channels connected to different hubs.
</Callout>

## Creating a Connection

<Steps>
  ### Find or create a hub
  
  Before connecting, you need a hub to connect to. Either:
  - Join an existing hub using `/hub browse`
  - Create your own hub with `/hub create`
  
  ### Use the connect command
  
  In the channel you want to connect, use the `/connect` command.
  
  ### Select a hub
  
  Choose which hub you want to connect to from the dropdown menu.
  
  ### Confirm the connection
  
  The bot will create a webhook in your channel and establish the connection.
  
  <Callout type="warning">
    You need the **Manage Webhooks** permission in your server to create connections.
  </Callout>
</Steps>

## Managing Connections

InterChat provides several commands to manage your connections:

### Viewing Connections

Use `/connection list` to see all connections in your server. This will show:
- Which channels are connected
- Which hubs they're connected to
- Whether the connection is active or paused

### Pausing and Resuming

You can temporarily pause a connection without removing it:

- `/connection pause` - Temporarily stop receiving and sending messages
- `/connection unpause` - Resume a paused connection

This is useful for maintenance or when you want to temporarily disconnect from a hub.

### Editing Connections

Use `/connection edit` to modify an existing connection. You can:
- Change the connected channel
- Update webhook settings
- Toggle compact mode (simplified message display)

### Removing Connections

To completely remove a connection, use the `/disconnect` command in the connected channel.

<Callout type="warning">
  Disconnecting is permanent. You'll need to create a new connection if you want to rejoin the hub.
</Callout>

## Troubleshooting

If messages aren't being sent or received properly:

1. Check if the connection is paused with `/connection list`
2. Verify that the bot has the necessary permissions
3. Try editing the connection with `/connection edit` to refresh the webhook
4. If problems persist, try disconnecting and creating a new connection

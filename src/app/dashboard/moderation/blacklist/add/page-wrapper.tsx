"use server";

import AddToBlacklistPage from "./client-page";
import { PermissionCheck } from "./permission-check";

export default async function AddToBlacklistPageWrapper({
  searchParams,
}: {
  searchParams: Promise<{ hubId?: string }>;
}) {
  // Check permissions first
  await PermissionCheck({ hubId: (await searchParams)?.hubId });

  // If permission check passes, render the client page component
  return <AddToBlacklistPage />;
}

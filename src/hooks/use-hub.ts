"use client";

import { useQuery, useMutation, useQueryClient } from "@/lib/tanstack-query";
import { useToast } from "@/components/ui/use-toast";
import { fetchApi } from "@/lib/api";
import { useErrorNotification } from "./use-error-notification";
import { SortOptions } from "@/app/hubs/constants";

// Query keys
export const hubKeys = {
  all: ["hubs"] as const,
  lists: () => [...hubKeys.all, "list"] as const,
  list: (filters: { search?: string; tags?: string[]; sort?: SortOptions }) =>
    [...hubKeys.lists(), filters] as const,
  details: () => [...hubKeys.all, "detail"] as const,
  detail: (id: string) => [...hubKeys.details(), id] as const,
};

// Fetch a single hub by ID
export async function getHub(hubId: string) {
  return fetchApi(`/api/hubs/${hubId}`);
}

// Hook for fetching a hub
export function useHub(hubId: string) {
  const query = useQuery({
    queryKey: hubKeys.detail(hubId),
    queryFn: () => getHub(hubId),
  });

  // Handle error with useEffect
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: "Error",
    description: "Failed to load hub",
  });

  return query;
}

// Interface for upvote response
interface UpvoteResponse {
  upvoted: boolean;
}

// Upvote a hub
export async function upvoteHub(hubId: string): Promise<UpvoteResponse> {
  const response = await fetchApi<UpvoteResponse>(`/api/hubs/${hubId}/upvote`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
  });
  return response;
}

// Hook for upvoting a hub
export function useHubUpvote(hubId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: () => upvoteHub(hubId),
    onSuccess: (data) => {
      toast({
        title: data.upvoted ? "Upvoted hub" : "Removed upvote",
        description: data.upvoted
          ? "Thanks for your support!"
          : "You've removed your upvote",
        duration: 2000,
      });

      // Invalidate the hub query to refresh the data
      queryClient.invalidateQueries({ queryKey: hubKeys.detail(hubId) });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update upvote status: ${error.message}`,
        variant: "destructive",
      });
    },
  });
}

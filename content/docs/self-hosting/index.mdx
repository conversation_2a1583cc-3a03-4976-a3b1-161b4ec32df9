---
title: Self-Hosting
description: Run your own instance of InterChat
icon: HeartHandshake
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Cards, Card } from 'fumadocs-ui/components/card'

# Self-Hosting InterChat

InterChat is open-source and can be self-hosted on your own infrastructure. This section provides guidance on setting up and maintaining your own instance of InterChat.

<Callout type="warning">
  Self-hosting requires technical knowledge and server resources. If you're not comfortable with server administration, consider using the [official hosted](/invite) version of InterChat instead.
</Callout>

## Why Self-Host?

There are several reasons you might want to self-host InterChat:

- Complete control over your data and privacy
- Customization of features to suit your specific needs
- No limitations on usage or connections
- Learning experience for Discord bot development

## Requirements

Before you begin, ensure you have:

- A server or VPS running Linux
- Node.js 22+
- MongoDB database
- Redis server
- Discord bot token and application
- Basic knowledge of command line and server administration

<Cards>
  <Card title="Installation Guide" href="/docs/self-hosting/installation">
    Step-by-step instructions for installing InterChat
  </Card>
  <Card title="Configuration Guide" href="/docs/self-hosting/configuration">
    How to configure your InterChat instance
  </Card>
</Cards>

## License Considerations

InterChat is licensed under the GNU Affero General Public License v3.0 (AGPL-3.0). This means:

- You can use, modify, and distribute the code
- If you modify the code and provide it as a service (even over a network), you **must** make your modifications available under the same license
- You **must** include the original copyright notice and license
- Creating "cheap copies" by rebranding without proper attribution is **strictly prohibited**

<Callout type="warning">
  License violations will be actively pursued through legal channels. We take our intellectual property rights seriously.
</Callout>

<Callout type="info">
  Read our [License Compliance Warning](/docs/LICENSE-DISCLAIMER) and the full [license](https://github.com/Discord-InterChat/InterChat/blob/main/LICENSE) before self-hosting to ensure compliance.
</Callout>

## Support

While we provide documentation for self-hosting, please note:

- Official support is limited for self-hosted instances
- You're responsible for maintaining and securing your own instance
- The community may be able to help with issues in our [support server](https://discord.gg/cgYgC6YZyX)

## Contributing Back

If you make improvements to InterChat while self-hosting, consider contributing them back to the main project:

1. Fork the repository on GitHub
2. Make your changes
3. Submit a pull request

This helps the entire community benefit from your improvements!

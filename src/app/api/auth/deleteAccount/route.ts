import { auth } from "@/auth";
import { signOut } from "next-auth/react";
import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function DELETE() {
	const session = await auth();

	// Only allow authenticated users
	if (!session?.user) {
		return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
	}

	const userId = session.user.id;

	await prisma.account.deleteMany({ where: { id: userId } });
	await prisma.session.deleteMany({ where: { id: userId } });

	await signOut({ callbackUrl: "/login" });

	return NextResponse.json({ message: "Account deleted" });
}

import { auth } from "@/auth";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";

// Schema for updating a connection
const updateConnectionSchema = z.object({
  connected: z.boolean().optional(),
  compact: z.boolean().optional(),
});

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ connectionId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { connectionId } = await props.params;

    const connection = await prisma.connection.findUnique({
      where: { id: connectionId },
      include: { hub: true, server: true },
    });

    if (!connection) {
      return NextResponse.json({ error: "Connection not found" }, { status: 404 });
    }

    // Check if user has permission to view this hub's connections
    const permissionLevel = await getUserHubPermission(session.user.id, connection.hubId);
    if (permissionLevel === PermissionLevel.NONE) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    return NextResponse.json({ connection });
  } catch (error) {
    console.error("Error fetching connection:", error);
    return NextResponse.json(
      { error: "Failed to fetch connection" },
      { status: 500 },
    );
  }
}

export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ connectionId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { connectionId } = await props.params;

    // Get the connection to check permissions
    const connection = await prisma.connection.findUnique({
      where: { id: connectionId },
      select: { hubId: true },
      
    });

    if (!connection) {
      return NextResponse.json({ error: "Connection not found" }, { status: 404 });
    }

    // Check if user has permission to manage this hub's connections
    const permissionLevel = await getUserHubPermission(session.user.id, connection.hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = updateConnectionSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 },
      );
    }

    const { connected, compact } = validation.data;

    const updatedConnection = await prisma.connection.update({
      where: { id: connectionId },
      data: {
        ...(connected !== undefined && { connected }),
        ...(compact !== undefined && { compact }),
      },
      include: {
        hub: true,
        server: true,
      },
    });

    return NextResponse.json({ connection: updatedConnection });
  } catch (error) {
    console.error("Error updating connection:", error);
    return NextResponse.json(
      { error: "Failed to update connection" },
      { status: 500 },
    );
  }
}

export async function DELETE(
  request: NextRequest,
  props: { params: Promise<{ connectionId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { connectionId } = await props.params;

    // Get the connection to check permissions
    const connection = await prisma.connection.findUnique({
      where: { id: connectionId },
      select: { hubId: true },
      
    });

    if (!connection) {
      return NextResponse.json({ error: "Connection not found" }, { status: 404 });
    }

    // Check if user has permission to manage this hub's connections
    const permissionLevel = await getUserHubPermission(session.user.id, connection.hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    await prisma.connection.delete({
      where: { id: connectionId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting connection:", error);
    return NextResponse.json(
      { error: "Failed to delete connection" },
      { status: 500 },
    );
  }
}

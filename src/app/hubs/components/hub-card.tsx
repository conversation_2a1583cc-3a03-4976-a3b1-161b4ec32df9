"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import { ArrowUpRight, Heart, Star, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { memo, useState } from "react";
import { useHubUpvote } from "../hooks/use-hub-upvote";
import JoinButton from "./hub-detail/JoinButton";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";

// Memoize the star rendering component
const StarRating = memo(
  ({
    averageRating,
    totalReviews,
  }: {
    averageRating: number;
    totalReviews: number;
  }) => {
    // Generate star rating elements
    const renderStars = () => {
      const stars = [];
      const fullStars = Math.floor(averageRating);
      const hasHalfStar = averageRating - fullStars >= 0.5;

      // Add filled stars
      for (let i = 0; i < fullStars; i++) {
        stars.push(
          <Star
            key={`star-${i}`}
            className="h-4 w-4 fill-primary text-primary"
            aria-hidden="true"
          />,
        );
      }

      // Add half star if needed
      if (hasHalfStar) {
        stars.push(
          <div key="half-star" className="relative" aria-hidden="true">
            <Star className="h-4 w-4 text-primary" />
            <div className="absolute top-0 left-0 w-1/2 overflow-hidden">
              <Star className="h-4 w-4 fill-primary text-primary" />
            </div>
          </div>,
        );
      }

      // Add empty stars to reach 5
      const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
      for (let i = 0; i < emptyStars; i++) {
        stars.push(
          <Star
            key={`empty-star-${i}`}
            className="h-4 w-4 text-gray-300 dark:text-gray-600"
            aria-hidden="true"
          />,
        );
      }

      return stars;
    };

    if (totalReviews <= 0) {
      return (
        <span className="text-xs text-gray-500 dark:text-gray-400">
          No ratings yet
        </span>
      );
    }

    return (
      <div
        className="flex"
        aria-label={`Rated ${averageRating.toFixed(1)} out of 5 stars based on ${totalReviews} reviews`}
      >
        <div className="flex" aria-hidden="true">
          {renderStars()}
        </div>
        <div className="flex items-center gap-1">
          <span className="ml-1 text-xs font-medium text-gray-700 dark:text-gray-300">
            {averageRating.toFixed(1)}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            ({totalReviews})
          </span>
        </div>
      </div>
    );
  },
);

StarRating.displayName = "StarRating";

// Memoize the HubIcon component
const HubIcon = memo(
  ({
    iconUrl,
    name,
    className = "",
  }: {
    iconUrl: string;
    name: string;
    className?: string;
  }) => (
    <div className={className}>
      {iconUrl ? (
        <div className="relative h-12 w-12 sm:h-14 sm:w-14 md:h-16 md:w-16 rounded-full border-4 border-card shadow-lg transition-transform duration-300 hover:scale-105">
          <Image
            unoptimized
            src={iconUrl}
            alt={`${name} hub icon`}
            fill
            className="rounded-full object-cover"
            priority={false}
            loading="lazy"
          />
        </div>
      ) : (
        <div
          className="flex h-12 w-12 sm:h-14 sm:w-14 md:h-16 md:w-16 items-center justify-center rounded-full bg-primary/20 text-xl sm:text-2xl md:text-3xl font-bold text-primary border-4 border-card shadow-lg transition-transform duration-300 hover:scale-105"
          aria-label={`${name} hub icon (letter ${name.charAt(0).toUpperCase()})`}
        >
          {name.charAt(0).toUpperCase()}
        </div>
      )}
    </div>
  ),
);

HubIcon.displayName = "HubIcon";

// Memoize the TagList component
const TagList = memo(({ tags }: { tags: string[] }) => {
  // Use the useMobile hook for responsive behavior
  const isMobile = useMobile(640); // 640px is the 'sm' breakpoint in Tailwind

  // Show fewer tags on mobile
  const visibleTagsCount = isMobile ? 2 : 3;

  return (
    <div
      className="mt-2 sm:mt-3 flex flex-wrap gap-1.5 sm:gap-2 min-h-[1.75rem] sm:min-h-[2rem]"
      aria-label="Hub tags"
    >
      {/* biome-ignore lint/complexity/useOptionalChain: <explanation> */}
      {tags && tags?.slice(0, visibleTagsCount).map((tag) => (
          <span
            key={tag}
            className="px-1.5 sm:px-2 py-0.5 sm:py-1 text-xs rounded-full bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white transition-colors duration-200 border border-gray-600 truncate max-w-[100px] sm:max-w-[120px]"
            title={tag}
          >
            {tag}
          </span>
        ))}
      {tags?.length > visibleTagsCount && (
        <span
          className="px-1.5 sm:px-2 py-0.5 sm:py-1 text-xs rounded-full bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white transition-colors duration-200 border border-gray-600"
          title={`${tags.length - visibleTagsCount} more tags: ${tags.slice(visibleTagsCount).join(', ')}`}
        >
          +{tags.length - visibleTagsCount} more
        </span>
      )}
    </div>
  );
});

TagList.displayName = "TagList";

// Main component
export function HubCard({ hub }: { hub: SimplifiedHub }) {
  const [isHovering, setIsHovering] = useState(false);
  const { id, name, iconUrl, bannerUrl, description, connections } = hub;
  const { liked, upvoteCount, handleUpvote } = useHubUpvote(id, hub.upvotes);

  return (
    <article
      className="group relative flex flex-col overflow-hidden rounded-xl border border-gray-700/50 bg-gray-900 transition-all duration-300 hover:shadow-xl hover:shadow-primary/20 hover:translate-y-[-2px] hover:border-gray-600/70 cursor-pointer h-full touch-manipulation"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onTouchStart={() => setIsHovering(true)}
      onTouchEnd={() => setTimeout(() => setIsHovering(false), 500)}
    >
      <Link
        href={`/hubs/${id}`}
        className="flex flex-col flex-grow"
        aria-label={`View ${name} hub details`}
      >
        {/* Banner with hover effect */}
        <div className="relative aspect-[16/9]">
          {bannerUrl ? (
            <div className="relative w-full h-full overflow-hidden">
              <div className="absolute inset-0 transition-transform duration-700 ease-in-out group-hover:scale-110">
                <Image
                  unoptimized
                  src={bannerUrl}
                  alt={`${name} hub banner`}
                  fill
                  className="object-cover"
                  priority={false}
                  loading="lazy"
                />
              </div>
              {/* gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-black/30 to-transparent opacity-70 group-hover:opacity-60 transition-opacity duration-300" />

              {/* "View hub" text on hover */}
              {isHovering && (
                <div className="absolute inset-0 flex items-center justify-center opacity-100 transition-opacity duration-300">
                  <span className="flex items-center gap-2 px-4 py-2 bg-primary/90 text-white rounded-full font-medium shadow-lg">
                    View Hub <ArrowUpRight className="h-4 w-4" aria-hidden="true" />
                  </span>
                </div>
              )}
            </div>
          ) : (
            <div className="relative w-full h-full">
              {/* Gradient background with animation */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-primary-alt/10 to-primary/15 group-hover:saturate-150 transition-all duration-500" />

              {/* Grid pattern overlay */}
              <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-card bg-center opacity-15 group-hover:opacity-20 transition-opacity duration-300" />

              {/* Animated gradient blobs */}
              <div className="absolute top-1/4 left-1/5 w-24 h-24 bg-primary/10 rounded-full blur-2xl group-hover:bg-primary/20 transition-colors duration-300" />
              <div
                className="absolute bottom-1/4 right-1/6 w-24 h-24 bg-primary-alt/20 rounded-full blur-2xl animate-pulse group-hover:bg-primary-alt/30 transition-colors duration-300"
                style={{ animationDuration: "8s" }}
                aria-hidden="true"
              />

              {/* "View hub" text on hover */}
              {isHovering && (
                <div className="absolute inset-0 flex items-center justify-center opacity-100 transition-opacity duration-300">
                  <span className="flex items-center gap-2 px-4 py-2 bg-primary/90 text-white rounded-full font-medium shadow-lg">
                    View Hub <ArrowUpRight className="h-4 w-4" aria-hidden="true" />
                  </span>
                </div>
              )}
            </div>
          )}

          <HubIcon
            iconUrl={iconUrl}
            name={name}
            className="absolute bottom-0 left-3 sm:left-4 transform translate-y-1/2 z-10"
          />
        </div>

        {/* Content */}
        <div className="relative flex flex-1 flex-col p-3 sm:p-4 md:p-5 pt-8 sm:pt-9 md:pt-10 flex-grow pb-4 sm:pb-5">
          <header className="flex justify-between items-start">
            <div className="flex flex-col">
              <h3 className="text-base sm:text-lg font-bold text-white group-hover:text-primary transition-colors duration-300 line-clamp-1">
                {name}
              </h3>
              {/* Star Rating */}
              {hub.reviews && (
                <div className="mt-1">
                  <StarRating
                    averageRating={
                      hub.reviews.reduce(
                        (sum, review) => sum + review.rating,
                        0,
                      ) / (hub.reviews.length || 1)
                    }
                    totalReviews={hub.reviews.length}
                  />
                </div>
              )}
            </div>

            <Button
              variant="outline"
              size="sm"
              className={cn(
                "flex items-center gap-1 sm:gap-2 px-2 sm:px-3 h-7 sm:h-8 rounded-full transition-all duration-200",
                "border hover:scale-105 active:scale-95 touch-manipulation",
                liked
                  ? "bg-primary/10 border-primary text-primary hover:bg-primary/20"
                  : "bg-gray-800 border-gray-700 text-gray-300 hover:border-primary/50 hover:text-primary",
              )}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleUpvote();
              }}
              aria-label={liked ? "Remove upvote" : "Upvote this hub"}
              aria-pressed={liked}
            >
              <Heart
                className={cn(
                  "h-4 w-4 transition-all duration-200",
                  liked ? "fill-primary text-primary scale-110" : "scale-100",
                )}
                aria-hidden="true"
              />
              <span className="font-medium">{upvoteCount}</span>
            </Button>
          </header>

          <p className="mt-2 sm:mt-3 line-clamp-2 flex-1 text-xs sm:text-sm text-gray-300 min-h-[2rem] sm:min-h-[2.5rem]">
            {description}
          </p>

          {/* Tags with hover effect */}
          {/* <TagList tags={['test', 'test2']} /> */}

          {/* Stats and Actions */}
          <div className="mt-3 sm:mt-4 flex flex-wrap items-center gap-2 min-h-[2rem] justify-between">
            <div className="flex items-center gap-2 flex-wrap">
              <div
                className="flex items-center gap-1 sm:gap-1.5 text-xs sm:text-sm text-gray-400 hover:text-primary transition-colors duration-200"
                aria-label={`${connections.length} connected servers`}
              >
                <Users className="h-4 w-4 text-blue-400" aria-hidden="true" />
                <span className="font-medium">{connections.length}</span>
                <span className="text-xs">servers</span>
              </div>

              <div
                className="flex items-center gap-1 sm:gap-1.5 text-xs sm:text-sm text-gray-400 hover:text-primary transition-colors duration-200"
                aria-label={`${upvoteCount} upvotes`}
              >
                <Heart
                  className={cn(
                    "h-4 w-4",
                    liked ? "fill-primary text-primary" : "text-yellow-400",
                  )}
                  aria-hidden="true"
                />
                <span className="font-medium">{upvoteCount}</span>
                <span className="text-xs">upvotes</span>
              </div>
            </div>

            <div className="flex mt-2 sm:mt-0 w-full sm:w-auto min-w-[100px]">
              {/* Ensure the button container has enough space */}
              <div className="w-full">
                <JoinButton hubName={name} hubId={id} />
              </div>
            </div>
          </div>
        </div>

        {/* Subtle hover indicator */}
        <div
          className={cn(
            "absolute bottom-0 left-0 w-full h-1 bg-primary scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left",
          )}
        />
      </Link>
    </article>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(HubCard);

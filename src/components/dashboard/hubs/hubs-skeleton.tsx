"use client";

import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export function HubsSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-9 w-32" />
      </div>

      <Tabs defaultValue="owned" className="space-y-4">
        <TabsList className="bg-[#0f1117] border border-gray-800">
          <TabsTrigger
            value="owned"
            className="data-[state=active]:bg-gray-800"
          >
            Owned (...)
          </TabsTrigger>
          <TabsTrigger
            value="managed"
            className="data-[state=active]:bg-gray-800"
          >
            Managed (...)
          </TabsTrigger>
          <TabsTrigger
            value="moderated"
            className="data-[state=active]:bg-gray-800"
          >
            Moderated (...)
          </TabsTrigger>
        </TabsList>

        <TabsContent value="owned" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <HubCardSkeleton key={i} />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export function HubCardSkeleton() {
  return (
    <Card className="border-gray-800 bg-[#0f1117] overflow-hidden flex flex-col">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div>
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-4 w-48 mt-1" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="py-4 flex-grow">
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex justify-between">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-8" />
          </div>
          <div className="flex justify-between">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-8" />
          </div>
          <div className="flex justify-between">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-20" />
          </div>
        </div>
      </CardContent>
      <CardFooter className="pt-0 border-t border-gray-800">
        <Skeleton className="h-9 w-full" />
      </CardFooter>
    </Card>
  );
}

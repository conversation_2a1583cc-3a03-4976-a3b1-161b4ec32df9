import { auth } from "@/auth";
import { getUserHubPermission } from "@/lib/permissions";
import { notFound, redirect } from "next/navigation";
import { ReactNode } from "react";
import { PermissionLevel } from "@/lib/constants";

interface InfractionsLayoutProps {
  children: ReactNode;
  params: Promise<{
    hubId: string;
  }>;
}

export default async function InfractionsLayout({
  children,
  params,
}: InfractionsLayoutProps) {
  const { hubId } = await params;
  const session = await auth();

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/hubs/${hubId}/infractions`);
  }

  // Check if user has manager or owner permissions
  const permissionLevel = await getUserHubPermission(session.user.id, hubId);

  if (permissionLevel < PermissionLevel.MANAGER) {
    // User doesn't have sufficient permissions
    notFound();
  }

  return <>{children}</>;
}

"use client";

import { UnderlinedTabs } from "@/components/dashboard/underlined-tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TabsContent } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import {
  InfractionType,
  Message,
  Report,
  ReportStatus,
} from "@/lib/generated/prisma/client";
import { trpc } from "@/utils/trpc";
import { formatDistanceToNow } from "date-fns";
import {
  <PERSON>ert<PERSON>riangle,
  ArrowLeft,
  Ban,
  CheckCircle,
  Clock,
  MessageSquare,
  Server,
  Shield,
  Users,
  XCircle
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useState, useTransition } from "react";

// Type for user hub from permissions
interface UserHub {
  id: string;
  name: string;
  permissionLevel: number;
}

// Type for report with included relations
interface ReportWithRelations extends Report {
  hub: {
    id: string;
    name: string;
    iconUrl: string | null;
  } | null;
  reporter: {
    id: string;
    name: string | null;
    image: string | null;
  } | null;
  reportedUser: {
    id: string;
    name: string | null;
    image: string | null;
  } | null;
  handler: {
    id: string;
    name: string | null;
    image: string | null;
  } | null;
  messageData: Pick<
    Message,
    | "id"
    | "content"
    | "imageUrl"
    | "channelId"
    | "guildId"
    | "authorId"
    | "createdAt"
    | "reactions"
    | "referredMessageId"
    | "reactions"
    | "referredMessageId"
  > | null;
  serverData: {
    id: string;
    name: string | null;
    iconUrl: string | null;
    inviteCode: string | null;
  } | null;
}

interface ReportsContentProps {
  moderatedHubs: UserHub[];
}

interface ReportCardProps {
  report: ReportWithRelations;
  onAction: (
    reportId: string,
    status: ReportStatus,
    resolution?: string
  ) => Promise<void>;
  onInfractionAction: (
    reportId: string,
    type: InfractionType,
    targetType: "user" | "server",
    reason: string,
    duration?: number
  ) => Promise<void>;
  isUpdating: boolean;
}

export function ReportsContent({ moderatedHubs }: ReportsContentProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const selectedHubId = searchParams.get("hubId") || "all";
  const currentPage = parseInt(searchParams.get("page") || "1");

  // Fetch reports using tRPC
  const {
    data: reportsData,
    isLoading,
    refetch,
  } = trpc.moderation.getReports.useQuery({
    hubId: selectedHubId === "all" ? undefined : selectedHubId,
    page: currentPage,
    limit: 10,
  });

  const updateReportMutation = trpc.moderation.updateReportStatus.useMutation({
    onSuccess: (data) => {
      refetch();
      toast({
        title: "Report Updated",
        description: `Report has been ${data.report.status.toLowerCase()} successfully.`,
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update report status.",
        variant: "destructive",
      });
    },
  });

  const createInfractionMutation =
    trpc.moderation.createInfractionFromReport.useMutation({
      onSuccess: (data) => {
        refetch();
        const actionType =
          data.infraction.type === InfractionType.WARNING
            ? "Warning issued"
            : "User/Server blacklisted";
        toast({
          title: "Action Completed",
          description: `${actionType} successfully. Report has been resolved.`,
          variant: "default",
        });
      },
      onError: (error) => {
        toast({
          title: "Error",
          description: error.message || "Failed to create infraction.",
          variant: "destructive",
        });
      },
    });

  const handleHubChange = (hubId: string) => {
    const params = new URLSearchParams(searchParams);
    if (hubId === "all") {
      params.delete("hubId");
    } else {
      params.set("hubId", hubId);
    }
    params.delete("page"); // Reset to first page when changing hub
    router.push(`/dashboard/moderation/reports?${params.toString()}`);
  };

  const handleReportAction = async (
    reportId: string,
    status: ReportStatus,
  ) => {
    try {
      await updateReportMutation.mutateAsync({
        reportId,
        status,
      });
    } catch (error) {
      console.error("Failed to update report:", error);
    }
  };

  const handleInfractionAction = async (
    reportId: string,
    type: InfractionType,
    targetType: "user" | "server",
    reason: string,
    duration?: number
  ) => {
    try {
      await createInfractionMutation.mutateAsync({
        reportId,
        type,
        targetType,
        reason,
        duration,
      });
    } catch (error) {
      console.error("Failed to create infraction:", error);
    }
  };

  if (isLoading) {
    return <ReportsLoadingSkeleton />;
  }

  const reports = reportsData?.reports || [];
  const pendingReports = reports.filter(
    (report) => report.status === ReportStatus.PENDING
  );
  const resolvedReports = reports.filter(
    (report) => report.status === ReportStatus.RESOLVED
  );
  const ignoredReports = reports.filter(
    (report) => report.status === ReportStatus.IGNORED
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button variant="ghost" size="sm" className="mr-2" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Reports Dashboard
              </h1>
              <p className="text-gray-400 mt-1">
                Manage and review reports from your moderated hubs
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <label className="text-sm text-gray-400">Filter by Hub:</label>
            <Select value={selectedHubId} onValueChange={handleHubChange}>
              <SelectTrigger className="w-[200px] bg-gray-900/50 border-gray-800">
                <SelectValue placeholder="Select Hub" />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-gray-800">
                <SelectItem value="all">All Hubs</SelectItem>
                {moderatedHubs.map((hub) => (
                  <SelectItem key={hub.id} value={hub.id}>
                    {hub.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex items-center gap-2 text-sm text-gray-400">
          <span>Total: {reportsData?.total || 0} reports</span>
        </div>
      </div>

      <UnderlinedTabs
        defaultValue="pending"
        className="space-y-6"
        tabs={[
          {
            value: "pending",
            label: (
              <div className="flex items-center gap-2">
                <span>Pending</span>
                {pendingReports.length > 0 && (
                  <Badge variant="destructive" className="h-5 w-5 p-0 text-xs">
                    {pendingReports.length}
                  </Badge>
                )}
              </div>
            ),
            color: "red",
            icon: <Clock className="h-4 w-4" />,
          },
          {
            value: "resolved",
            label: `Resolved (${resolvedReports.length})`,
            color: "green",
            icon: <CheckCircle className="h-4 w-4" />,
          },
          {
            value: "ignored",
            label: `Ignored (${ignoredReports.length})`,
            color: "blue",
            icon: <XCircle className="h-4 w-4" />,
          },
        ]}
      >
        <TabsContent value="pending" className="space-y-4">
          {pendingReports.length > 0 ? (
            <div className="space-y-4">
              {pendingReports.map((report) => (
                <ReportCard
                  key={report.id}
                  report={report}
                  onAction={handleReportAction}
                  onInfractionAction={handleInfractionAction}
                  isUpdating={
                    updateReportMutation.isPending ||
                    createInfractionMutation.isPending
                  }
                />
              ))}
            </div>
          ) : (
            <EmptyState
              title="No Pending Reports"
              description="There are no pending reports that require your attention."
              message="All reports have been resolved. Great job keeping your hubs safe!"
            />
          )}
        </TabsContent>

        <TabsContent value="resolved" className="space-y-4">
          {resolvedReports.length > 0 ? (
            <div className="space-y-4">
              {resolvedReports.map((report) => (
                <ReportCard
                  key={report.id}
                  report={report}
                  onAction={handleReportAction}
                  onInfractionAction={handleInfractionAction}
                  isUpdating={
                    updateReportMutation.isPending ||
                    createInfractionMutation.isPending
                  }
                />
              ))}
            </div>
          ) : (
            <EmptyState
              title="No Resolved Reports"
              description="There are no resolved reports to display."
              message="When you resolve reports, they will appear here for reference."
            />
          )}
        </TabsContent>

        <TabsContent value="ignored" className="space-y-4">
          {ignoredReports.length > 0 ? (
            <div className="space-y-4">
              {ignoredReports.map((report) => (
                <ReportCard
                  key={report.id}
                  report={report}
                  onAction={handleReportAction}
                  onInfractionAction={handleInfractionAction}
                  isUpdating={
                    updateReportMutation.isPending ||
                    createInfractionMutation.isPending
                  }
                />
              ))}
            </div>
          ) : (
            <EmptyState
              title="No Ignored Reports"
              description="There are no ignored reports to display."
              message="Reports you choose to ignore will appear here."
            />
          )}
        </TabsContent>
      </UnderlinedTabs>
    </div>
  );
}

interface EmptyStateProps {
  title: string;
  description: string;
  message: string;
}

function EmptyState({ title, description, message }: EmptyStateProps) {
  return (
    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-gray-400">{message}</p>
      </CardContent>
    </Card>
  );
}

function ReportCard({
  report,
  onAction,
  onInfractionAction,
  isUpdating,
}: ReportCardProps) {
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false);
  const [isInfractionDialogOpen, setIsInfractionDialogOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>("");
  const [selectedInfractionType, setSelectedInfractionType] =
    useState<InfractionType | null>(null);
  const [selectedTargetType, setSelectedTargetType] = useState<
    "user" | "server"
  >("user");
  const [resolution, setResolution] = useState("");
  const [infractionReason, setInfractionReason] = useState("");
  const [duration, setDuration] = useState<string>("");
  const [isPending, startTransition] = useTransition();

  const timeAgo = formatDistanceToNow(new Date(report.createdAt), {
    addSuffix: true,
  });

  const getStatusBadge = (status: ReportStatus) => {
    switch (status) {
      case ReportStatus.PENDING:
        return (
          <Badge
            variant="outline"
            className="border-yellow-500/50 text-yellow-400 bg-yellow-500/10"
          >
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case ReportStatus.RESOLVED:
        return (
          <Badge
            variant="outline"
            className="border-green-500/50 text-green-400 bg-green-500/10"
          >
            <CheckCircle className="h-3 w-3 mr-1" />
            Resolved
          </Badge>
        );
      case ReportStatus.IGNORED:
        return (
          <Badge
            variant="outline"
            className="border-gray-500/50 text-gray-400 bg-gray-500/10"
          >
            <XCircle className="h-3 w-3 mr-1" />
            Ignored
          </Badge>
        );
    }
  };

  const handleAction = (action: string) => {
    setSelectedAction(action);
    setIsActionDialogOpen(true);
  };

  const handleInfractionAction = (
    type: InfractionType,
    targetType: "user" | "server"
  ) => {
    setSelectedInfractionType(type);
    setSelectedTargetType(targetType);
    setInfractionReason(report.reason); // Pre-fill with report reason
    setIsInfractionDialogOpen(true);
  };

  const executeAction = () => {
    startTransition(async () => {
      try {
        let status: ReportStatus;
        let resolutionText = resolution;

        switch (selectedAction) {
          case "resolve":
            status = ReportStatus.RESOLVED;
            resolutionText = resolution || "Report resolved by moderator";
            break;
          case "ignore":
            status = ReportStatus.IGNORED;
            resolutionText = "Report ignored by moderator";
            break;
          default:
            return;
        }

        await onAction(report.id, status, resolutionText);
        setIsActionDialogOpen(false);
        setResolution("");
        setSelectedAction("");
      } catch (error) {
        console.error("Failed to execute action:", error);
      }
    });
  };

  const executeInfractionAction = () => {
    if (!selectedInfractionType) return;

    startTransition(async () => {
      try {
        // Parse duration if provided
        let durationInSeconds: number | undefined;
        if (duration) {
          const durationMatch = duration.match(/^(\d+)([dhm])$/);
          if (durationMatch) {
            const value = parseInt(durationMatch[1]);
            const unit = durationMatch[2];
            switch (unit) {
              case "d":
                durationInSeconds = value * 24 * 60 * 60;
                break;
              case "h":
                durationInSeconds = value * 60 * 60;
                break;
              case "m":
                durationInSeconds = value * 60;
                break;
            }
          }
        }

        await onInfractionAction(
          report.id,
          selectedInfractionType,
          selectedTargetType,
          infractionReason,
          durationInSeconds
        );

        // Reset state
        setIsInfractionDialogOpen(false);
        setSelectedInfractionType(null);
        setInfractionReason("");
        setDuration("");
      } catch (error) {
        console.error("Failed to execute infraction action:", error);
      }
    });
  };

  return (
    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm hover:border-gray-700/50 transition-colors">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Image
              src={
                report.hub?.iconUrl ||
                "https://api.dicebear.com/7.x/shapes/svg?seed=hub"
              }
              alt={report.hub?.name || "Hub"}
              width={32}
              height={32}
              className="rounded-full border border-gray-700"
              unoptimized
            />
            <div>
              <CardTitle className="text-lg">
                {report.hub?.name || "Unknown Hub"}
              </CardTitle>
              <CardDescription>Reported {timeAgo}</CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge(report.status)}
          </div>
        </div>

        {/* Server Origin Information */}
        <div className="mt-3 p-3 rounded-md bg-blue-900/20 border border-blue-800/50">
          <div className="text-sm text-blue-300 mb-2 font-medium">
            Report Origin
          </div>
          <div className="flex items-center gap-3">
            <Image
              src={
                report.serverData?.iconUrl ||
                `https://api.dicebear.com/7.x/identicon/svg?seed=${encodeURIComponent(
                  report.reportedServerId
                )}`
              }
              alt="Server Icon"
              width={24}
              height={24}
              className="rounded-full"
              unoptimized
            />
            <div className="flex-1">
              <div className="text-sm font-medium text-blue-200">
                {report.serverData?.name || "Unknown Server"}
              </div>
              <div className="text-xs text-blue-400">
                Server ID: {report.reportedServerId}
              </div>
            </div>
            {report.messageId && (
              <div className="text-xs text-blue-400">
                Message ID: {report.messageId}
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Reporter and Reported User */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Image
              src={
                report.reportedUser?.image ||
                "https://api.dicebear.com/7.x/shapes/svg?seed=user"
              }
              alt={report.reportedUser?.name || "Unknown User"}
              width={40}
              height={40}
              className="rounded-full border border-red-500/20"
              unoptimized
            />
            <div>
              <div className="font-medium text-red-400">
                {report.reportedUser?.name || "Unknown User"}
              </div>
              <div className="text-xs text-gray-400">Reported User</div>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="font-medium text-blue-400">
                {report.reporter?.name || "Unknown Reporter"}
              </div>
              <div className="text-xs text-gray-400">Reporter</div>
            </div>
            <Image
              src={
                report.reporter?.image ||
                "https://api.dicebear.com/7.x/shapes/svg?seed=reporter"
              }
              alt={report.reporter?.name || "Unknown Reporter"}
              width={40}
              height={40}
              className="rounded-full border border-blue-500/20"
              unoptimized
            />
          </div>
        </div>

        {/* Report Reason */}
        <div className="p-3 rounded-md bg-gray-900/50 border border-gray-800">
          <div className="text-sm mb-2 text-gray-400 flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Reason for Report:
          </div>
          <div className="text-sm">{report.reason}</div>
        </div>

        {/* Message Information */}
        {report.messageId && (
          <div className="p-3 rounded-md bg-gray-800/50 border border-gray-700">
            <div className="text-sm mb-3 text-gray-400 flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Reported Message
            </div>

            {/* Message Metadata */}
            <div className="space-y-2 mb-3">
              <div className="text-xs text-gray-400">
                Message ID:{" "}
                <span className="font-mono text-gray-300">
                  {report.messageId}
                </span>
              </div>
              {report.messageData && (
                <>
                  <div className="text-xs text-gray-400">
                    Channel ID:{" "}
                    <span className="font-mono text-gray-300">
                      {report.messageData.channelId}
                    </span>
                  </div>
                  <div className="text-xs text-gray-400">
                    Sent:{" "}
                    {new Date(report.messageData.createdAt).toLocaleString()}
                  </div>
                </>
              )}
            </div>

            {/* Message Content */}
            {report.messageData ? (
              <div className="space-y-2">
                {report.messageData.content && (
                  <div className="bg-gray-900/50 p-3 rounded border border-gray-700">
                    <div className="text-gray-400 text-xs mb-2">
                      Message Content:
                    </div>
                    <div className="text-gray-200 whitespace-pre-wrap break-words">
                      {report.messageData.content}
                    </div>
                  </div>
                )}

                {report.messageData.imageUrl && (
                  <div className="bg-gray-900/50 p-3 rounded border border-gray-700">
                    <div className="text-gray-400 text-xs mb-2">
                      Attachment:
                    </div>
                    <Image
                      src={report.messageData.imageUrl}
                      alt="Message attachment"
                      width={300}
                      height={200}
                      className="rounded border border-gray-600 max-w-full h-auto"
                      unoptimized
                    />
                  </div>
                )}

                {report.messageData.referredMessageId && (
                  <div className="text-xs text-gray-400">
                    Reply to:{" "}
                    <span className="font-mono text-gray-300">
                      {report.messageData.referredMessageId}
                    </span>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-sm text-gray-400 italic">
                Message content not available (may have been deleted)
              </div>
            )}
          </div>
        )}

        {/* Resolution (for resolved/ignored reports) */}
        {(report.status === ReportStatus.RESOLVED ||
          report.status === ReportStatus.IGNORED) &&
          report.handler && (
            <div className="p-3 rounded-md bg-green-900/20 border border-green-800/50">
              <div className="text-sm mb-2 text-gray-400">
                {report.status === ReportStatus.RESOLVED
                  ? "Resolution:"
                  : "Ignored by:"}
              </div>
              <div className="text-sm">
                Handled by{" "}
                <span className="font-medium text-green-400">
                  {report.handler.name}
                </span>
                {report.handledAt && (
                  <span className="text-gray-400 ml-2">
                    {formatDistanceToNow(new Date(report.handledAt), {
                      addSuffix: true,
                    })}
                  </span>
                )}
              </div>
            </div>
          )}

        {/* Action Buttons */}
        <div className="space-y-3 pt-2">
          {report.status === ReportStatus.PENDING ? (
            <>
              {/* Moderation Actions */}
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-yellow-600/50 text-yellow-400 hover:bg-yellow-600/10"
                  onClick={() =>
                    handleInfractionAction(InfractionType.WARNING, "user")
                  }
                  disabled={isUpdating || isPending}
                >
                  <Shield className="h-4 w-4 mr-2" />
                  Warn User
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-red-600/50 text-red-400 hover:bg-red-600/10"
                  onClick={() =>
                    handleInfractionAction(InfractionType.BLACKLIST, "user")
                  }
                  disabled={isUpdating || isPending}
                >
                  <Ban className="h-4 w-4 mr-2" />
                  Blacklist User
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-red-600/50 text-red-400 hover:bg-red-600/10"
                  onClick={() =>
                    handleInfractionAction(InfractionType.BLACKLIST, "server")
                  }
                  disabled={isUpdating || isPending}
                >
                  <Server className="h-4 w-4 mr-2" />
                  Blacklist Server
                </Button>
              </div>

              {/* Basic Actions */}
              <div className="flex gap-2 justify-end">
                <Button
                  variant="outline"
                  className="border-gray-700 hover:border-gray-600"
                  onClick={() => handleAction("ignore")}
                  disabled={isUpdating || isPending}
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Ignore
                </Button>
                <Button
                  onClick={() => handleAction("resolve")}
                  disabled={isUpdating || isPending}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-600/80 hover:to-emerald-600/80 border-none"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Resolve
                </Button>
              </div>
            </>
          ) : (
            <div className="flex gap-2 justify-end">
              <Button variant="outline" className="border-gray-700" asChild>
                <Link
                  href={`/dashboard/hubs/${report.hubId}/infractions?userId=${report.reportedUserId}`}
                >
                  <Users className="h-4 w-4 mr-2" />
                  View Past Infractions
                </Link>
              </Button>
            </div>
          )}
        </div>
      </CardContent>

      {/* Action Dialog */}
      <Dialog open={isActionDialogOpen} onOpenChange={setIsActionDialogOpen}>
        <DialogContent className="bg-gray-900 border-gray-800">
          <DialogHeader>
            <DialogTitle>
              {selectedAction === "resolve"
                ? "Resolve Report"
                : "Ignore Report"}
            </DialogTitle>
            <DialogDescription>
              {selectedAction === "resolve"
                ? "Provide details about how this report was resolved."
                : "This report will be marked as ignored and moved to the ignored tab."}
            </DialogDescription>
          </DialogHeader>

          {selectedAction === "resolve" && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-300 mb-2 block">
                  Resolution Details (Optional)
                </label>
                <Textarea
                  value={resolution}
                  onChange={(e) => setResolution(e.target.value)}
                  placeholder="Describe the action taken or resolution details..."
                  className="bg-gray-800 border-gray-700 text-gray-100"
                  rows={3}
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsActionDialogOpen(false)}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={executeAction}
              disabled={isPending}
              className={
                selectedAction === "resolve"
                  ? "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-600/80 hover:to-emerald-600/80 border-none"
                  : "bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-600/80 hover:to-gray-700/80 border-none"
              }
            >
              {isPending
                ? "Processing..."
                : selectedAction === "resolve"
                ? "Resolve"
                : "Ignore"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Infraction Dialog */}
      <Dialog
        open={isInfractionDialogOpen}
        onOpenChange={setIsInfractionDialogOpen}
      >
        <DialogContent className="bg-gray-900 border-gray-800 max-w-md">
          <DialogHeader>
            <DialogTitle>
              {selectedInfractionType === InfractionType.WARNING
                ? "Issue Warning"
                : "Blacklist"}{" "}
              {selectedTargetType === "user" ? "User" : "Server"}
            </DialogTitle>
            <DialogDescription>
              {selectedInfractionType === InfractionType.WARNING
                ? `Issue a warning to the reported ${selectedTargetType}.`
                : `Blacklist the reported ${selectedTargetType} from this hub.`}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-300 mb-2 block">
                Reason
              </label>
              <Textarea
                value={infractionReason}
                onChange={(e) => setInfractionReason(e.target.value)}
                placeholder="Reason for the infraction..."
                className="bg-gray-800 border-gray-700 text-gray-100"
                rows={3}
                required
              />
            </div>

            {selectedInfractionType === InfractionType.BLACKLIST && (
              <div>
                <label className="text-sm font-medium text-gray-300 mb-2 block">
                  Duration (Optional)
                </label>
                <input
                  type="text"
                  value={duration}
                  onChange={(e) => setDuration(e.target.value)}
                  placeholder="e.g., 7d, 24h, 30m (leave empty for permanent)"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Format: number + unit (d=days, h=hours, m=minutes)
                </p>
              </div>
            )}

            <div className="p-3 rounded-md bg-blue-900/20 border border-blue-800/50">
              <div className="text-sm text-blue-300">
                <strong>Target:</strong>{" "}
                {selectedTargetType === "user"
                  ? report.reportedUser?.name
                  : "Server"}
                {selectedTargetType === "user"
                  ? ` (${report.reportedUserId})`
                  : ` (${report.reportedServerId})`}
              </div>
              <div className="text-sm text-blue-300 mt-1">
                <strong>Hub:</strong> {report.hub?.name}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsInfractionDialogOpen(false)}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={executeInfractionAction}
              disabled={isPending || !infractionReason.trim()}
              className={
                selectedInfractionType === InfractionType.WARNING
                  ? "bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-600/80 hover:to-orange-600/80 border-none"
                  : "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-600/80 hover:to-red-700/80 border-none"
              }
            >
              {isPending
                ? "Processing..."
                : selectedInfractionType === InfractionType.WARNING
                ? "Issue Warning"
                : "Blacklist"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

function ReportsLoadingSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="h-10 w-48 bg-gray-800 rounded animate-pulse" />
        <div className="h-5 w-32 bg-gray-800 rounded animate-pulse" />
      </div>

      <div className="space-y-4">
        <div className="h-10 w-full bg-gray-800 rounded animate-pulse" />
        {Array.from({ length: 3 }).map((_, i) => (
          <Card
            key={i}
            className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm"
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 bg-gray-700 rounded-full animate-pulse" />
                  <div className="h-5 w-24 bg-gray-700 rounded animate-pulse" />
                </div>
                <div className="h-5 w-16 bg-gray-700 rounded animate-pulse" />
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="h-16 w-full bg-gray-700 rounded animate-pulse" />
                <div className="h-12 w-full bg-gray-700 rounded animate-pulse" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

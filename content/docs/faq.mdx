---
title: Frequently Asked Questions
description: Common questions about InterChat
icon: <PERSON>Help
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Accordions, Accordion } from 'fumadocs-ui/components/accordion'

# Frequently Asked Questions

Find answers to common questions about InterChat below.

## General Questions

<Accordions>
<Accordion title="What is InterChat?">
  InterChat is a Discord bot that enables cross-server communication through a system of hubs and connections. It allows communities to connect and interact in real-time across different Discord servers.
</Accordion>

<Accordion title="Is InterChat free to use?">
  Yes, InterChat is completely free to use. We do offer a [Ko-fi page](https://ko-fi.com/interchat) for donations to support development and hosting costs, but all features are available for free.
</Accordion>

<Accordion title="How many servers can join a hub?">
  There is no hard limit on the number of servers that can join a hub. However, for performance reasons, we recommend keeping it under 100 servers per hub.
</Accordion>

<Accordion title="Is InterChat open source?">
  Yes, InterChat is open source and licensed under the GNU AGPL-3.0 license. You can find the source code on [GitHub](https://github.com/discord-interchat/InterChat).
</Accordion>
</Accordions>

## Setup and Configuration

<Accordions>
<Accordion title="How do I add InterChat to my server?">
  Visit [interchat.tech/invite](https://interchat.tech/invite) to add the bot to your Discord server. Make sure to grant all the required permissions for the bot to function properly.
</Accordion>

<Accordion title="What permissions does InterChat need?">
  InterChat requires the following key permissions:
  - Manage Webhooks (to create connections)
  - Send Messages (to relay messages)
  - Read Messages (to see messages to relay)
  - Manage Messages (for moderation features)
  
  The invite link automatically requests the necessary permissions.
</Accordion>

<Accordion title="Can I connect multiple channels in my server to the same hub?">
  No, each channel can only be connected to one hub at a time, and you cannot connect multiple channels to the same hub from a single server. This is to prevent message loops and confusion.
</Accordion>

<Accordion title="How do I create a hub?">
  Use the `/hub create` command to create a new hub. You'll need to provide a name and description for your hub. Once created, you'll become the hub owner.
</Accordion>
</Accordions>
## Features and Usage

<Accordions>
<Accordion title="How do messages get relayed between servers?">
  InterChat uses Discord's webhook system to relay messages. When a message is sent in a connected channel, the bot:
  1. Captures the message content, attachments, and author information
  2. Creates webhook messages in all other connected channels
  3. Preserves the original author's username and avatar
  
  This creates a smooth experience where messages appear to come directly from the original author.
</Accordion>

<Accordion title="Can users from different servers see each other's profiles?">
  Users can see each other's usernames and avatars, but they cannot directly interact with profiles from other servers (like viewing full profiles or sending friend requests) unless they share a mutual server.
</Accordion>

<Accordion title="What happens if someone posts inappropriate content?">
  InterChat provides several layers of protection:
  
  1. Built-in content filtering (configurable by hub owners)
  2. NSFW content detection
  3. User reporting system
  4. Blacklisting system for moderators to ban problematic users
  
  Hub moderators can also delete inappropriate messages and blacklist users who violate the rules.
</Accordion>

<Accordion title="Can I customize how messages appear?">
  Yes, connections have a "compact mode" option that simplifies the appearance of messages. You can toggle this with the `/connection edit` command.
</Accordion>
</Accordions>

## Moderation and Security

<Accordions>
<Accordion title="How do I moderate my hub?">
  As a hub owner, you can:
  
  1. Add moderators with `/hub moderator add`
  2. Set up logging channels with `/hub config logging`
  3. Configure content filters with `/hub config anti-swear`
  4. Blacklist problematic users with `/blacklist user`
  5. Set hub rules with `/hub config rules`
  
  Moderators can delete messages, blacklist users, and handle reports.
</Accordion>

<Accordion title="Can users from other servers see my server's private channels?">
  No. Users can only see messages that are sent in the specific channel that's connected to the hub. They cannot see any other channels in your server, including private ones.
</Accordion>

<Accordion title="What happens if I blacklist a user?">
  When you blacklist a user:
  
  1. Their messages will no longer appear in any channels connected to your hub
  2. They cannot join your hub with new connections
  3. The blacklist can be temporary or permanent
  4. The action is logged in your moderation logs
</Accordion>

<Accordion title="Can server admins override hub moderator actions?">
  Server administrators always have full control over their own server's connections. They can disconnect from a hub at any time.
  
  However, they cannot override hub-wide moderation actions like blacklists. If a user is blacklisted from a hub, server admins cannot allow that user's messages to appear in the hub.
</Accordion>
</Accordions>

## Troubleshooting

<Accordions>
<Accordion title="Messages aren't being sent or received. What should I do?">
  Try these troubleshooting steps:
  
  1. Check if the connection is paused with `/connection list`
  2. Verify that the bot has the necessary permissions
  3. Try editing the connection with `/connection edit` to refresh the webhook
  4. If problems persist, try disconnecting and creating a new connection
</Accordion>

<Accordion title="How do I report a bug or suggest a feature?">
  Join our [support server](https://discord.gg/cgYgC6YZyX) to report bugs or suggest features. You can also open an issue on our [GitHub repository](https://github.com/discord-interchat/InterChat/issues).
</Accordion>

<Accordion title="The bot is not responding to commands. What should I do?">
  If the bot is not responding to commands:
  
  1. Check if the bot is online
  2. Verify that the bot has the necessary permissions
  3. Try using slash commands instead of prefix commands
  4. Restart your Discord client
  5. If problems persist, join our support server for assistance
</Accordion>
</Accordions>
<Callout type="info">
  If your question isn't answered here, join our [support server](https://discord.gg/cgYgC6YZyX) for help!
</Callout>

"use client";

import {
  type PaginationInfo,
  type SimplifiedHub,
  useInfiniteHubs,
} from "@/hooks/use-infinite-hubs";
import debounce from "lodash/debounce";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { memo, useCallback, useEffect, useState, useTransition } from "react";
import { useInView } from "react-intersection-observer";
import { SortOptions, ContentFilter, VerificationStatus } from "../constants";
import type { ExcludedSortOptions } from "./hub-filters";
import { HubResults } from "./hub-results";

// --- Memoized Helper Components ---
const LoadingIndicator = memo(() => (
  <div className="flex justify-center items-center py-6 h-16">
    <div
      className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"
      role="status"
      aria-label="Loading more hubs"
    />
  </div>
));
LoadingIndicator.displayName = "LoadingIndicator";

const EndOfResults = memo(() => (
  <div className="text-center text-muted-foreground py-6 text-sm">
    You&apos;ve reached the end.
  </div>
));
EndOfResults.displayName = "EndOfResults";

// --- Main HubGrid Component ---
export const HubGrid = memo(
  ({
    initialHubs,
  }: {
    initialHubs: SimplifiedHub[];
    pagination: PaginationInfo;
  }) => {
    const searchParams = useSearchParams();
    const router = useRouter();
    const pathname = usePathname();
    const [isNavigating, startTransition] = useTransition(); // For URL updates

    // Filter States (mirrored from URL) - keep these for controlled components in HubFilters
    const [searchTerm, setSearchTerm] = useState(
      searchParams.get("search") || ""
    );
    const [selectedTags, setSelectedTags] = useState<string[]>(
      searchParams.get("tags")?.split(",").filter(Boolean) || []
    );
    const [sortBy, setSortBy] = useState<ExcludedSortOptions>(
      (searchParams.get("sort") as ExcludedSortOptions | null) ||
        SortOptions.Trending
    );

    // New filter states
    const [contentFilter, setContentFilter] = useState<ContentFilter>(
      (searchParams.get("contentFilter") as ContentFilter) || ContentFilter.All
    );
    const [verificationStatus, setVerificationStatus] = useState<VerificationStatus>(
      (searchParams.get("verificationStatus") as VerificationStatus) || VerificationStatus.All
    );
    const [language, setLanguage] = useState<string | undefined>(
      searchParams.get("language") || undefined
    );
    const [region, setRegion] = useState<string | undefined>(
      searchParams.get("region") || undefined
    );
    const [minMembers, setMinMembers] = useState<number | undefined>(
      searchParams.get("minMembers") ? Number.parseInt(searchParams.get("minMembers") || "0", 10) : undefined
    );
    const [maxMembers, setMaxMembers] = useState<number | undefined>(
      searchParams.get("maxMembers") ? Number.parseInt(searchParams.get("maxMembers") || "0", 10) : undefined
    );

    // Track if an explicit search/filter action was performed to differentiate initial state from no results
    const [hasFiltered, setHasFiltered] = useState(
      !!searchParams.get("search") ||
        !!searchParams.get("tags") ||
        !!searchParams.get("sort") ||
        !!searchParams.get("contentFilter") ||
        !!searchParams.get("verificationStatus") ||
        !!searchParams.get("language") ||
        !!searchParams.get("region") ||
        !!searchParams.get("minMembers") ||
        !!searchParams.get("maxMembers")
    );

    // State for filtering
    const [isFiltering, setIsFiltering] = useState(false);

    // Use Tanstack Query for infinite scrolling
    const {
      hubs: fetchedHubs,
      hasMore,
      fetchNextPage,
      isFetchingNextPage,
      isLoading,
    } = useInfiniteHubs({
      search: searchTerm,
      tags: selectedTags,
      sort: sortBy as SortOptions,
      contentFilter,
      verificationStatus,
      language,
      region,
      minMembers,
      maxMembers,
      limit: 12,
    });

    // Combine initial hubs with fetched hubs, ensuring no duplicates
    const hubs = [...initialHubs].concat(
      fetchedHubs.filter(
        (hub) => !initialHubs.some((initialHub) => initialHub.id === hub.id)
      )
    );

    // --- Intersection Observer for Infinite Scroll ---
    const { ref: loadMoreRef, inView } = useInView({
      threshold: 0.1, // Trigger slightly after entering viewport
      rootMargin: "300px 0px", // Load content 300px before it becomes visible vertically
      triggerOnce: false, // Keep observing
    });

    // Trigger fetchNextPage when observer is in view
    useEffect(() => {
      if (
        inView &&
        hasMore &&
        !isFetchingNextPage &&
        !isFiltering &&
        !isNavigating
      ) {
        fetchNextPage();
      }
    }, [
      inView,
      hasMore,
      fetchNextPage,
      isFetchingNextPage,
      isFiltering,
      isNavigating,
    ]);

    // Effect to update filter state when URL changes
    useEffect(() => {
      setIsFiltering(false); // Reset filtering state after navigation completes & data arrives
      setHasFiltered(
        !!searchParams.get("search") ||
          !!searchParams.get("tags") ||
          !!searchParams.get("sort") ||
          !!searchParams.get("contentFilter") ||
          !!searchParams.get("verificationStatus") ||
          !!searchParams.get("language") ||
          !!searchParams.get("region") ||
          !!searchParams.get("minMembers") ||
          !!searchParams.get("maxMembers")
      );

      // Update local filter state to match URL after navigation
      setSearchTerm(searchParams.get("search") || "");
      setSelectedTags(
        searchParams.get("tags")?.split(",").filter(Boolean) || []
      );
      setSortBy(
        (searchParams.get("sort") as ExcludedSortOptions | null) ||
          SortOptions.Trending
      );

      // Update new filter states
      setContentFilter(
        (searchParams.get("contentFilter") as ContentFilter) || ContentFilter.All
      );
      setVerificationStatus(
        (searchParams.get("verificationStatus") as VerificationStatus) || VerificationStatus.All
      );
      setLanguage(searchParams.get("language") || undefined);
      setRegion(searchParams.get("region") || undefined);
      setMinMembers(
        searchParams.get("minMembers")
          ? Number.parseInt(searchParams.get("minMembers") || "0", 10)
          : undefined
      );
      setMaxMembers(
        searchParams.get("maxMembers")
          ? Number.parseInt(searchParams.get("maxMembers") || "0", 10)
          : undefined
      );
    }, [searchParams]); // Depend on searchParams only

    // Debounced function to trigger navigation on filter changes
    const triggerFilterUpdate = useCallback(
      debounce((
        newSearch: string,
        newTags: string[],
        newSort: SortOptions,
        newContentFilter: ContentFilter,
        newVerificationStatus: VerificationStatus,
        newLanguage?: string,
        newRegion?: string,
        newMinMembers?: number,
        newMaxMembers?: number
      ) => {
        setIsFiltering(true); // Indicate filtering is in progress

        const params = new URLSearchParams(searchParams); // Preserve existing params initially

        // Basic filters
        if (newSearch) params.set("search", newSearch);
        else params.delete("search");
        if (newTags.length) params.set("tags", newTags.join(","));
        else params.delete("tags");
        if (newSort && newSort !== SortOptions.Trending)
          params.set("sort", newSort);
        else params.delete("sort");

        // New filters
        if (newContentFilter !== ContentFilter.All)
          params.set("contentFilter", newContentFilter);
        else params.delete("contentFilter");

        if (newVerificationStatus !== VerificationStatus.All)
          params.set("verificationStatus", newVerificationStatus);
        else params.delete("verificationStatus");

        if (newLanguage) params.set("language", newLanguage);
        else params.delete("language");

        if (newRegion) params.set("region", newRegion);
        else params.delete("region");

        if (newMinMembers !== undefined)
          params.set("minMembers", newMinMembers.toString());
        else params.delete("minMembers");

        if (newMaxMembers !== undefined)
          params.set("maxMembers", newMaxMembers.toString());
        else params.delete("maxMembers");

        params.delete("skip"); // Always reset pagination on new filter

        startTransition(() => {
          // Using push preserves history, replace might be better if you don't want filter changes in history
          router.push(`${pathname}?${params.toString()}`, { scroll: false }); // Prevent scroll jump on filter
        });
        // Note: isLoading is set to false in the useEffect that handles initialHubs update
      }, 500), // Adjust debounce timing as needed (500ms is usually good for typing)
      [pathname, router] // These are needed for navigation
    );

    // Clear all filters and navigate to base path
    const clearFilters = useCallback(() => {
      triggerFilterUpdate.cancel();
      setSearchTerm("");
      setSelectedTags([]);
      setSortBy(SortOptions.Trending);
      setContentFilter(ContentFilter.All);
      setVerificationStatus(VerificationStatus.All);
      setLanguage(undefined);
      setRegion(undefined);
      setMinMembers(undefined);
      setMaxMembers(undefined);
      setIsFiltering(true); // Show loading while navigating
      setHasFiltered(false);

      startTransition(() => {
        router.push(pathname, { scroll: false }); // Go to path without params
      });
    }, [pathname, router, triggerFilterUpdate]);

    // Combined loading state for UI feedback
    const showLoadingOverlay = isFiltering || isNavigating;

    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        {/* Results Area */}
        {/* Adding aria-live and aria-busy for better screen reader experience */}
        <div
          className="relative" // For potential overlay positioning
          aria-live="polite"
          aria-busy={showLoadingOverlay || isLoading || isFetchingNextPage} // Busy during filtering OR infinite scroll loading
        >
          {/* Optional: Loading Overlay during filtering */}
          {showLoadingOverlay && (
            <div className="absolute inset-0 bg-background/50 dark:bg-background/30 backdrop-blur-sm flex justify-center items-center z-10 rounded-lg">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary" />
            </div>
          )}
          {/* Hub Results Grid */}
          {/* HubResults should handle the grid layout and the "No results" message */}
          <HubResults
            hubs={hubs}
            // Pass state needed for displaying messages within HubResults
            isLoading={showLoadingOverlay || isLoading} // Is the whole grid loading/filtering?
            hasSearched={hasFiltered} // Was a filter/search applied?
            clearFilters={clearFilters}
            searchTerm={searchTerm} // Pass current search term for "no results for 'term'" message
          />
          {/* Infinite Scroll Trigger & Indicators */}
          <div ref={loadMoreRef} className="h-1" /> {/* Invisible trigger */}
          {isFetchingNextPage && <LoadingIndicator />}
          {!hasMore &&
            hubs.length > 0 &&
            !isFetchingNextPage &&
            !showLoadingOverlay && <EndOfResults />}
        </div>
      </div>
    );
  }
);

HubGrid.displayName = "HubGrid";

export default HubGrid; // Export default if it's the main page component

"use client";

import { HubFilters } from "../hub-filters";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { SortOptions } from "../../constants";
import { cn } from "@/lib/utils";

export function HubSearchBar({
  className,
  placeholder,
}: {
  className?: string;
  placeholder?: string;
}) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading] = useState(false);

  const handleSearchTermUpdate = (term: string) => {
    setSearchTerm(term);
  };

  const handleSearchSubmit = (term: string) => {
    if (term.trim()) {
      router.push(`/hubs/search?search=${encodeURIComponent(term)}`);
    }
  };

  const handleSortChange = (sort: SortOptions) => {
    router.push(`/hubs/search?sort=${sort}`);
  };

  return (
    <div
      className={cn(
        `container max-w-full mx-auto bg-transparent mb-3`,
        className,
      )}
    >
      <HubFilters
        searchTerm={searchTerm}
        placeholder={placeholder}
        isLoading={isLoading}
        sortBy={SortOptions.Trending}
        hasSearched={false}
        onSearchTermUpdate={handleSearchTermUpdate}
        onSearchSubmit={handleSearchSubmit}
        handleSortChange={handleSortChange}
      />
    </div>
  );
}

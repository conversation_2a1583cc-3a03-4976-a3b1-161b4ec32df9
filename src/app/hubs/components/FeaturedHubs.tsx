"use client";

import { useState, useEffect } from "react";
import { motion } from "motion/react";
import { Star, ArrowRight, Users, MessageSquare } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";

interface FeaturedHubsProps {
  hubs?: SimplifiedHub[];
  isLoading?: boolean;
}

export function FeaturedHubs({ hubs = [], isLoading = false }: FeaturedHubsProps) {
  const [activeIndex, setActiveIndex] = useState(0);

  // Auto-rotate featured hubs every 5 seconds
  useEffect(() => {
    if (hubs.length <= 1) return;

    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % hubs.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [hubs.length]);

  if (isLoading) {
    return <FeaturedHubsSkeleton />;
  }

  if (hubs.length === 0) {
    return null;
  }

  return (
    <div className="w-full mb-12 mt-12">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-400 fill-yellow-400" />
            <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-purple-300 to-primary bg-clip-text text-transparent">
              Featured Hubs
            </h2>
          </div>
          <Link
            href="/hubs/search?sort=trending"
            className="text-primary hover:text-primary-alt transition-colors flex items-center gap-1 text-sm font-medium"
          >
            View all
            <ArrowRight className="h-4 w-4" />
          </Link>
        </div>

        <motion.div
          className="relative overflow-hidden rounded-xl border border-primary/20 bg-gradient-to-br from-gray-900/80 to-gray-950/90 backdrop-blur-sm shadow-2xl"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Subtle background pattern */}
          <div className="absolute inset-0 bg-mesh-gradient opacity-30" />

          <div className="relative z-10">
            {/* Featured hub carousel */}
            <div className="relative w-full">
              {hubs.map((hub, index) => (
                <motion.div
                  key={hub.id}
                  className={cn(
                    "w-full transition-all duration-500",
                    index === activeIndex ? "opacity-100" : "opacity-0 absolute inset-0 pointer-events-none"
                  )}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: index === activeIndex ? 1 : 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <Link
                    href={`/hubs/${hub.id}`}
                    className="block group"
                  >
                    <div className="flex flex-col md:flex-row items-center">
                      {/* Hub image with improved styling */}
                      <div className="relative w-full md:w-2/5 aspect-video md:aspect-square flex items-center justify-center p-6 overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-transparent opacity-30" />
                        <motion.div
                          className="relative h-32 w-32 md:h-40 md:w-40 rounded-full shadow-xl overflow-hidden z-10"
                          whileHover={{ scale: 1.05 }}
                          transition={{ duration: 0.3 }}
                        >
                          <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          <div className="absolute inset-0 border-4 border-primary/30 rounded-full z-20" />

                          {hub.iconUrl ? (
                            <Image
                              unoptimized
                              src={hub.iconUrl}
                              alt={`${hub.name} hub icon`}
                              fill
                              className="rounded-full object-cover"
                              priority={true}
                            />
                          ) : (
                            <div
                              className="flex h-full w-full items-center justify-center rounded-full bg-gradient-to-br from-primary/40 to-primary/10 text-4xl md:text-5xl font-bold text-white"
                              aria-label={`${hub.name} hub icon (letter ${hub.name.charAt(0).toUpperCase()})`}
                            >
                              {hub.name.charAt(0).toUpperCase()}
                            </div>
                          )}
                        </motion.div>
                      </div>

                      {/* Hub content with improved styling */}
                      <div className="w-full md:w-3/5 p-6 md:p-8">
                        <div className="flex flex-col">
                          <h3 className="text-2xl md:text-3xl font-bold text-white mb-2 group-hover:text-primary transition-colors duration-300">
                            {hub.name}
                          </h3>

                          {/* Stats row with improved styling */}
                          <div className="flex flex-wrap items-center gap-4 mb-4">
                            {/* Star Rating */}
                            {hub.reviews && hub.reviews.length > 0 && (
                              <div
                                className="flex items-center gap-1.5 text-sm bg-gray-800/50 px-3 py-1.5 rounded-full border border-gray-700/50"
                                aria-label={`Rating: ${(hub.reviews.reduce((sum, review) => sum + review.rating, 0) / hub.reviews.length).toFixed(1)} out of 5 stars`}
                              >
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" aria-hidden="true" />
                                <span className="text-gray-200 font-medium">
                                  {(hub.reviews.reduce((sum, review) => sum + review.rating, 0) / hub.reviews.length).toFixed(1)}
                                </span>
                              </div>
                            )}

                            {/* Server count */}
                            <div className="flex items-center gap-1.5 text-sm bg-gray-800/50 px-3 py-1.5 rounded-full border border-gray-700/50">
                              <Users className="h-4 w-4 text-blue-400" />
                              <span className="text-gray-200">
                                <span className="font-medium">{hub.connections.length}</span> servers
                              </span>
                            </div>

                            {/* Activity indicator */}
                            <div className="flex items-center gap-1.5 text-sm bg-gray-800/50 px-3 py-1.5 rounded-full border border-gray-700/50">
                              <MessageSquare className="h-4 w-4 text-green-400" />
                              <span className="text-gray-200 font-medium">Active</span>
                            </div>
                          </div>

                          {/* Tags with improved styling */}
                          {hub.tags.length > 0 && (
                            <div className="flex flex-wrap gap-2 mb-4">
                              {hub.tags.slice(0, 3).map(tag => (
                                <span
                                  key={tag.name}
                                  className="px-2.5 py-1 text-xs rounded-md bg-primary/10 text-primary/90 border border-primary/20 shadow-sm"
                                >
                                  {tag.name}
                                </span>
                              ))}
                            </div>
                          )}

                          <p className="text-gray-300 mb-6 line-clamp-2 md:line-clamp-3">{hub.description}</p>

                          <div className="flex justify-end mt-auto">
                            <Button
                              className="cursor-pointer bg-gradient-to-r from-primary to-primary-alt hover:opacity-90 text-white shadow-lg transition-all duration-300 group-hover:shadow-primary/20"
                            >
                              View Hub
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Improved pagination indicators */}
          {hubs.length > 1 && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-20">
              {hubs.map((hub, index) => (
                <Button
                  key={`pagination-${hub.id}`}
                  className={cn(
                    "transition-all duration-300 p-0 min-w-0 h-1.5 rounded-full shadow-md",
                    index === activeIndex
                      ? "w-8 bg-gradient-to-r from-primary to-primary-alt"
                      : "w-2 bg-gray-600/70 hover:bg-gray-500/70 hover:w-4"
                  )}
                  onClick={() => setActiveIndex(index)}
                  aria-label={`Go to featured hub ${index + 1}`}
                />
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
}

function FeaturedHubsSkeleton() {
  return (
    <div className="w-full mb-12">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-400 fill-yellow-400" />
            <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-purple-300 to-primary bg-clip-text text-transparent">
              Featured Hubs
            </h2>
          </div>
          <div className="w-20 h-5">
            <Skeleton className="h-full w-full" />
          </div>
        </div>

        <div className="relative overflow-hidden rounded-xl border border-primary/20 bg-gradient-to-br from-gray-900/80 to-gray-950/90 backdrop-blur-sm shadow-2xl">
          <div className="absolute inset-0 bg-mesh-gradient opacity-30" />

          <div className="relative z-10">
            <div className="w-full">
              <div className="flex flex-col md:flex-row items-center">
                {/* Hub image skeleton */}
                <div className="relative w-full md:w-2/5 aspect-video md:aspect-square flex items-center justify-center p-6 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-transparent opacity-30" />
                  <Skeleton className="h-32 w-32 md:h-40 md:w-40 rounded-full relative z-10" />
                </div>

                {/* Hub content skeleton */}
                <div className="w-full md:w-3/5 p-6 md:p-8">
                  <Skeleton className="h-8 w-2/3 mb-4" />

                  {/* Stats row skeleton */}
                  <div className="flex flex-wrap gap-4 mb-4">
                    <Skeleton className="h-8 w-20 rounded-full" />
                    <Skeleton className="h-8 w-28 rounded-full" />
                    <Skeleton className="h-8 w-24 rounded-full" />
                  </div>

                  {/* Tags skeleton */}
                  <div className="flex gap-2 mb-4">
                    <Skeleton className="h-6 w-16 rounded-md" />
                    <Skeleton className="h-6 w-20 rounded-md" />
                    <Skeleton className="h-6 w-14 rounded-md" />
                  </div>

                  {/* Description skeleton */}
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4 mb-6" />

                  {/* Button skeleton */}
                  <div className="flex justify-end">
                    <Skeleton className="h-10 w-36 rounded-md" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Pagination skeleton */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-20">
            <Skeleton className="w-8 h-1.5 rounded-full" />
            <Skeleton className="w-2 h-1.5 rounded-full" />
            <Skeleton className="w-2 h-1.5 rounded-full" />
            <Skeleton className="w-2 h-1.5 rounded-full" />
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { Check, PlusIcon, Terminal } from "lucide-react";
import { useState } from "react";
import { SimplifiedHub } from "@/hooks/use-infinite-hubs";

export default function JoinHubCard({ hub }: { hub: SimplifiedHub }) {
  const { name } = hub;
  const [copied, setCopied] = useState(false);

  const joinHubCommand = `/connect hub:${name}`;

  const handleCopyCommand = async () => {
    try {
      await navigator.clipboard.writeText(joinHubCommand);
      setCopied(true);
      toast({
        title: "Command copied!",
        description:
          "Paste this command in your Discord server to join the hub.",
        duration: 3000,
        className: "bg-card border-primary/20 text-primary-foreground",
      });
      setTimeout(() => setCopied(false), 2000);
    } catch {
      toast({
        title: "Failed to copy",
        description: "Please copy the command manually.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="dark:border-primary/20 border-primary/10 dark:bg-gradient-to-br dark:from-primary/15 dark:to-primary/5 bg-gradient-to-br from-primary/3 to-primary/1 shadow-md overflow-hidden">
      <div className="border-primary/10 dark:bg-primary/15 bg-primary/3 border-b dark:border-primary/20 p-4">
        <h3 className="text-lg font-medium flex items-center justify-center gap-2 dark:text-card-foreground text-card">
          <PlusIcon className="h-5 w-5 text-primary" />
          Join This Hub
        </h3>
      </div>
      <CardContent className="p-5">
        <p className="text-sm text-center mb-4 dark:text-card-foreground text-card">
          Connect your Discord server to this hub in one easy step
        </p>
        <Button
          className="w-full bg-primary hover:bg-primary/90 text-card-foreground shadow-sm cursor-pointer"
          onClick={handleCopyCommand}
        >
          {copied ? (
            <>
              <Check className="mr-2 h-4 w-4" />
              Command Copied
            </>
          ) : (
            <>
              <Terminal className="mr-2 h-4 w-4" />
              Copy Join Command
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}

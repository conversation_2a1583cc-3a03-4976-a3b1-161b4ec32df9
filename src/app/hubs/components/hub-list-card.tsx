"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ArrowUpRight, Heart, Star, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { memo, useState } from "react";
import { useHubUpvote } from "../hooks/use-hub-upvote";
import Jo<PERSON><PERSON>utt<PERSON> from "./hub-detail/JoinButton";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";

// Reuse the StarRating component from hub-card.tsx
const StarRating = memo(
  ({
    averageRating,
    totalReviews,
  }: {
    averageRating: number;
    totalReviews: number;
  }) => {
    // Generate star rating elements
    const renderStars = () => {
      const stars = [];
      const fullStars = Math.floor(averageRating);
      const hasHalfStar = averageRating - fullStars >= 0.5;

      // Add filled stars
      for (let i = 0; i < fullStars; i++) {
        stars.push(
          <Star
            key={`star-${i}`}
            className="h-4 w-4 fill-primary text-primary"
            aria-hidden="true"
          />,
        );
      }

      // Add half star if needed
      if (hasHalfStar) {
        stars.push(
          <div key="half-star" className="relative" aria-hidden="true">
            <Star className="h-4 w-4 text-primary" />
            <div className="absolute top-0 left-0 w-1/2 overflow-hidden">
              <Star className="h-4 w-4 fill-primary text-primary" />
            </div>
          </div>,
        );
      }

      // Add empty stars to reach 5
      const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
      for (let i = 0; i < emptyStars; i++) {
        stars.push(
          <Star
            key={`empty-star-${i}`}
            className="h-4 w-4 text-gray-300 dark:text-gray-600"
            aria-hidden="true"
          />,
        );
      }

      return stars;
    };

    if (totalReviews <= 0) {
      return (
        <span className="text-xs text-gray-500 dark:text-gray-400">
          No ratings yet
        </span>
      );
    }

    return (
      <div
        className="flex"
        aria-label={`Rated ${averageRating.toFixed(1)} out of 5 stars based on ${totalReviews} reviews`}
      >
        <div className="flex" aria-hidden="true">
          {renderStars()}
        </div>
        <div className="flex items-center gap-1">
          <span className="ml-1 text-xs font-medium text-gray-700 dark:text-gray-300">
            {averageRating.toFixed(1)}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            ({totalReviews})
          </span>
        </div>
      </div>
    );
  },
);

StarRating.displayName = "StarRating";

// Memoize the TagList component
const TagList = memo(({ tags }: { tags: string[] }) => {
  // Show fewer tags in list view
  const visibleTagsCount = 2;

  return (
    <div
      className="flex flex-wrap gap-1.5 min-h-[1.75rem]"
      aria-label="Hub tags"
    >
      {/* biome-ignore lint/complexity/useOptionalChain: <explanation> */}
      {tags && tags?.slice(0, visibleTagsCount).map((tag) => (
          <span
            key={tag}
            className="px-1.5 py-0.5 text-xs rounded-full bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white transition-colors duration-200 border border-gray-600 truncate max-w-[100px]"
            title={tag}
          >
            {tag}
          </span>
        ))}
      {tags?.length > visibleTagsCount && (
        <span
          className="px-1.5 py-0.5 text-xs rounded-full bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white transition-colors duration-200 border border-gray-600"
          title={`${tags.length - visibleTagsCount} more tags: ${tags.slice(visibleTagsCount).join(', ')}`}
        >
          +{tags.length - visibleTagsCount} more
        </span>
      )}
    </div>
  );
});

TagList.displayName = "TagList";

// Main component
export function HubListCard({ hub }: { hub: SimplifiedHub }) {
  const [isHovering, setIsHovering] = useState(false);
  const { id, name, iconUrl, description, tags, connections } = hub;
  const { liked, upvoteCount, handleUpvote } = useHubUpvote(id, hub.upvotes);

  return (
    <article
      className="group relative flex overflow-hidden rounded-xl border border-gray-700/50 bg-gray-800/30 backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:shadow-primary/20 hover:translate-y-[-2px] hover:border-primary/30 cursor-pointer h-full min-h-[120px] sm:min-h-[140px] touch-manipulation"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onTouchStart={() => setIsHovering(true)}
      onTouchEnd={() => setTimeout(() => setIsHovering(false), 500)}
    >
      <Link
        href={`/hubs/${id}`}
        className="flex w-full"
        aria-label={`View ${name} hub details`}
      >
        {/* Icon/Avatar section */}
        <div className="relative flex-shrink-0 w-[100px] sm:w-[120px] bg-gradient-to-b from-gray-900/70 to-gray-800/50 flex items-center justify-center p-4">
          <div className="relative h-16 w-16 sm:h-20 sm:w-20 rounded-full border-2 border-gray-700/50 shadow-xl transition-transform duration-300 group-hover:scale-105 overflow-hidden">
            {iconUrl ? (
              <Image
                unoptimized
                src={iconUrl}
                alt={`${name} hub icon`}
                fill
                className="rounded-full object-cover"
                priority={false}
                loading="lazy"
              />
            ) : (
              <div
                className="flex h-16 w-16 sm:h-20 sm:w-20 items-center justify-center rounded-full bg-gradient-to-br from-primary/30 to-primary/10 text-2xl sm:text-3xl font-bold text-primary border-2 border-gray-700/50 shadow-lg"
                aria-label={`${name} hub icon (letter ${name.charAt(0).toUpperCase()})`}
              >
                {name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
        </div>

        {/* Content section */}
        <div className="flex flex-col flex-grow p-3 sm:p-4 md:p-5 bg-gradient-to-br from-gray-800/30 to-gray-900/30">
          <div className="flex justify-between">
            <div className="flex flex-col">
              {/* Hub name */}
              <h3 className="text-base sm:text-lg md:text-xl font-bold text-white group-hover:text-primary transition-colors duration-300 line-clamp-1">
                {name}
              </h3>

              {/* Stats and tags in a row */}
              <div className="flex items-center gap-3 flex-wrap mt-1 sm:mt-2">
                {/* Star Rating */}
                {hub.reviews && hub.reviews.length > 0 && (
                  <div
                    className="flex items-center gap-1 text-xs sm:text-sm"
                    aria-label={`Rating: ${(hub.reviews.reduce((sum, review) => sum + review.rating, 0) / hub.reviews.length).toFixed(1)} out of 5 stars`}
                  >
                    <Star className="h-4 w-4 fill-purple-500 text-purple-500 drop-shadow-sm" aria-hidden="true" />
                    <span className="text-gray-300 font-medium">
                      {(hub.reviews.reduce((sum, review) => sum + review.rating, 0) / hub.reviews.length).toFixed(1)}
                    </span>
                  </div>
                )}

                {/* Server count */}
                <div
                  className="flex items-center gap-1 text-xs sm:text-sm"
                  aria-label={`${connections.length} connected servers`}
                >
                  <Users className="h-4 w-4 text-purple-500 drop-shadow-sm" aria-hidden="true" />
                  <span className="text-gray-300 font-medium">{connections.length}</span>
                </div>

                {/* Tags as badges */}
                {tags.slice(0, 2).map(tag => (
                  <span
                    key={tag.name}
                    className="px-2 py-0.5 text-xs rounded-md bg-gray-800/80 text-gray-300 border border-gray-700/50 shadow-sm"
                  >
                    {tag.name}
                  </span>
                ))}

                {/* +1 indicator if there are more tags */}
                {tags.length > 2 && (
                  <span className="px-2 py-0.5 text-xs rounded-md bg-gray-800/80 text-gray-300 border border-gray-700/50 shadow-sm">
                    +{tags.length - 2}
                  </span>
                )}
              </div>
            </div>

            {/* Action Buttons - Moved to top right */}
            <div className="flex gap-2 sm:gap-3 flex-shrink-0">
              {/* Upvote Button */}
              <Button
                variant="outline"
                size="sm"
                className={cn(
                  "flex items-center gap-1 px-2 sm:px-3 py-1 h-8 sm:h-9 rounded-md transition-all duration-200",
                  "border hover:scale-105 active:scale-95 touch-manipulation shadow-md",
                  liked
                    ? "bg-primary/10 border-primary text-primary hover:bg-primary/20 hover:shadow-primary/20"
                    : "bg-gray-800/90 border-gray-700 text-gray-300 hover:border-primary/50 hover:text-primary hover:shadow-primary/10",
                )}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleUpvote();
                }}
                aria-label={liked ? "Remove upvote" : "Upvote this hub"}
                aria-pressed={liked}
              >
                <Heart
                  className={cn(
                    "h-4 w-4 transition-all duration-200",
                    liked ? "fill-primary text-primary scale-110" : "scale-100",
                  )}
                  aria-hidden="true"
                />
                <span className="hidden sm:inline font-medium ml-1">Vote</span>
                <span className="inline sm:hidden font-medium ml-1">{upvoteCount}</span>
                <span className="hidden sm:inline">({upvoteCount})</span>
              </Button>

              {/* Join Button */}
              <div className="min-w-[70px] sm:min-w-[100px]">
                <JoinButton hubName={name} hubId={id} />
              </div>
            </div>
          </div>

          <p className="mt-2 sm:mt-3 line-clamp-2 text-xs sm:text-sm text-gray-300 min-h-[2rem] leading-relaxed">
            {description}
          </p>
        </div>

        {/* View hub indicator on hover */}
        {isHovering && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 z-10">
            <span className="flex items-center gap-1 px-2 py-1 bg-primary/90 text-white rounded-full text-xs font-medium shadow-lg">
              View <ArrowUpRight className="h-3 w-3" aria-hidden="true" />
            </span>
          </div>
        )}

        {/* Subtle hover indicator */}
        <div
          className={cn(
            "absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary/80 to-primary-alt/80 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left shadow-lg",
          )}
        />
      </Link>
    </article>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(HubListCard);

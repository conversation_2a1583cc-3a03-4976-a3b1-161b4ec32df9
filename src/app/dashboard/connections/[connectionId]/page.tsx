"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Connection, Hub, ServerData } from "@/lib/generated/prisma/client";
import { ArrowLeft, Edit, Loader2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { ConnectionNavigationTabs } from "@/components/dashboard/connections/connection-navigation-tabs";

export default function ConnectionPage() {
  const { connectionId } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [connection, setConnection] = useState<
    (Connection & { hub: Hub; server: ServerData }) | null
  >(null);
  const { toast } = useToast();
  const router = useRouter();

  // Fetch connection data
  useEffect(() => {
    const fetchConnection = async () => {
      try {
        setIsLoading(true);

        const response = await fetch(
          `/api/dashboard/connections/${connectionId}`,
        );

        if (!response.ok) {
          throw new Error("Failed to fetch connection data");
        }

        const data = await response.json();
        setConnection(data.connection);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching connection:", error);
        toast({
          title: "Error",
          description: "Failed to load connection data. Please try again.",
          variant: "destructive",
        });
        router.push("/dashboard/servers");
      }
    };

    fetchConnection();
  }, [connectionId, toast, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href="/dashboard/servers?tab=connections">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Connection Details</h1>
        </div>
        <Button
          asChild
          size="sm"
          className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
        >
          <Link href={`/dashboard/connections/${connectionId}/edit`}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Connection
          </Link>
        </Button>
      </div>
      <ConnectionNavigationTabs connectionId={connectionId as string} currentTab="overview" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm md:col-span-2">
          <CardHeader className="px-4 sm:px-6">
            <CardTitle>Connection Details</CardTitle>
            <CardDescription>
              Information about this connection
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6 px-4 sm:px-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-400">Status</h3>
                  <div className="flex items-center mt-1">
                    <div
                      className={`h-2.5 w-2.5 rounded-full mr-2 ${
                        connection?.connected
                          ? "bg-green-500"
                          : "bg-red-500"
                      }`}
                    ></div>
                    <span className="font-medium">
                      {connection?.connected ? "Connected" : "Disconnected"}
                    </span>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-400">Channel</h3>
                  <p className="mt-1 font-medium">
                    {connection?.channelId}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-400">Compact Mode</h3>
                  <p className="mt-1 font-medium">
                    {connection?.compact ? "Enabled" : "Disabled"}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-400">Last Active</h3>
                  <p className="mt-1 font-medium">
                    {connection?.lastActive
                      ? new Date(connection.lastActive).toLocaleString()
                      : "Never"}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-400">Hub</h3>
                  <div className="flex items-center gap-3 mt-1">
                    <Image
                      src={connection?.hub.iconUrl || "/pfp1.png"}
                      alt={connection?.hub.name || "Hub"}
                      width={40}
                      height={40}
                      className="rounded-full"
                    />
                    <div>
                      <p className="font-medium">{connection?.hub.name}</p>
                      <Button
                        variant="link"
                        size="sm"
                        className="h-auto p-0"
                        asChild
                      >
                        <Link href={`/dashboard/hubs/${connection?.hub.id}`}>
                          View Hub
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-400">Server</h3>
                  <div className="flex items-center gap-3 mt-1">
                    <Image
                      src={`https://api.dicebear.com/7.x/identicon/svg?seed=${connection?.server.id}`}
                      alt={connection?.server.name || "Server"}
                      width={40}
                      height={40}
                      className="rounded-full"
                    />
                    <div>
                      <p className="font-medium">{connection?.server.name}</p>
                      <Button
                        variant="link"
                        size="sm"
                        className="h-auto p-0"
                        asChild
                      >
                        <Link href={`/dashboard/servers/${connection?.server.id}`}>
                          View Server
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader className="px-4 sm:px-6">
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Manage this connection
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 px-4 sm:px-6">
            <Button
              asChild
              className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
            >
              <Link href={`/dashboard/connections/${connectionId}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Edit Connection</span>
                <span className="inline sm:hidden">Edit</span>
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

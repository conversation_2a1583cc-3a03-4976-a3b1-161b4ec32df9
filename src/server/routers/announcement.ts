/**
 * Announcement router for tRPC
 */
import { router, protectedProcedure } from '../trpc';
import prisma from '@/lib/prisma';

export const announcementRouter = router({
  // Get all announcements
  getAnnouncements: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session.user.id;

      // Get the user's last read date
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { inboxLastReadDate: true },
      });

      // Get all announcements
      const announcements = await prisma.announcement.findMany({
        orderBy: { createdAt: 'desc' },
      });

      // Mark which announcements are unread
      const lastReadDate = user?.inboxLastReadDate || new Date(0);
      
      return {
        announcements: announcements.map(announcement => ({
          ...announcement,
          isUnread: announcement.createdAt > lastReadDate,
        })),
        unreadCount: announcements.filter(a => a.createdAt > lastReadDate).length,
      };
    }),

  // Mark all announcements as read
  markAllAsRead: protectedProcedure
    .mutation(async ({ ctx }) => {
      const userId = ctx.session.user.id;

      // Update the user's last read date to now
      await prisma.user.update({
        where: { id: userId },
        data: { inboxLastReadDate: new Date() },
      });

      return { success: true };
    }),
});

import { CheckCircle2, <PERSON>, <PERSON> } from "lucide-react";
import type React from "react";

export type PricingTier = {
  id: string;
  name: string;
  description: string;
  price: string;
  period?: string;
  features: string[];
  limitations?: string[];
  cta: string;
  ctaLink: string;
  popular: boolean;
  icon: React.ReactNode;
  gradientFrom: string;
  gradientTo: string;
};

export type FeatureCategory = {
  category: string;
  features: {
    name: string;
    free: string;
    talker: string;
    broadcaster: string;
  }[];
};

// Define the pricing tiers
export const pricingTiers: PricingTier[] = [
  {
    id: "free",
    name: "Free",
    description: "Perfect for small communities",
    price: "Free",
    features: [
      "1 hub",
      "75 servers per hub",
      "Basic moderation tools",
      "Standard support",
      "7-day message retention",
      "50 blacklist entries",
    ],
    limitations: ["No appeals system", "Limited hub customization"],
    cta: "Get Started",
    ctaLink: "/invite",
    popular: false,
    icon: <CheckCircle2 className="h-5 w-5" />,
    gradientFrom: "from-gray-500/20",
    gradientTo: "to-gray-700/30",
  },
  {
    id: "talker",
    name: "Talker",
    description: "For growing communities",
    price: "$4.99",
    period: "per month",
    features: [
      "5 hubs",
      "200 servers per hub",
      "Advanced moderation tools",
      "Priority support",
      "30-day message retention",
      "500 blacklist entries",
      "Appeals system",
      "Enhanced hub customization",
      "Custom welcome messages",
    ],
    cta: "Upgrade Now",
    ctaLink: "https://ko-fi.com/dev737",
    popular: true,
    icon: <Star className="h-5 w-5" />,
    gradientFrom: "from-primary/20",
    gradientTo: "to-primary-alt/30",
  },
  {
    id: "broadcaster",
    name: "Broadcaster",
    description: "For large communities",
    price: "$9.99",
    period: "per month",
    features: [
      "Unlimited hubs",
      "Unlimited servers per hub",
      "Premium moderation suite",
      "Priority support with direct access",
      "90-day message retention",
      "Unlimited blacklist entries",
      "Advanced appeals system",
      "Full hub customization",
      "Custom welcome messages",
      "Analytics dashboard",
      "API access",
    ],
    cta: "Upgrade Now",
    ctaLink: "https://ko-fi.com/dev737",
    popular: false,
    icon: <Crown className="h-5 w-5" />,
    gradientFrom: "from-amber-500/20",
    gradientTo: "to-amber-700/30",
  },
];

// Feature comparison table data
export const featureComparisonData: FeatureCategory[] = [
  {
    category: "Hubs & Servers",
    features: [
      {
        name: "Number of Hubs",
        free: "1",
        talker: "5",
        broadcaster: "Unlimited",
      },
      {
        name: "Servers per Hub",
        free: "75",
        talker: "200",
        broadcaster: "Unlimited",
      },
    ],
  },
  {
    category: "Moderation",
    features: [
      {
        name: "Blacklist Entries",
        free: "50",
        talker: "500",
        broadcaster: "Unlimited",
      },
      {
        name: "Appeals System",
        free: "❌",
        talker: "✓",
        broadcaster: "Advanced",
      },
      {
        name: "Anti-Swear Rules",
        free: "Basic",
        talker: "Advanced",
        broadcaster: "Premium",
      },
      {
        name: "NSFW Detection",
        free: "✓",
        talker: "✓",
        broadcaster: "✓",
      },
    ],
  },
  {
    category: "Customization",
    features: [
      {
        name: "Welcome Messages",
        free: "Basic",
        talker: "Custom with Variables",
        broadcaster: "Custom with Variables",
      },
      {
        name: "Hub Customization",
        free: "Limited",
        talker: "Enhanced",
        broadcaster: "Full",
      },
    ],
  },
  {
    category: "Support & Data",
    features: [
      {
        name: "Message Retention",
        free: "7 days",
        talker: "30 days",
        broadcaster: "90 days",
      },
      {
        name: "Support",
        free: "Standard",
        talker: "Priority",
        broadcaster: "Priority with Direct Access",
      },
      {
        name: "Analytics",
        free: "❌",
        talker: "Basic",
        broadcaster: "Advanced",
      },
      {
        name: "API Access",
        free: "❌",
        talker: "❌",
        broadcaster: "✓",
      },
    ],
  },
];

import "../global.css";

export default function HubsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative min-h-screen bg-gray-950 overflow-hidden">
      {/* Main background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-950 via-gray-900 to-gray-950" />

      {/* Animated gradient orbs */}
      <div
        className="absolute top-0 left-1/4 w-[800px] h-[800px] rounded-full bg-purple-900/10 blur-3xl animate-pulse"
        style={{ animationDuration: "15s" }}
      />
      <div
        className="absolute bottom-0 right-1/4 w-[600px] h-[600px] rounded-full bg-blue-900/10 blur-3xl animate-pulse"
        style={{ animationDuration: "20s" }}
      />
      <div
        className="absolute top-1/3 right-1/3 w-[500px] h-[500px] rounded-full bg-pink-900/5 blur-3xl animate-pulse"
        style={{ animationDuration: "25s" }}
      />

      {/* Radial gradient overlay */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-purple-900/10 via-transparent to-transparent opacity-70" />

      {/* Noise texture overlay */}
      <div className="absolute inset-0 bg-[url('/noise.svg')] opacity-[0.15] mix-blend-overlay" />

      {/* Star-like dots */}
      <div className="absolute inset-0">
        {Array.from({ length: 50 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full animate-pulse"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              opacity: Math.random() * 0.5 + 0.1,
              animationDuration: `${Math.random() * 10 + 10}s`,
            }}
          />
        ))}
      </div>

      {/* Content */}
      <div className="relative mx-auto z-0">{children}</div>
    </div>
  );
}

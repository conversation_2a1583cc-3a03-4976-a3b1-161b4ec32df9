"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	<PERSON><PERSON><PERSON><PERSON>,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	Di<PERSON>Footer,
	<PERSON><PERSON>Header,
	<PERSON><PERSON>Title,
	Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import type { Connection, Hub } from "@/lib/generated/prisma/client";
import { Check, Home, Loader2, MessageSquare } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "@/hooks/use-toast";

interface Channel {
	id: string;
	name: string;
	type: number;
	parentId: string | null;
	parentName: string | null;
	position: number;
	isThread: boolean;
	isPrivateThread: boolean;
}

interface FastServerConnectProps {
	serverId: string;
	serverName: string;
	serverIcon: string | null;
	botAdded: boolean;
}

export function FastServerConnect({
	serverId,
	serverName,
	serverIcon,
	botAdded,
}: FastServerConnectProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [hubs, setHubs] = useState<Hub[]>([]);
	const [channels, setChannels] = useState<Channel[]>([]);
	const [existingConnections, setExistingConnections] = useState<
		{ hubId: string; channelId: string }[]
	>([]);
	const [selectedHub, setSelectedHub] = useState<string>("");
	const [selectedChannel, setSelectedChannel] = useState<string>("");
	const [step, setStep] = useState(1);
	const [success, setSuccess] = useState(false);
	const router = useRouter();

	// Fetch hubs when dialog opens
	useEffect(() => {
		if (isOpen && !isLoading && hubs.length === 0) {
			fetchHubs();
		}
	}, [isOpen]);

	// Fetch channels when hub is selected
	useEffect(() => {
		if (selectedHub && botAdded && step === 2) {
			fetchExistingConnections();
		}
	}, [selectedHub, step]);

	const fetchHubs = async () => {
		setIsLoading(true);
		try {
			// First fetch existing connections to filter out hubs that are already connected
			const connectionsResponse = await fetch("/api/dashboard/connections");
			let connectedHubIds: string[] = [];

			if (connectionsResponse.ok) {
				const connectionsData = await connectionsResponse.json();

				// Filter connections for this server and extract hubIds
				// The connections array contains objects with serverId as a string
				const serverConnections = connectionsData.connections.filter(
					(conn: { serverId: string }) => conn.serverId === serverId,
				) as Connection[];

				connectedHubIds = serverConnections.map((conn) => conn.hubId);
			}

			// Then fetch available hubs
			const response = await fetch("/api/hubs?moderated=true");
			if (!response.ok) {
				throw new Error("Failed to fetch hubs");
			}
			const data = await response.json();

			// Filter out hubs that are already connected to this server
			const filteredHubs = data.hubs.filter(
				(hub: { id: string; name: string }) => {
					const isConnected = connectedHubIds.includes(hub.id);
					return !isConnected;
				},
			);
			setHubs(filteredHubs);
		} catch (error) {
			console.error("Error fetching hubs:", error);
			toast({
				title: "Error",
				description: "Failed to load your hubs. Please try again.",
				variant: "destructive",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const fetchExistingConnections = async () => {
		setIsLoading(true);
		try {
			// First fetch existing connections
			const connectionsResponse = await fetch("/api/dashboard/connections");
			if (connectionsResponse.ok) {
				const connectionsData = await connectionsResponse.json();
				// Filter connections for this server
				const serverConnections = connectionsData.connections
					.filter((conn: Connection) => conn.serverId === serverId)
					.map((conn: Connection) => ({
						hubId: conn.hubId,
						channelId: conn.channelId,
					}));

				setExistingConnections(serverConnections);

				// Check if the server is already connected to the selected hub
				const isConnectedToSelectedHub = serverConnections.some(
					(conn: { hubId: string; channelId: string }) =>
						conn.hubId === selectedHub,
				);

				if (isConnectedToSelectedHub) {
					// If already connected to this hub, show a message and don't fetch channels
					setChannels([]);
					toast({
						title: "Information",
						description: "This server is already connected to the selected hub. A server can only connect to a hub once.",
					});
					setIsLoading(false);
					return;
				}
			}

			// Then fetch channels with the hubId parameter to enable server-hub validation on the backend
			const response = await fetch(
				`/api/discord/servers/${serverId}/channels?hubId=${selectedHub}`,
			);
			if (!response.ok) {
				throw new Error("Failed to fetch channels");
			}
			const data = await response.json();

			// The backend already filters out ineligible channels, but we'll add an extra layer of filtering here
			const textChannels = data.channels.filter((channel: Channel) => {
				// Check if channel is already connected to any hub
				const isAlreadyConnected = existingConnections.some(
					(conn) => conn.channelId === channel.id,
				);

				// Only include text channels and threads that are not already connected
				return (
					(channel.type === 0 ||
						channel.type === 5 ||
						channel.type === 11 ||
						channel.type === 15) &&
					!isAlreadyConnected
				);
			});

			setChannels(textChannels);
		} catch (error) {
			console.error("Error fetching channels:", error);
			toast({
				title: "Error",
				description: "Failed to load channels. Please try again.",
				variant: "destructive",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleConnect = async () => {
		if (!selectedHub || !selectedChannel) {
			toast({
				title: "Error",
				description: "Please select both a hub and a channel.",
				variant: "destructive",
			});
			return;
		}

		setIsSubmitting(true);
		try {
			const response = await fetch("/api/dashboard/connections", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					hubId: selectedHub,
					serverId: serverId,
					channelId: selectedChannel,
				}),
			});

			if (!response.ok) {
				const error = await response.json();

				// Handle specific blacklist errors
				if (
					error.error === "You are blacklisted from this hub" ||
					error.error === "This server is blacklisted from this hub"
				) {
					throw new Error(error.error);
				}

				throw new Error(error.error || "Failed to create connection");
			}

			setSuccess(true);
			toast({
				title: "Success",
				description: "Your server has been connected to the hub successfully.",
				variant: "default",
			});

			// Refresh the page after a short delay
			setTimeout(() => {
				router.refresh();
			}, 1500);
		} catch (error) {
			console.error("Error connecting server:", error);
			toast({
				title: "Error",
				description: error instanceof Error ? error.message : "Failed to connect server",
				variant: "destructive",
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	const nextStep = () => {
		if (step === 1 && selectedHub) {
			setStep(2);
		}
	};

	const prevStep = () => {
		if (step === 2) {
			setStep(1);
		}
	};

	const resetState = () => {
		setStep(1);
		setSelectedHub("");
		setSelectedChannel("");
		setSuccess(false);
	};

	const handleOpenChange = (open: boolean) => {
		setIsOpen(open);
		if (!open) {
			resetState();
		}
	};

	const getSelectedHubName = () => {
		const hub = hubs.find((h) => h.id === selectedHub);
		return hub ? hub.name : "";
	};

	// If bot is not added, show a different UI
	if (!botAdded) {
		return (
			<Card className="border border-yellow-500/20 bg-yellow-950/10">
				<CardHeader>
					<CardTitle className="text-lg flex items-center gap-2">
						<Home className="h-5 w-5 text-yellow-400" />
						Quick Connect
					</CardTitle>
					<CardDescription>Add the bot to this server first</CardDescription>
				</CardHeader>
				<CardContent>
					<p className="text-sm text-gray-400 mb-4">
						You need to add the InterChat bot to this server before you can
						connect it to a hub.
					</p>
				</CardContent>
				<CardFooter>
					<Button
						className="cursor-pointer w-full bg-gradient-to-r from-yellow-600 to-amber-600 hover:from-yellow-600/80 hover:to-amber-600/80 border-none"
						asChild
					>
						<a
							href={`https://discord.com/oauth2/authorize?client_id=769921109209907241&guild_id=${serverId}`}
							target="_blank"
							rel="noopener noreferrer"
						>
							Add Bot to Server
						</a>
					</Button>
				</CardFooter>
			</Card>
		);
	}

	return (
		<Card className="border border-indigo-500/20 bg-indigo-950/10">
			<CardHeader>
				<CardTitle className="text-lg flex items-center gap-2">
					<MessageSquare className="h-5 w-5 text-indigo-400" />
					Quick Connect
				</CardTitle>
				<CardDescription>
					Connect this server to a hub in seconds
				</CardDescription>
			</CardHeader>
			<CardContent>
				<p className="text-sm text-gray-400 mb-4">
					Quickly connect this server to one of your hubs with just a few
					clicks.
				</p>
			</CardContent>
			<CardFooter>
				<Dialog open={isOpen} onOpenChange={handleOpenChange}>
					<DialogTrigger asChild>
						<Button className="cursor-pointer w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none">
							Connect to Hub
						</Button>
					</DialogTrigger>
					<DialogContent className="sm:max-w-[425px] bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50">
						{success ? (
							<div className="py-10 flex flex-col items-center justify-center">
								<div className="h-16 w-16 rounded-full bg-green-500/20 flex items-center justify-center mb-4">
									<Check className="h-8 w-8 text-green-500" />
								</div>
								<h2 className="text-xl font-semibold text-center mb-2">
									Connection Successful!
								</h2>
								<p className="text-gray-400 text-center mb-6">
									Your server has been connected to the hub.
								</p>
								<Button
									onClick={() => handleOpenChange(false)}
									className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
								>
									Done
								</Button>
							</div>
						) : (
							<>
								<DialogHeader>
									<DialogTitle>Connect Server to Hub</DialogTitle>
									<DialogDescription>
										{step === 1
											? "Select a hub to connect this server to"
											: "Choose a channel for the connection"}
									</DialogDescription>
								</DialogHeader>

								{/* Step indicator */}
								<div className="w-full bg-gray-800/50 h-1 rounded-full overflow-hidden mt-2">
									<div
										className="bg-gradient-to-r from-blue-600 to-indigo-600 h-full transition-all duration-300 ease-in-out"
										style={{ width: step === 1 ? "50%" : "100%" }}
									/>
								</div>

								<div className="py-4">
									{/* Server info */}
									<div className="flex items-center gap-3 p-3 bg-gray-800/30 rounded-lg border border-gray-800/50 mb-4">
										<div className="h-10 w-10 rounded-full overflow-hidden bg-gray-800 flex-shrink-0">
											{serverIcon ? (
												<Image
													src={`https://cdn.discordapp.com/icons/${serverId}/${serverIcon}.png?size=128`}
													alt={serverName}
													width={40}
													height={40}
													className="object-cover"
												/>
											) : (
												<div className="h-full w-full flex items-center justify-center text-gray-400">
													<Home className="h-5 w-5" />
												</div>
											)}
										</div>
										<div className="flex-1 min-w-0">
											<h3 className="font-medium truncate">{serverName}</h3>
											<p className="text-xs text-gray-400 truncate">
												{serverId}
											</p>
										</div>
									</div>

									{step === 1 ? (
										<div className="space-y-4">
											<div className="space-y-2">
												<Label className="text-sm font-medium">
													Select Hub
												</Label>
												<Select
													value={selectedHub}
													onValueChange={setSelectedHub}
													disabled={isLoading}
												>
													<SelectTrigger className="w-full bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50">
														<SelectValue placeholder="Select a hub" />
													</SelectTrigger>
													<SelectContent className="bg-gray-900 border border-gray-800">
														{isLoading ? (
															<div className="flex items-center justify-center py-2">
																<Loader2 className="h-4 w-4 animate-spin text-gray-400 mr-2" />
																<span className="text-gray-400">
																	Loading hubs...
																</span>
															</div>
														) : hubs.length === 0 ? (
															<div className="p-2 text-center text-gray-400">
																No hubs found
															</div>
														) : (
															hubs.map((hub) => (
																<SelectItem
																	key={hub.id}
																	value={hub.id}
																	className="flex items-center gap-2"
																>
																	<div className="flex items-center gap-2">
																		<div className="h-4 w-4 rounded-full overflow-hidden">
																			<Image
																				src={hub.iconUrl}
																				alt={hub.name}
																				width={16}
																				height={16}
																				className="object-cover"
																			/>
																		</div>
																		<span>{hub.name}</span>
																	</div>
																</SelectItem>
															))
														)}
													</SelectContent>
												</Select>
											</div>
										</div>
									) : (
										<div className="space-y-4">
											<div className="p-3 bg-indigo-500/10 border border-indigo-500/20 rounded-lg">
												<div className="flex items-center gap-2 mb-1">
													<div className="h-4 w-4 rounded-full overflow-hidden">
														<Image
															src={
																hubs.find((h) => h.id === selectedHub)
																	?.iconUrl || ""
															}
															alt={getSelectedHubName()}
															width={16}
															height={16}
															className="object-cover"
														/>
													</div>
													<h3 className="font-medium text-sm">
														{getSelectedHubName()}
													</h3>
												</div>
											</div>

											<div className="space-y-2">
												<label className="text-sm font-medium">
													Select Channel
												</label>
												<Select
													value={selectedChannel}
													onValueChange={setSelectedChannel}
													disabled={isLoading}
												>
													<SelectTrigger className="w-full bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50">
														<SelectValue placeholder="Select a channel" />
													</SelectTrigger>
													<SelectContent className="bg-gray-900 border border-gray-800 max-h-[200px]">
														{isLoading ? (
															<div className="flex items-center justify-center py-2">
																<Loader2 className="h-4 w-4 animate-spin text-gray-400 mr-2" />
																<span className="text-gray-400">
																	Loading channels...
																</span>
															</div>
														) : channels.length === 0 ? (
															<div className="p-2 text-center text-gray-400">
																{existingConnections.length > 0
																	? "All channels in this server are already connected to hubs"
																	: "No channels found"}
																{existingConnections.length > 0 && (
																	<div className="text-xs text-gray-500 mt-1">
																		A channel can only be connected to one hub
																		at a time
																	</div>
																)}
															</div>
														) : (
															channels.map((channel) => (
																<SelectItem key={channel.id} value={channel.id}>
																	<div className="flex items-center gap-1">
																		<span className="text-gray-300">
																			#{channel.name}
																		</span>
																		{channel.parentName && (
																			<span className="text-xs text-gray-400">
																				(in {channel.parentName})
																			</span>
																		)}
																	</div>
																</SelectItem>
															))
														)}
													</SelectContent>
												</Select>
											</div>
										</div>
									)}
								</div>

								<DialogFooter className="flex gap-2">
									{step === 2 && (
										<Button
											variant="outline"
											onClick={prevStep}
											className="flex-1 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
										>
											Back
										</Button>
									)}
									{step === 1 ? (
										<Button
											onClick={nextStep}
											disabled={!selectedHub || isLoading}
											className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
										>
											Continue
										</Button>
									) : (
										<Button
											onClick={handleConnect}
											disabled={!selectedChannel || isSubmitting}
											className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
										>
											{isSubmitting ? (
												<>
													<Loader2 className="h-4 w-4 mr-2 animate-spin" />
													Connecting...
												</>
											) : (
												"Connect"
											)}
										</Button>
									)}
								</DialogFooter>
							</>
						)}
					</DialogContent>
				</Dialog>
			</CardFooter>
		</Card>
	);
}

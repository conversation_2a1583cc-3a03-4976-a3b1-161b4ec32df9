import { Badge } from "@/components/ui/badge";
import { Card } from "fumadocs-ui/components/card";
import { Tag } from "lucide-react";

interface HubTagsCardProps {
  tags: string[];
  handleTagClick: (tag: string) => void;
}

export default function HubTagsCard({
  tags,
  handleTagClick,
}: HubTagsCardProps) {
  return (
    <>
      {/* Tags Section */}
      {tags.length > 0 && (
        <Card
          className="border-none dark:bg-gray-900 bg-gray-300 rounded-xl shadow-lg overflow-hidden dark:text-card-foreground text-card"
          title={undefined}
        >
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-3 text-foreground border-b border-fd-border pb-2 flex items-center gap-2">
              <Tag className="h-5 w-5 text-primary" />
              Tags
            </h2>
            <div className="flex flex-wrap gap-2 mt-4">
              {tags.map((tag) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="dark:bg-primary/10 bg-primary/20 text-primary border-primary/20 hover:bg-primary/20 transition-colors cursor-pointer"
                  onClick={() => handleTagClick(tag)}
                >
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </Card>
      )}
    </>
  );
}

---
title: Message Reactions
description: Configure and use emoji reactions in your hub
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'

# Message Reactions

InterChat supports emoji reactions across servers, allowing users to react to messages with emojis that are visible to everyone in the hub.

## Understanding Reactions

Message reactions in InterChat:

- Work across different servers in a hub
- Allow for non-verbal communication and feedback
- Are synchronized in real-time
- Can be enabled or disabled by hub settings

<Callout type="info">
  When someone adds a reaction to a message, it appears for all users in all connected channels, regardless of which server they're in.
</Callout>

## Using Reactions

<Tabs items={['Adding Reactions', 'Removing Reactions']}>
  <Tab>
    To add a reaction to a message:
    
    1. Hover over the message (desktop) or tap and hold (mobile)
    2. Click/tap the reaction emoji button (😀)
    3. Select an emoji from the picker
    
    The reaction will be added to the message and visible to all users & servers in the hub.
  </Tab>
  <Tab>
    To remove your reaction:
    
    1. Click/tap on the reaction you previously added
    
    Your reaction will be removed from the count.
    
    Note: You can only remove your own reactions, not reactions added by others (unless you're a moderator).
  </Tab>
</Tabs>

## Reaction Settings

Hub owners and managers can control reaction behavior through hub settings.

### Enabling/Disabling Reactions

To toggle reactions for your hub:

1. Use the command `/hub config settings hub:YourHubName`
2. Find the "Reactions" toggle in the settings menu
3. Enable or disable as desired

<Callout type="warning">
  When reactions are disabled, existing reactions will still be visible, but users won't be able to add new ones.
</Callout>

## Moderation Controls

Hub moderators have additional controls for managing reactions:

### Removing All Reactions

If inappropriate reactions are added to a message, moderators can remove all reactions at once:

1. Right-click on the message (or tap and hold on mobile)
2. Select "Apps" > "InterChat" > "Mod Panel"
3. Click the "Remove All Reactions" button

This removes all reactions from the message across all servers in the hub.

### Reaction Restrictions

Some restrictions apply to reactions:

- Custom emojis from servers not in the hub cannot be used
- There is a rate limit to prevent spam
- Inappropriate emoji usage can result in moderation action

## Best Practices

For effective use of reactions in your hub:

1. **Establish norms**: Consider setting guidelines for appropriate reaction usage
2. **Use reactions for polls**: Simple polls can be conducted using reactions
3. **Encourage positive interactions**: Reactions can foster a sense of community
4. **Monitor for misuse**: Watch for patterns of inappropriate reaction usage

<Callout type="tip">
  Reactions can significantly enhance communication in your hub without adding message clutter. They're especially useful for showing agreement, appreciation, or simple responses.
</Callout>

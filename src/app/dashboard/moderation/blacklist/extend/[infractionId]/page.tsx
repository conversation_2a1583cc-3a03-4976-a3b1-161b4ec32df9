"use client";

import {
  DurationSelector,
  type DurationType,
} from "@/components/duration-selector";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { addDays, formatDistanceToNow } from "date-fns";
import { ArrowLeft, Clock, Loader2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { FullInfraction } from "../../page";

export default function ExtendBlacklistPage() {
  const { infractionId } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [infraction, setInfraction] = useState<FullInfraction | null>(null);
  const [duration, setDuration] = useState<DurationType>("30d");

  const { toast } = useToast();
  const router = useRouter();

  // Fetch infraction data
  useEffect(() => {
    const fetchInfraction = async () => {
      try {
        setIsLoading(true);

        const response = await fetch(`/api/infractions/${infractionId}`);

        if (!response.ok) {
          throw new Error("Failed to fetch infraction data");
        }

        const data = await response.json();
        setInfraction(data.infraction);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching infraction:", error);
        toast({
          title: "Error",
          description: "Failed to load infraction data. Please try again.",
          variant: "destructive",
        });
        router.push("/dashboard/moderation/blacklist");
      }
    };

    fetchInfraction();
  }, [infractionId, toast, router]);

  const getExpirationDate = (): string | null => {
    if (duration === "permanent") return null;

    const now = new Date();
    let expirationDate: Date;

    switch (duration) {
      case "7d":
        expirationDate = addDays(now, 7);
        break;
      case "30d":
        expirationDate = addDays(now, 30);
        break;
      case "90d":
        expirationDate = addDays(now, 90);
        break;
      case "365d":
        expirationDate = addDays(now, 365);
        break;
      default:
        expirationDate = addDays(now, 30);
    }

    return expirationDate.toISOString();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);

      const expiresAt = getExpirationDate();

      const response = await fetch(`/api/infractions/${infractionId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          expiresAt,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to extend blacklist");
      }

      toast({
        title: "Success",
        description: "Blacklist duration has been extended.",
      });

      router.push("/dashboard/moderation/blacklist");
    } catch (error) {
      console.error("Error extending blacklist:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to extend blacklist",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!infraction) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <p className="text-lg text-gray-400">Infraction not found</p>
      </div>
    );
  }

  const isUserBlacklist = !!infraction.userId;
  const currentExpiration = infraction.expiresAt
    ? formatDistanceToNow(new Date(infraction.expiresAt), { addSuffix: true })
    : "Never (Permanent)";

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href="/dashboard/moderation/blacklist">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">
            Extend Blacklist
          </h1>
        </div>
        <p className="text-gray-400 text-sm max-w-md">
          Extend the duration of an existing blacklist entry.
        </p>
      </div>
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Image
                src={infraction.hub.iconUrl}
                alt={infraction.hub.name}
                width={32}
                height={32}
                className="rounded-full"
              />
              <CardTitle className="text-lg">{infraction.hub.name}</CardTitle>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-3">
            {isUserBlacklist ? (
              <>
                <Image
                  src={
                    infraction.user?.image ||
                    "https://api.dicebear.com/7.x/shapes/svg?seed=user"
                  }
                  alt={infraction.user?.name || "Unknown User"}
                  width={40}
                  height={40}
                  className="rounded-full"
                />
                <div>
                  <div className="font-medium cursor-pointer">
                    <Link
                      href={`https://discordlookup.com/user/${infraction.userId}`}
                      target="_blank"
                    >
                      {infraction.user?.name || "Unknown User"}
                    </Link>
                  </div>
                  <div className="text-xs text-gray-400">
                    User ID: {infraction.userId}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="h-10 w-10 rounded-full bg-gray-800 flex items-center justify-center">
                  <Clock className="h-5 w-5" />
                </div>
                <div>
                  <div className="font-medium">{infraction.serverName}</div>
                  <div className="text-xs text-gray-400">
                    Server ID: {infraction.serverId}
                  </div>
                </div>
              </>
            )}
          </div>

          <div className="p-3 rounded-md bg-gray-900/50 border border-gray-800/50">
            <div className="text-sm mb-2 text-gray-400 flex items-center gap-1">
              <Clock className="h-3.5 w-3.5 text-amber-400" />
              Current Expiration:
            </div>
            <div className="text-sm font-medium">{currentExpiration}</div>
          </div>
        </CardContent>

        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6 pt-0">
            <DurationSelector
              value={duration}
              onChange={setDuration}
              description="Select a new duration for the blacklist"
            />
          </CardContent>
          <CardFooter>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 border-none"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Extending Blacklist...
                </>
              ) : (
                <>
                  <Clock className="h-4 w-4 mr-2" />
                  Extend Blacklist
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}

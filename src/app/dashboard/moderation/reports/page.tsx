import { auth } from "@/auth";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { getUserHubs } from "@/lib/permissions";
import { PermissionLevel } from "@/lib/constants";
import { <PERSON><PERSON>ef<PERSON>, AlertTriangle } from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";
import { redirect } from "next/navigation";
import { Suspense } from "react";
import { ReportsContent } from "./reports-content";

export const metadata: Metadata = {
  title: "Reports | InterChat Dashboard",
  description: "Manage reports for your InterChat hubs",
};

export default async function ReportsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard/moderation/reports");
  }

  const userHubs = await getUserHubs(session.user.id);

  // Filter hubs where user has at least moderator permissions
  const moderatedHubs = userHubs.filter(
    (hub) => hub.permissionLevel >= PermissionLevel.MODERATOR,
  );

  if (moderatedHubs.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="sm" className="mr-2" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">Reports</h1>
        </div>
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-400" />
              No Moderation Access
            </CardTitle>
            <CardDescription>
              You don&apos;t have moderation permissions for any hubs.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-400 mb-4">
              You need to be a hub owner, manager, or moderator to access the
              reports dashboard.
            </p>
            <Button asChild className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-600/80 hover:to-purple-600/80 border-none">
              <Link href="/dashboard/hubs">View My Hubs</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Suspense fallback={<ReportsLoadingSkeleton />}>
        <ReportsContent moderatedHubs={moderatedHubs} />
      </Suspense>
    </div>
  );
}

function ReportsLoadingSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="h-8 w-32 bg-gray-800 rounded animate-pulse" />
        <div className="h-10 w-48 bg-gray-800 rounded animate-pulse" />
      </div>

      <div className="space-y-4">
        <div className="h-10 w-full bg-gray-800 rounded animate-pulse" />
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 bg-gray-700 rounded-full animate-pulse" />
                  <div className="h-5 w-24 bg-gray-700 rounded animate-pulse" />
                </div>
                <div className="h-5 w-16 bg-gray-700 rounded animate-pulse" />
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 bg-gray-700 rounded-full animate-pulse" />
                  <div className="space-y-2">
                    <div className="h-4 w-20 bg-gray-700 rounded animate-pulse" />
                    <div className="h-3 w-16 bg-gray-700 rounded animate-pulse" />
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="space-y-2">
                    <div className="h-4 w-20 bg-gray-700 rounded animate-pulse" />
                    <div className="h-3 w-16 bg-gray-700 rounded animate-pulse" />
                  </div>
                  <div className="h-10 w-10 bg-gray-700 rounded-full animate-pulse" />
                </div>
              </div>
              <div className="space-y-2">
                <div className="h-16 w-full bg-gray-700 rounded animate-pulse" />
                <div className="h-12 w-full bg-gray-700 rounded animate-pulse" />
              </div>
              <div className="flex gap-2 justify-end">
                <div className="h-9 w-20 bg-gray-700 rounded animate-pulse" />
                <div className="h-9 w-24 bg-gray-700 rounded animate-pulse" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

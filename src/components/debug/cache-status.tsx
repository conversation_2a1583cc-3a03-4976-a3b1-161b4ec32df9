"use client";

import { useQueryClient } from "@/lib/tanstack-query";
import { useState, useEffect } from "react";

export function CacheDebugger() {
  const queryClient = useQueryClient();
  const [cacheEntries, setCacheEntries] = useState<
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    any[]
  >([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Update cache entries every second
    const interval = setInterval(() => {
      if (isVisible) {
        const cache = queryClient.getQueryCache().getAll();
        setCacheEntries(cache);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [queryClient, isVisible]);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-2 rounded-md text-sm z-50"
      >
        Show Cache Debug
      </button>
    );
  }

  return (
    <div className="fixed bottom-0 right-0 w-full md:w-1/2 lg:w-1/3 h-1/2 bg-gray-900/95 border border-gray-700 rounded-t-lg overflow-auto z-50 text-xs">
      <div className="sticky top-0 bg-gray-800 p-2 flex justify-between items-center border-b border-gray-700">
        <h3 className="font-bold">
          Tanstack Query Cache ({cacheEntries.length} entries)
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="bg-gray-700 hover:bg-gray-600 text-white px-2 py-1 rounded"
        >
          Close
        </button>
      </div>
      <div className="p-2 space-y-2">
        {cacheEntries.map((entry) => {
          const queryKey = JSON.stringify(entry.queryKey);
          const dataUpdatedAt = new Date(
            entry.state.dataUpdatedAt
          ).toLocaleTimeString();
          // In Tanstack Query v5, we need to access these properties differently
          const isStale = entry.state.isStale;
          const isFetching = entry.state.isFetching;

          return (
            <div
              key={queryKey}
              className={`p-2 rounded ${
                isStale ? "bg-yellow-900/30" : "bg-green-900/30"
              } ${
                isFetching ? "border-blue-500 border" : "border-gray-700 border"
              }`}
            >
              <div className="font-mono">
                <div className="font-bold">Query Key: {queryKey}</div>
                <div>Updated: {dataUpdatedAt}</div>
                <div>Status: {entry.state.status}</div>
                <div>Fetch Count: {entry.state.fetchCount || 0}</div>
                <div>Error Count: {entry.state.errorCount || 0}</div>
                <div>
                  Data Size: ~{JSON.stringify(entry.state.data).length} bytes
                </div>
                <div className="flex gap-2">
                  <span
                    className={isStale ? "text-yellow-400" : "text-green-400"}
                  >
                    {isStale ? "Stale" : "Fresh"}
                  </span>
                  {isFetching && (
                    <span className="text-blue-400">Fetching...</span>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

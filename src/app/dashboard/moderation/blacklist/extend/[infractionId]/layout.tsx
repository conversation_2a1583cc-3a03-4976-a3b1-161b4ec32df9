import { auth } from "@/auth";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { notFound, redirect } from "next/navigation";

interface ExtendBlacklistLayoutProps {
  children: React.ReactNode;
  params: Promise<{ infractionId: string }>;
}

export default async function ExtendBlacklistLayout({
  children,
  params,
}: ExtendBlacklistLayoutProps) {
  const infractionId = (await params)?.infractionId;
  const session = await auth();

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/moderation/blacklist/extend/${infractionId}`);
  }

  // Get the infraction to check the hubId
  const infraction = await prisma.infraction.findUnique({
    where: { id: infractionId },
    select: { hubId: true },
    
  });

  if (!infraction) {
    notFound();
  }

  // Check if the user has permission to modify this infraction
  const permissionLevel = await getUserHubPermission(session.user.id, infraction.hubId);

  // User must have at least moderator permissions to extend blacklist
  if (permissionLevel < PermissionLevel.MODERATOR) {
    notFound();
  }

  return <>{children}</>;
}

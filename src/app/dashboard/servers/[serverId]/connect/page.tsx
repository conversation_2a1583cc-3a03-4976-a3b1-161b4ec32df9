// Optimized to prevent redundant API calls
"use client";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import { ChannelIcon } from "@/components/discord/channel-icon";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import {
  ArrowLeft,
  Check,
  Hash,
  Home,
  Loader2,
  MessageSquare,
  Search,
  X,
} from "lucide-react";
import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";

interface ServerData {
  id: string;
  name: string;
  icon: string | null;
}

interface HubData {
  id: string;
  name: string;
  iconUrl: string;
  description: string;
}

interface ChannelData {
  id: string;
  name: string;
  type: number;
  parentId: string | null;
  parentName: string | null;
  position: number;
  isThread: boolean;
  isPrivateThread: boolean;
}

export default function ServerConnectPage() {
  // Initialize with loading false to prevent unnecessary loading state
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValidatingInvite, setIsValidatingInvite] = useState(false);
  const [inviteCode, setInviteCode] = useState("");
  const [isHubPreselected, setIsHubPreselected] = useState(false);
  const [preselectedHub, setPreselectedHub] = useState<HubData | null>(null);
  const searchParams = useSearchParams();
  const { serverId } = useParams();

  const [server, setServer] = useState<ServerData | null>(null);
  const [hubs, setHubs] = useState<HubData[]>([]);
  const [selectedHub, setSelectedHub] = useState<string>("");
  const [selectedChannel, setSelectedChannel] = useState<string>("");
  const [channels, setChannels] = useState<ChannelData[]>([]);
  const [channelSearchQuery, setChannelSearchQuery] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const { toast } = useToast();
  const router = useRouter();
  const { data: session } = useSession();

  // Use refs to track data loading state to prevent duplicate fetches
  // This is crucial for preventing infinite loading when switching tabs
  const dataFetchedRef = useRef({
    server: false,
    hubs: false,
    channels: false,
  });

  // Check for hubId in query params (for redirects from public hubs)
  useEffect(() => {
    const hubIdParam = searchParams.get("hubId");

    if (hubIdParam) {
      // Fetch hub details and preselect it in
      const fetchHubDetails = async () => {
        try {
          const response = await fetch(`/api/hubs/${hubIdParam}`);

          if (!response.ok) {
            throw new Error("Failed to fetch hub details");
          }

          const data = await response.json();
          setSelectedHub(data.hub.id);
          setPreselectedHub(data.hub);
          setIsHubPreselected(true);
        } catch (error) {
          console.error("Error fetching hub details:", error);
          toast({
            title: "Error",
            description: "Failed to load hub details. Please try again.",
            variant: "destructive",
          });
        }
      };

      fetchHubDetails();
    }
  }, [searchParams, toast]);

  // Fetch data when component mounts
  useEffect(() => {
    // Only proceed if we have a session
    if (!session) return;

    // Skip if we've already fetched all the data
    if (
      dataFetchedRef.current.server &&
      dataFetchedRef.current.hubs &&
      dataFetchedRef.current.channels
    ) {
      return;
    }

    let isMounted = true;

    const fetchAllData = async () => {
      // Only set loading state if we're actually going to fetch something
      if (
        !dataFetchedRef.current.server ||
        !dataFetchedRef.current.hubs ||
        !dataFetchedRef.current.channels
      ) {
        setIsLoading(true);
      }

      try {
        // Fetch server details if not already fetched
        if (!dataFetchedRef.current.server) {
          const serverResponse = await fetch(`/api/servers/${serverId}`);

          if (!serverResponse.ok) {
            throw new Error("Failed to fetch server data");
          }

          const serverData = await serverResponse.json();
          if (isMounted) {
            setServer(serverData.server);
            dataFetchedRef.current.server = true;
          }
        }

        // Fetch hubs the user has access to if not already fetched
        if (!dataFetchedRef.current.hubs) {
          const hubsResponse = await fetch("/api/hubs?moderated=true");

          if (!hubsResponse.ok) {
            throw new Error("Failed to fetch hubs data");
          }

          const hubsData = await hubsResponse.json();
          if (isMounted) {
            setHubs(hubsData.hubs);

            if (hubsData.hubs.length > 0 && !selectedHub && !isHubPreselected) {
              setSelectedHub(hubsData.hubs[0].id);
            }

            dataFetchedRef.current.hubs = true;
          }
        }

        // Fetch Discord channels if not already fetched
        if (!dataFetchedRef.current.channels) {
          // If we have a preselected hub, include the hubId in the request
          // to enable server-hub validation on the backend
          const hubIdParam = searchParams.get("hubId");
          const channelsUrl = hubIdParam
            ? `/api/discord/servers/${serverId}/channels?hubId=${hubIdParam}`
            : `/api/discord/servers/${serverId}/channels`;

          const channelsResponse = await fetch(channelsUrl);

          if (!channelsResponse.ok) {
            throw new Error("Failed to fetch Discord channels");
          }

          const channelsData = await channelsResponse.json();
          if (isMounted) {
            setChannels(channelsData.channels);
            dataFetchedRef.current.channels = true;

            // If no channels are returned and we have a hubId, it might be because
            // the server is already connected to this hub
            if (channelsData.channels.length === 0 && hubIdParam) {
              toast({
                title: "Already Connected",
                description:
                  "This server is already connected to the selected hub. A server can only connect to a hub once.",
                variant: "destructive",
              });
              router.push(`/dashboard/servers/${serverId}`);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        if (isMounted) {
          toast({
            title: "Error",
            description: "Failed to load server data. Please try again.",
            variant: "destructive",
          });
          router.push(`/dashboard/servers/${serverId}`);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchAllData();

    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session, serverId, toast, router]); // Intentionally omitting selectedHub and isHubPreselected to prevent re-fetching

  // Function to validate invite code
  const validateInviteCode = async () => {
    if (!inviteCode.trim()) return;

    try {
      setIsValidatingInvite(true);
      const response = await fetch(`/api/hubs/invite/${inviteCode}`);

      if (!response.ok) {
        const error = await response.json();

        // Handle specific blacklist errors
        if (error.error === "You are blacklisted from this hub") {
          throw new Error(error.error);
        }

        throw new Error("Invalid invite code");
      }

      const data = await response.json();
      setSelectedHub(data.hub.id);
      setPreselectedHub(data.hub);
      setIsHubPreselected(true);

      toast({
        title: "Success",
        description: `Joined hub: ${data.hub.name}`,
      });
    } catch (error) {
      console.error("Error validating invite code:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error &&
          error.message === "You are blacklisted from this hub"
            ? "You are blacklisted from this hub"
            : "Invalid or expired invite code",
        variant: "destructive",
      });
    } finally {
      setIsValidatingInvite(false);
      setInviteCode("");
    }
  };

  // Filter hubs based on search query
  const filteredHubs = hubs.filter((hub) =>
    hub.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Filter channels based on search query
  const filteredChannels = channels.filter(
    (channel) =>
      channel.name.toLowerCase().includes(channelSearchQuery.toLowerCase()) ||
      (channel.parentName &&
        channel.parentName
          .toLowerCase()
          .includes(channelSearchQuery.toLowerCase()))
  );

  const handleConnect = async () => {
    if (!selectedHub) {
      toast({
        title: "Error",
        description: "Please select a hub to connect to.",
        variant: "destructive",
      });
      return;
    }

    // If we have a preselected hub, use its ID to ensure we're connecting to the right hub
    const hubIdToUse =
      isHubPreselected && preselectedHub ? preselectedHub.id : selectedHub;

    if (!selectedChannel) {
      toast({
        title: "Error",
        description: "Please select a channel for the connection.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const response = await fetch("/api/dashboard/connections", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          hubId: hubIdToUse,
          serverId: serverId,
          channelId: selectedChannel,
        }),
      });

      if (!response.ok) {
        const error = await response.json();

        // Handle specific blacklist errors
        if (
          error.error === "You are blacklisted from this hub" ||
          error.error === "This server is blacklisted from this hub"
        ) {
          throw new Error(error.error);
        }

        throw new Error(error.error || "Failed to create connection");
      }

      toast({
        title: "Connection Created",
        description: `The server has been connected to ${
          preselectedHub?.name || "the hub"
        } successfully.`,
      });

      router.push(`/dashboard/servers/${serverId}`);
    } catch (error) {
      console.error("Error creating connection:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to create connection",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button
          variant="ghost"
          size="sm"
          className="mr-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
          asChild
        >
          <Link href={`/dashboard/servers/${serverId}`}>
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Link>
        </Button>
        <h1 className="text-2xl font-bold tracking-tight">Connect to Hub</h1>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm md:col-span-2">
          <CardHeader>
            <CardTitle>Select Hub</CardTitle>
            <CardDescription>
              Choose a hub to connect your server to
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {isHubPreselected ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Selected Hub</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setIsHubPreselected(false);
                      setPreselectedHub(null);
                    }}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Change Hub
                  </Button>
                </div>

                <Card className="border-primary/20 bg-primary/5">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-4">
                      <Image
                        src={preselectedHub?.iconUrl || ""}
                        alt={preselectedHub?.name || "Hub"}
                        width={64}
                        height={64}
                        className="rounded-full"
                      />
                      <div>
                        <h4 className="text-xl font-bold">
                          {preselectedHub?.name}
                        </h4>
                        <p className="text-sm text-gray-400">
                          {preselectedHub?.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <div className="flex items-center gap-2 text-sm text-green-500">
                  <Check className="h-4 w-4" />
                  <span>
                    Hub selected. Now choose a channel below to complete the
                    connection.
                  </span>
                </div>
              </div>
            ) : (
              <>
                <div className="space-y-4">
                  <div>
                    <Label
                      htmlFor="invite-code"
                      className="text-sm font-medium mb-1.5 block"
                    >
                      Have an invite code?
                    </Label>
                    <div className="relative">
                      <Input
                        id="invite-code"
                        type="text"
                        placeholder="Enter invite code here"
                        value={inviteCode}
                        onChange={(e) => setInviteCode(e.target.value)}
                        className="bg-[#0a0a0c] border-gray-800 pr-24"
                      />
                      <Button
                        size="sm"
                        className="absolute right-1 top-1"
                        onClick={validateInviteCode}
                        disabled={isValidatingInvite || !inviteCode.trim()}
                      >
                        {isValidatingInvite ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <Check className="h-4 w-4 mr-2" />
                        )}
                        Join
                      </Button>
                    </div>
                    <p className="text-xs text-gray-400 mt-1">
                      Use an invite code to join a private hub
                    </p>
                  </div>

                  <Separator className="my-4" />

                  <div>
                    <Label className="text-sm font-medium mb-1.5 block">
                      Or select from your hubs
                    </Label>
                    <div className="relative">
                      <Input
                        type="text"
                        placeholder="Search hubs..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="bg-[#0a0a0c] border-gray-800 pl-10"
                      />
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                        <Search className="h-4 w-4 text-gray-400" />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4 max-h-[300px] overflow-y-auto pr-2">
                    {filteredHubs.length === 0 ? (
                      <div className="text-center py-6">
                        <MessageSquare className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                        <p className="text-gray-400">
                          No hubs found matching your search.
                        </p>
                      </div>
                    ) : (
                      filteredHubs.map((hub) => (
                        <div
                          key={hub.id}
                          className={`p-4 rounded-md cursor-pointer transition-colors ${
                            selectedHub === hub.id
                              ? "bg-primary/20 border border-primary/30"
                              : "bg-[#0a0a0c] border border-gray-800 hover:bg-gray-800/50"
                          }`}
                          onClick={() => {
                            // Make sure we're setting both the selectedHub and preselectedHub
                            setSelectedHub(hub.id);
                            setPreselectedHub(hub);
                            setIsHubPreselected(true);
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <Image
                              src={hub.iconUrl}
                              alt={hub.name}
                              width={48}
                              height={48}
                              className="rounded-full"
                            />
                            <div>
                              <div className="font-medium">{hub.name}</div>
                              <div className="text-sm text-gray-400 line-clamp-2">
                                {hub.description}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </>
            )}

            <div className="space-y-2">
              <Label htmlFor="channel">Discord Channel</Label>
              <div className="relative mb-4">
                <Input
                  type="text"
                  placeholder="Search channels..."
                  value={channelSearchQuery}
                  onChange={(e) => setChannelSearchQuery(e.target.value)}
                  className="bg-[#0a0a0c] border-gray-800 pl-10"
                />
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
              </div>

              <div className="max-h-[300px] overflow-y-auto space-y-2 pr-2">
                {filteredChannels.length === 0 ? (
                  <div className="text-center py-4">
                    <Hash className="h-6 w-6 mx-auto text-gray-400 mb-2" />
                    <p className="text-gray-400">No channels found</p>
                  </div>
                ) : (
                  filteredChannels.map((channel) => (
                    <div
                      key={channel.id}
                      className={`flex items-center p-3 rounded-md cursor-pointer ${
                        selectedChannel === channel.id
                          ? "bg-primary/20 border border-primary/30"
                          : "bg-[#0a0a0c] border border-gray-800 hover:bg-gray-800/50"
                      }`}
                      onClick={() => setSelectedChannel(channel.id)}
                    >
                      <ChannelIcon
                        type={channel.type}
                        className="h-4 w-4 mr-2 flex-shrink-0"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">
                          {channel.name}
                        </div>
                        {channel.parentName && (
                          <div className="text-xs text-gray-400 truncate">
                            in {channel.parentName}
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>

              <p className="text-xs text-gray-400 mt-2">
                Select the Discord channel where messages will be sent and
                received
              </p>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleConnect}
              disabled={isSubmitting || !selectedHub || !selectedChannel}
              className="w-full"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Connecting...
                </>
              ) : (
                <>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Connect to Hub
                </>
              )}
            </Button>
          </CardFooter>
        </Card>

        <Card className="border-gray-800 bg-[#0f1117]">
          <CardHeader>
            <CardTitle>Server</CardTitle>
            <CardDescription>
              The Discord server you&apos;re connecting
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3">
              <Image
                src={
                  server?.icon
                    ? server.icon
                    : `https://api.dicebear.com/7.x/identicon/svg?seed=${serverId}`
                }
                alt={server?.name || "Server"}
                width={64}
                height={64}
                className="rounded-full"
              />
              <div>
                <div className="font-medium text-lg">
                  {server?.name || "Unknown Server"}
                </div>
                <div className="flex items-center text-sm text-gray-400">
                  <Home className="h-3 w-3 mr-1" />
                  Discord Server
                </div>
              </div>
            </div>

            <div className="mt-6 space-y-2">
              <h3 className="text-sm font-medium">About Connections</h3>
              <p className="text-sm text-gray-400">
                Connecting your server to a hub allows members to chat across
                Discord servers.
              </p>
              <ul className="text-sm text-gray-400 space-y-1 list-disc pl-5 mt-2">
                <li>
                  Messages sent in the selected channel will be shared with the
                  hub
                </li>
                <li>
                  Messages from other servers in the hub will appear in your
                  channel
                </li>
                <li>You can disconnect at any time</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

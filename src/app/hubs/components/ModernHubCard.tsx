"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";
import { cn } from "@/lib/utils";
import { Heart, Star, Users } from "lucide-react";
import Link from "next/link";
import { memo } from "react";
import { useHubUpvote } from "../hooks/use-hub-upvote";
import Jo<PERSON><PERSON><PERSON><PERSON> from "./hub-detail/JoinButton";
import Image from "next/image";

// Star rating component
const StarRating = memo(
  ({
    averageRating,
    totalReviews,
  }: {
    averageRating: number;
    totalReviews: number;
  }) => {
    if (totalReviews <= 0) {
      return null;
    }

    return (
      <div className="flex items-center gap-1 text-xs">
        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
        <span className="font-medium text-yellow-400">
          {averageRating.toFixed(1)}
        </span>
        <span className="text-gray-400">({totalReviews})</span>
      </div>
    );
  }
);

StarRating.displayName = "StarRating";

// Main component
export function ModernHubCard({ hub }: { hub: SimplifiedHub }) {
  const { id, name, iconUrl, description, connections } = hub;
  const { liked, upvoteCount, handleUpvote } = useHubUpvote(id, hub.upvotes);

  // Get tags array
  const tags = hub.tags?.map(tag => typeof tag === 'string' ? tag : tag.name) || [];

  // Calculate average rating
  const averageRating = hub.reviews?.length
    ? hub.reviews.reduce((sum, review) => sum + review.rating, 0) / hub.reviews.length
    : 0;

  return (
    <div className="group relative flex flex-col overflow-hidden rounded-xl border border-gray-700/50 bg-gray-900 transition-all duration-300 hover:shadow-lg hover:shadow-primary/10 hover:-translate-y-1 hover:border-gray-600/70 h-full">
      <Link
        href={`/hubs/${id}`}
        className="absolute inset-0 z-10"
        aria-label={`View ${name} hub details`}
      />

      <div className="p-4 flex flex-col h-full">
        {/* Header with icon and name - fixed height */}
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center gap-3">
            <div className="relative h-10 w-10 rounded-full border-2 border-gray-700/50 overflow-hidden flex-shrink-0">
              {iconUrl ? (
                <Image
                  src={iconUrl}
                  alt={`${name} icon`}
                  width={40}
                  height={40}
                  className="object-cover w-full h-full"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-primary/20 text-primary font-bold text-lg">
                  {name.charAt(0).toUpperCase()}
                </div>
              )}
            </div>
            <div className="min-w-0 flex-1"> {/* Added min-width and flex-1 to handle text overflow */}
              <h3 className="font-bold text-white group-hover:text-primary transition-colors duration-300 line-clamp-1 text-sm">
                {name}
              </h3>

              {/* Stats row */}
              <div className="flex items-center gap-2 mt-0.5">
                {hub.reviews && hub.reviews.length > 0 && (
                  <StarRating
                    averageRating={averageRating}
                    totalReviews={hub.reviews.length}
                  />
                )}

                <div className="flex items-center gap-1 text-xs text-gray-400">
                  <Users className="h-3 w-3 text-blue-400" />
                  <span>{connections.length}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Upvote button - positioned relative to allow interaction */}
          <div className="relative z-20 flex-shrink-0 ml-2">
            <Button
              variant="outline"
              size="sm"
              className={cn(
                "flex items-center gap-1 px-2 h-7 rounded-full transition-all duration-200",
                "border hover:scale-105 active:scale-95",
                liked
                  ? "bg-primary/10 border-primary text-primary hover:bg-primary/20"
                  : "bg-gray-800 border-gray-700 text-gray-300 hover:border-primary/50 hover:text-primary",
              )}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleUpvote();
              }}
              aria-label={liked ? "Remove upvote" : "Upvote this hub"}
              aria-pressed={liked}
            >
              <Heart
                className={cn(
                  "h-3.5 w-3.5 transition-all duration-200",
                  liked ? "fill-primary text-primary scale-110" : "scale-100",
                )}
                aria-hidden="true"
              />
              <span className="text-xs font-medium">{upvoteCount}</span>
            </Button>
          </div>
        </div>

        {/* Description - fixed height with line clamp */}
        <div className="h-10 mb-3"> {/* Fixed height container */}
          <p className="text-xs text-gray-300 line-clamp-2">
            {description}
          </p>
        </div>

        {/* Spacer to push content to top and bottom */}
        <div className="flex-grow" />

        {/* Tags - fixed position from bottom */}
        <div className="flex flex-wrap gap-1 mb-3">
          {tags.slice(0, 2).map((tag) => (
            <span
              key={tag}
              className="px-1.5 py-0.5 text-xs rounded-full bg-gray-700/50 text-gray-300 border border-gray-700/50"
            >
              {tag}
            </span>
          ))}
          {tags.length > 2 && (
            <span className="px-1.5 py-0.5 text-xs rounded-full bg-gray-700/50 text-gray-300 border border-gray-700/50">
              +{tags.length - 2} more
            </span>
          )}
        </div>

        {/* Join button - fixed position at bottom */}
        <div className="relative z-20">
          <JoinButton hubName={name} hubId={id} />
        </div>
      </div>
    </div>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(ModernHubCard);

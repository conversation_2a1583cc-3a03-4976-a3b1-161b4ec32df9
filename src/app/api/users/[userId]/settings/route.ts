import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { z } from "zod";
import { Prisma } from "@/lib/generated/prisma/client";

// Define supported languages
const supportedLanguages = ["en", "hi", "es", "pt", "zh", "ru", "et"] as const;

// Schema for updating user settings
const updateUserSettingsSchema = z.object({
  mentionOnReply: z.boolean().optional(),
  locale: z.enum(supportedLanguages).optional(),
});

export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ userId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { userId } = await props.params;

    // Ensure users can only update their own settings
    if (session.user.id !== userId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = updateUserSettingsSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: validationResult.error.format(),
        },
        { status: 400 },
      );
    }

    const { mentionOnReply, locale } = validationResult.data;

    // Only include fields that were provided in the request
    const updateData: Prisma.UserUpdateInput = {};
    if (mentionOnReply !== undefined)
      updateData.mentionOnReply = mentionOnReply;
    if (locale !== undefined) updateData.locale = locale;

    // Update user settings in database
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        mentionOnReply: true,
        locale: true,
      },
    });

    return NextResponse.json({
      user: updatedUser,
      message: "Settings updated successfully",
    });
  } catch (error) {
    console.error("Error updating user settings:", error);
    return NextResponse.json(
      { error: "Failed to update user settings" },
      { status: 500 },
    );
  }
}

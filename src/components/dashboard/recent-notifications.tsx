"use client";

import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { trpc } from "@/utils/trpc";
import { formatDistanceToNow } from "date-fns";
import {
  AlertTriangle,
  Bell,
  CheckCircle,
  ExternalLink,
  Info,
} from "lucide-react";
import { motion } from "motion/react";
import Link from "next/link";
import { useInView } from "react-intersection-observer";

export function RecentNotifications() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Fetch recent announcements
  const { data: announcements, isLoading } =
    trpc.announcement.getAnnouncements.useQuery(undefined, {
      staleTime: 60 * 1000, // 1 minute stale time
    });

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "info":
        return <Info className="h-4 w-4 text-blue-400" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-400" />;
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      default:
        return <Bell className="h-4 w-4 text-gray-400" />;
    }
  };

  const getNotificationBorderColor = (type: string) => {
    switch (type) {
      case "info":
        return "border-blue-500/20 hover:border-blue-500/30";
      case "warning":
        return "border-yellow-500/20 hover:border-yellow-500/30";
      case "success":
        return "border-green-500/20 hover:border-green-500/30";
      default:
        return "border-gray-500/20 hover:border-gray-500/30";
    }
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={inView ? "show" : "hidden"}
      variants={container}
      className="h-full"
    >
      <Card className="border-gray-800 bg-gradient-to-b from-gray-900/50 to-gray-900/30 backdrop-blur-sm h-full">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5 text-indigo-400" />
            <div>
              <CardTitle className="text-xl font-bold">
                Recent Updates
              </CardTitle>
              <CardDescription>Latest announcements</CardDescription>
            </div>
          </div>
          <Link
            href="/dashboard/announcements"
            className="text-xs text-primary hover:underline flex items-center gap-1"
          >
            View all
            <ExternalLink className="h-3 w-3" />
          </Link>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {isLoading ? (
              // Loading skeleton
              Array.from({ length: 3 }).map((_, i) => (
                <motion.div key={i} variants={item}>
                  <div className="flex items-start space-x-3 rounded-md p-3 border border-gray-800/50">
                    <div className="h-4 w-4 bg-gray-700 rounded animate-pulse" />
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-700 rounded animate-pulse" />
                      <div className="h-3 bg-gray-800 rounded animate-pulse w-2/3" />
                    </div>
                  </div>
                </motion.div>
              ))
            ) : announcements && announcements.announcements.length > 0 ? (
              announcements.announcements.slice(0, 4).map((announcement) => {
                const timeAgo = formatDistanceToNow(
                  new Date(announcement.createdAt),
                  { addSuffix: true }
                );

                return (
                  <motion.div key={announcement.id} variants={item}>
                    <div
                      className={`flex items-start space-x-3 rounded-md p-3 border transition-colors duration-200 ${getNotificationBorderColor('info')}`}
                    >
                      <div className="mt-0.5">
                        {getNotificationIcon('info')}
                      </div>
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium text-white">
                          {announcement.title}
                        </p>
                        <p className="text-xs text-gray-400 line-clamp-2">
                          {announcement.content}
                        </p>
                        <p className="text-xs text-gray-500">{timeAgo}</p>
                      </div>
                    </div>
                  </motion.div>
                );
              })
            ) : (
              <motion.div variants={item} className="text-center py-8">
                <Bell className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-lg text-gray-300 mb-2">No recent updates</p>
                <p className="text-sm text-gray-400">
                  You&apos;re all caught up! Check back later for new announcements.
                </p>
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

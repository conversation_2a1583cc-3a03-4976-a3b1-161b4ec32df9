"use client";

import { pricingTiers } from "@/lib/pricing-constants";
import { PricingCard } from "./PricingCard";

interface PricingCardsSectionProps {
  compact?: boolean;
}

export function PricingCardsSection({ compact = false }: PricingCardsSectionProps) {
  return (
    <section className="py-12 relative">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-3 gap-8">
          {pricingTiers.map((tier, index) => (
            <PricingCard 
              key={tier.id} 
              tier={tier} 
              index={index} 
              compact={compact} 
            />
          ))}
        </div>
      </div>
    </section>
  );
}

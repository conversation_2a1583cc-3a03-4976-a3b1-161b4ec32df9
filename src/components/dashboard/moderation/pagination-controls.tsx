"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

interface PaginationControlsProps {
  currentPage: number;
  totalPages: number;
  selectedHubId: string;
}

export function PaginationControls({
  currentPage,
  totalPages,
  selectedHubId,
}: PaginationControlsProps) {
  if (totalPages <= 1) {
    return null;
  }

  return (
    <div className="flex justify-center mt-6">
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage <= 1}
          className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
          asChild
        >
          <Link
            href={{
              pathname: "/dashboard/moderation/blacklist",
              query: {
                ...(selectedHubId !== "all" ? { hubId: selectedHubId } : {}),
                page: currentPage > 1 ? currentPage - 1 : 1,
              },
            }}
          >
            Previous
          </Link>
        </Button>

        <div className="text-sm bg-gray-800/50 px-3 py-1 rounded-md border border-gray-700/50">
          Page {currentPage} of {totalPages}
        </div>

        <Button
          variant="outline"
          size="sm"
          disabled={currentPage >= totalPages}
          className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
          asChild
        >
          <Link
            href={{
              pathname: "/dashboard/moderation/blacklist",
              query: {
                ...(selectedHubId !== "all" ? { hubId: selectedHubId } : {}),
                page: currentPage < totalPages ? currentPage + 1 : totalPages,
              },
            }}
          >
            Next
          </Link>
        </Button>
      </div>
    </div>
  );
}

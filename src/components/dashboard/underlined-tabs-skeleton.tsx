"use client";

import type * as React from "react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface UnderlinedTabsSkeletonProps {
  tabs: {
    value: string;
    label: string;
    color?: "indigo" | "blue" | "green" | "purple" | "red";
  }[];
  children?: React.ReactNode;
  className?: string;
}

export function UnderlinedTabsSkeleton({
  tabs,
  children,
  className,
}: UnderlinedTabsSkeletonProps) {
  // Get the first tab value to use as default
  const defaultValue = tabs.length > 0 ? tabs[0].value : "default";

  return (
    <Tabs defaultValue={defaultValue} className={`w-full space-y-6 ${className || ""}`}>
      <div className="relative overflow-x-auto -mx-6 px-0 border-b border-gray-800/50 bg-gray-900/80 backdrop-blur-md z-10 shadow-sm transition-all duration-200 no-scrollbar">
        <div className="px-4 sm:px-6 w-full">
          {/* Scroll indicators for mobile */}
          <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-900/80 to-transparent pointer-events-none sm:hidden" />

          <TabsList className="w-full flex flex-nowrap justify-start sm:justify-center gap-2 sm:gap-6 bg-transparent max-w-screen-xl mx-auto h-auto p-0 rounded-none">
            {tabs.map((tab) => {
              // Define colors for each tab type
              const colorMap: Record<string, { borderColor: string; color: string }> = {
                indigo: { borderColor: "#6366f1", color: "#818cf8" },
                blue: { borderColor: "#3b82f6", color: "#60a5fa" },
                green: { borderColor: "#10b981", color: "#34d399" },
                purple: { borderColor: "#8b5cf6", color: "#a78bfa" },
                red: { borderColor: "#ef4444", color: "#f87171" },
                default: { borderColor: "#6366f1", color: "#818cf8" },
              };

              const color = tab.color || "indigo";

              return (
                <TabsTrigger
                  key={tab.value}
                  value={tab.value}
                  className="cursor-pointer px-3 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-400 border-b-[2px] border-transparent data-[state=active]:border-[var(--tab-border-color)] data-[state=active]:text-[var(--tab-text-color)] transition-all duration-200 hover:text-gray-300 bg-transparent rounded-none shadow-none focus:outline-none focus:ring-0 focus:shadow-none whitespace-nowrap"
                  style={
                    {
                      "--tab-border-color": colorMap[color].borderColor,
                      "--tab-text-color": colorMap[color].color,
                    } as React.CSSProperties
                  }
                >
                  <span className="text-xs sm:text-sm">{tab.label}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>
        </div>
      </div>
      {children}
    </Tabs>
  );
}

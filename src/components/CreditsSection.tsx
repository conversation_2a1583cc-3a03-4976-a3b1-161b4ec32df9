import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Github, UsersRound } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { DiscordIcon } from "./DiscordIcon";

interface Contributor {
  name: string;
  role: string;
  roleType: "developer" | "staff" | "translator";
  avatar?: string;
  github?: string;
  discord?: string;
}

const getRoleBadgeStyles = (roleType: Contributor["roleType"]) => {
  switch (roleType) {
    case "developer":
      return "bg-purple-600 dark:bg-purple-700 text-white";
    case "staff":
      return "bg-emerald-600 dark:bg-emerald-700 text-white";
    case "translator":
      return "bg-amber-600 dark:bg-amber-700 text-white";
    default:
      return "";
  }
};

const contributors: Contributor[] = [
  // Developers
  {
    name: "devoid.",
    role: "Lead Developer",
    roleType: "developer",
    avatar:
      "https://cdn.discordapp.com/avatars/701727675311587358/789fb02094d8963727a2cf6bf78c99fe.webp?size=4096",
    github: "dev-737",
    discord: "701727675311587358",
  },
  // Staff
  {
    name: "hecash",
    role: "Community Manager",
    roleType: "staff",
    avatar:
      "https://cdn.discordapp.com/avatars/1160735837940617336/1eab31719d918f0b739db953a40ed632.webp?size=4096",
    discord: "1160735837940617336",
  },
  {
    name: "orange_mitro",
    role: "Moderator",
    roleType: "staff",
    avatar:
      "https://cdn.discordapp.com/avatars/994411851557392434/430d95924ffea1709e235c0029a0a507.webp?size=4096",
    discord: "994411851557392434",
  },
  {
    name: "sqishsqash",
    role: "Moderator",
    roleType: "staff",
    avatar:
      "https://cdn.discordapp.com/avatars/853178500193583104/ffb2c9c90def8ae53904a01b0671d35f.webp?size=4096",
    discord: "853178500193583104",
  },
  // Translators
  {
    name: "spacelemoon",
    role: "Russian Translator",
    roleType: "translator",
    avatar:
      "https://cdn.discordapp.com/avatars/845357241132384286/954653d2f58cdf003709515df5820a0c.webp?size=4096",
    discord: "845357241132384286",
  },
  {
    name: "dannybarbosalimabr",
    role: "Portuguese Translator", // Corrected spelling
    roleType: "translator",
    avatar:
      "https://cdn.discordapp.com/avatars/1289961731115847803/08c837d3a564494a271869a4e0d44e81.webp?size=4096",
    discord: "1289961731115847803",
  },
  {
    name: "Chenxian.201277050224",
    role: "Chinese Translator",
    roleType: "translator",
  },
  {
    name: "akshut",
    role: "Hindi Translator",
    avatar:
      "https://cdn.discordapp.com/avatars/827745783964499978/88e4d6a6d306b1f51355eb4609522bee.webp?size=4096",
    roleType: "translator",
    discord: "827745783964499978",
  },
];

export const CreditsSection = () => {
  const sortedContributors = [...contributors].sort((a, b) => {
    if (a.roleType === b.roleType) return 0;
    if (a.roleType === "developer") return -1;
    if (b.roleType === "developer") return 1;
    if (a.roleType === "staff") return -1;
    if (b.roleType === "staff") return 1;
    return 0;
  });

  return (
    <section className="relative overflow-hidden py-32" id="team">
      {/* Enhanced gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-900/30 to-purple-900/20" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-purple-900/30 via-transparent to-transparent" />
      {/* Static gradient blobs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/5 w-96 h-96 bg-primary/20 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/6 w-96 h-96 bg-primary-alt/25 rounded-full blur-3xl" />
      </div>
      <div className="container mx-auto px-4 relative">
        <div className="text-center mb-20 relative z-10">
          <div className="inline-block mb-6">
            <div className="flex items-center justify-center bg-white/10 text-primary px-6 py-3 rounded-full border border-white/15 backdrop-blur-lg">
              <UsersRound className="w-5 h-5 mr-3" aria-hidden="true" />
              <span className="font-medium tracking-wide text-base">
                InterChat Team
              </span>
            </div>
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            The Talented Team Behind
            <span className="block mt-3 text-transparent bg-clip-text bg-gradient-to-r from-primary-alt via-primary to-primary-alt animate-gradient-x">
              InterChat&apos;s Success
            </span>
          </h2>
          <p className="max-w-3xl mx-auto text-lg text-gray-300/90">
            Meet the dedicated individuals who contribute their skills and
            passion to make InterChat a reality. From developers to community
            managers and translators, our team works tirelessly to provide you
            with the best possible cross-server Discord experience.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 relative z-10">
          {sortedContributors.map((contributor) => (
            <div
              key={contributor.name}
              className="group relative"
              itemScope // Added for Schema
              itemType="https://schema.org/Person" // Added for Schema
            >
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-primary/20 to-primary-alt/20 blur-xl opacity-0 group-hover:opacity-70 transition-opacity duration-700" />

              <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 group-hover:border-primary/40 transition-all duration-300 relative">
                <div className="flex items-center text-start">
                  {contributor.avatar ? (
                    <Image
                      src={contributor.avatar}
                      alt={`${contributor.name}'s avatar`} // Improved alt text
                      width={60}
                      height={60}
                      className="rounded-full border border-white/20"
                      itemProp="image" // Added for Schema
                    />
                  ) : (
                    <div className="w-14 h-14 rounded-full flex items-center justify-center bg-primary/10 text-primary">
                      <span className="text-lg font-semibold">
                        {contributor.name[0]}
                      </span>
                    </div>
                  )}

                  <div className="ml-4 flex-1">
                    <h4
                      className="font-medium text-white group-hover:text-primary-alt transition-colors duration-300"
                      itemProp="name"
                    >
                      {contributor.name}
                    </h4>
                    <Badge
                      className={cn(
                        "mt-1",
                        getRoleBadgeStyles(contributor.roleType),
                      )}
                      itemProp="jobTitle" // Added for Schema
                    >
                      {contributor.role}
                    </Badge>
                  </div>

                  <div className="flex space-x-2">
                    {contributor.github && (
                      <Link
                        href={`https://github.com/${contributor.github}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 rounded-full hover:bg-white/10 transition-colors"
                        // Added aria-label
                        aria-label={`View ${contributor.name}'s GitHub profile`}
                      >
                        <Github
                          className="h-5 w-5 text-gray-300"
                          aria-hidden="true"
                        />
                      </Link>
                    )}
                    {contributor.discord && (
                      <Link
                        href={`https://discord.com/users/${contributor.discord}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 rounded-full hover:bg-white/10 transition-colors"
                        // Added aria-label
                        aria-label={`Contact ${contributor.name} on Discord`}
                      >
                        <DiscordIcon
                          className="h-5 w-5 fill-gray-300"
                          aria-hidden="true"
                        />
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

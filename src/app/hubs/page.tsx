import type { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { <PERSON><PERSON>Hero } from "./components/HubsHero";
import { ModernHubContent } from "./components/ModernHubContent";
import HubContentLoading from "./components/HubContentLoading";
import { ClientOnboardingWrapper } from "./components/ClientOnboardingWrapper";

// Force dynamic rendering to ensure fresh data
export const dynamic = "force-dynamic";

/**
 * Generate metadata for SEO optimization
 */
export async function generateMetadata(): Promise<Metadata> {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://interchat.tech";

  return {
    title:
      "Discover InterChat Hubs | Connect Your Discord Server to Active Communities",
    description:
      "Browse and join thriving InterChat Hubs. Find active servers for gaming, art, technology, and more. Connect your Discord server today!",
    keywords: [
      "discord hubs",
      "discord server connections",
      "connect discord servers",
      "discord communities",
      "active discord servers",
      "discord server discovery",
      "join discord communities",
      "interchat hubs",
    ],
    openGraph: {
      title:
        "Discover InterChat Hubs | Connect Your Discord Server to Active Communities",
      description:
        "Browse and join thriving Discord communities with InterChat Hubs. Find active servers for gaming, art, technology, and more. Connect your Discord server today!",
      type: "website",
      url: `${baseUrl}/hubs`,
      images: [
        {
          url: `${baseUrl}/features/HubDiscovery.png`,
          width: 1200,
          height: 630,
          alt: "InterChat Hubs Discovery Page",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      images: [`${baseUrl}/features/HubDiscovery.png`],
      title: "Discover InterChat Hubs | Connect Your Discord Server",
      description:
        "Browse and join thriving Discord communities with InterChat Hubs. Find active servers for gaming, art, technology, and more.",
      creator: "@737_dev",
      site: "@interchatapp",
    },
    alternates: {
      canonical: `${baseUrl}/hubs`,
    },
  };
}

/**
 * Main page component for the Hubs discovery page
 * Uses Suspense to allow the hero section to render immediately
 * while the data-dependent content loads separately
 */
export default function HubsPage() {
  return (
    <>
      {/* Hero section with search - renders immediately */}
      <HubsHero />

      {/* Data-dependent content with loading state */}
      <Suspense fallback={<HubContentLoading />}>
        <ModernHubContent />
      </Suspense>

      {/* Onboarding tooltip for new users - client component wrapper */}
      <ClientOnboardingWrapper />
    </>
  );
}

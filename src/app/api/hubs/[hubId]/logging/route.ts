import { auth } from "@/auth";
import { PermissionLevel } from "@/lib/constants";
import type { HubLogConfig } from "@/lib/generated/prisma/client";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { type NextRequest, NextResponse } from "next/server";

export async function PATCH(
	request: NextRequest,
	props: { params: Promise<{ hubId: string }> },
) {
	try {
		const session = await auth();
		if (!session?.user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { hubId } = await props.params;

		// Check if the user has permission to edit this hub
		const permissionLevel = await getUserHubPermission(session.user.id, hubId);
		if (permissionLevel < PermissionLevel.MANAGER) {
			return NextResponse.json({ error: "Forbidden" }, { status: 403 });
		}

		// Get the request body
		const { logConfig } = await request.json() as { logConfig: HubLogConfig };

		// Validate the log config
		if (!logConfig || typeof logConfig !== "object") {
			return NextResponse.json(
				{ error: "Invalid log configuration" },
				{ status: 400 },
			);
		}

		// Check if the hub exists
		const hub = await prisma.hub.findUnique({
			where: { id: hubId },
			select: { logConfig: true },
		});

		if (!hub) {
			return NextResponse.json({ error: "Hub not found" }, { status: 404 });
		}

		// Update or create the log config
		const updatedLogConfig = await prisma.hubLogConfig.upsert({
			where: {
				hubId,
			},
			create: {
				hubId,
				modLogsChannelId: logConfig.modLogsChannelId,
				modLogsRoleId: logConfig.modLogsRoleId,
				joinLeavesChannelId: logConfig.joinLeavesChannelId,
				joinLeavesRoleId: logConfig.joinLeavesRoleId,
				appealsChannelId: logConfig.appealsChannelId,
				appealsRoleId: logConfig.appealsRoleId,
				reportsChannelId: logConfig.reportsChannelId,
				reportsRoleId: logConfig.reportsRoleId,
				networkAlertsChannelId: logConfig.networkAlertsChannelId,
				networkAlertsRoleId: logConfig.networkAlertsRoleId,
			},
			update: {
				modLogsChannelId: logConfig.modLogsChannelId,
				modLogsRoleId: logConfig.modLogsRoleId,
				joinLeavesChannelId: logConfig.joinLeavesChannelId,
				joinLeavesRoleId: logConfig.joinLeavesRoleId,
				appealsChannelId: logConfig.appealsChannelId,
				appealsRoleId: logConfig.appealsRoleId,
				reportsChannelId: logConfig.reportsChannelId,
				reportsRoleId: logConfig.reportsRoleId,
				networkAlertsChannelId: logConfig.networkAlertsChannelId,
				networkAlertsRoleId: logConfig.networkAlertsRoleId,
			},
		});

		return NextResponse.json({ logConfig: updatedLogConfig });
	} catch (error) {
		console.error("Error updating hub logging configuration:", error);
		return NextResponse.json(
			{ error: "Failed to update hub logging configuration" },
			{ status: 500 },
		);
	}
}

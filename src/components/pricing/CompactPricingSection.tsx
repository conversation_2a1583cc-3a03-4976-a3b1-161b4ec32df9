"use client";

import { PricingCardsSection } from "./PricingCardsSection";
import Link from "next/link";

interface CompactPricingSectionProps {
  title?: string;
  description?: string;
  showViewAllLink?: boolean;
}

export function CompactPricingSection({
  title = "Choose Your Plan",
  description = "Select the plan that best fits your community's needs.",
  showViewAllLink = true,
}: CompactPricingSectionProps) {
  return (
    <div className="py-8">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h2 className="text-2xl font-bold text-white mb-2">{title}</h2>
            <p className="text-gray-400">{description}</p>
          </div>
          {showViewAllLink && (
            <Link
              href="/pricing"
              className="text-primary hover:text-primary-alt transition-colors mt-4 md:mt-0"
            >
              View all features →
            </Link>
          )}
        </div>
        <PricingCardsSection compact />
      </div>
    </div>
  );
}

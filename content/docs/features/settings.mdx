---
title: Configure Hub Settings
description: Learn how to configure your hub's settings.
---

## Hub Settings

Configure hub behavior using `/hub config settings`:

| Setting        | Description                                                                                          |
| -------------- | ---------------------------------------------------------------------------------------------------- |
| `BlockInvites` | Prevents sharing of Discord invite links                                                             |
| `UseNicknames` | Displays server nicknames instead of usernames. If user has no nickname, their display name is shown |
| `BlockNSFW`    | Filters NSFW images                                                                                  |
| `HideLinks`    | Prevents links from being sent in chat                                                               |
| `SpamFilter`   | Enables spam detection and blocking                                                                  |
| `Reactions`    | Allows reacting to messages across linked channels                                                   |

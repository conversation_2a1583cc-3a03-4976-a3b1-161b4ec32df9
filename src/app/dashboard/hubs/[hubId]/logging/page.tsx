import { auth } from "@/auth";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { HubNavigationTabs } from "@/components/dashboard/hubs/hub-navigation-tabs";
import { HubLoggingForm } from "@/components/dashboard/hubs/hub-logging-form";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { AlertCircle, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";

interface HubLoggingPageProps {
  params: Promise<{
    hubId: string;
  }>;
}

export default async function HubLoggingPage({
  params: paramsPromise,
}: HubLoggingPageProps) {
  const { hubId } = await paramsPromise;
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Get the user's permission level for this hub
  const permissionLevel = await getUserHubPermission(session.user.id, hubId);
  const canModerate = permissionLevel >= PermissionLevel.MODERATOR;
  const canEdit = permissionLevel >= PermissionLevel.MANAGER;

  // If the user doesn't have permission to edit the hub, redirect to the hub overview
  if (!canEdit) {
    redirect(`/dashboard/hubs/${hubId}`);
  }

  // Get the hub data
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    include: {
      logConfig: true,
    },
  });

  if (!hub) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href={`/dashboard/hubs/${hubId}`}>
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">
            {hub.name} Logging
          </h1>
        </div>
      </div>
      <HubNavigationTabs
        hubId={hubId}
        currentTab="logging"
        canModerate={canModerate}
        canEdit={canEdit}
      />
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>Logging Configuration</CardTitle>
          <CardDescription>
            Configure logging channels for different hub activities
          </CardDescription>
        </CardHeader>
        <CardContent>
          {canEdit ? (
            <HubLoggingForm
              hubId={hubId}
              initialLogConfig={hub.logConfig || null}
            />
          ) : (
            <div className="flex items-center justify-center p-6">
              <AlertCircle className="h-5 w-5 mr-2 text-amber-500" />
              <p className="text-gray-400">
                You don&apos;t have permission to edit this hub.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

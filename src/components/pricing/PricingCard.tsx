"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Check, ExternalLink } from "lucide-react";
import { motion } from "motion/react";
import Link from "next/link";
import type { PricingTier } from "@/lib/pricing-constants";

interface PricingCardProps {
  tier: PricingTier;
  index: number;
  compact?: boolean;
}

export function PricingCard({ tier, index, compact = false }: PricingCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className={`relative rounded-2xl overflow-hidden ${
        tier.popular
          ? "border-2 border-primary shadow-lg shadow-primary/20"
          : "border border-gray-800/50"
      }`}
      style={{ isolation: 'isolate' }}
    >
      {tier.popular && (
        <div className="absolute top-0 right-0 bg-primary text-white text-xs font-bold px-3 py-1.5 rounded-bl-md z-10">
          MOST POPULAR
        </div>
      )}

      <div
        className={`p-6 bg-gradient-to-br ${tier.gradientFrom} ${tier.gradientTo} h-full flex flex-col`}
      >
        <div className="mb-6">
          <div
            className={`inline-flex items-center justify-center w-12 h-12 rounded-full mb-4 ${
              tier.popular
                ? "bg-primary/20 text-primary"
                : "bg-white/10 text-white"
            }`}
          >
            {tier.icon}
          </div>
          <h3 className="text-2xl font-bold text-white mb-2">
            {tier.name}
          </h3>
          <p className="text-gray-400 mb-4">{tier.description}</p>
          <div className="mb-4">
            <span className="text-3xl font-bold text-white">
              {tier.price}
            </span>
            {tier.period && (
              <span className="text-gray-400 ml-1">
                {tier.period}
              </span>
            )}
          </div>
        </div>

        {!compact && (
          <div className="mb-6 flex-grow">
            <ul className="space-y-3">
              {tier.features.map((feature) => (
                <li
                  key={feature}
                  className="flex items-start text-gray-300"
                >
                  <Check className="h-5 w-5 text-green-400 mr-2 shrink-0 mt-0.5" />
                  <span>{feature}</span>
                </li>
              ))}
              {tier.limitations?.map((limitation) => (
                <li
                  key={limitation}
                  className="flex items-start text-gray-400"
                >
                  <span className="text-red-400 mr-2">✕</span>
                  <span>{limitation}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {compact && (
          <div className="mb-6 flex-grow">
            <ul className="space-y-3">
              {tier.features.slice(0, 3).map((feature) => (
                <li
                  key={feature}
                  className="flex items-start text-gray-300"
                >
                  <Check className="h-5 w-5 text-green-400 mr-2 shrink-0 mt-0.5" />
                  <span>{feature}</span>
                </li>
              ))}
              {tier.features.length > 3 && (
                <li className="text-gray-400 text-sm">
                  + {tier.features.length - 3} more features
                </li>
              )}
            </ul>
          </div>
        )}

        <div>
          <Link
            href={tier.ctaLink}
            target={tier.ctaLink.startsWith("http") ? "_blank" : ""}
            rel={
              tier.ctaLink.startsWith("http")
                ? "noopener noreferrer"
                : ""
            }
          >
            <Button
              className={`w-full py-6 ${
                tier.popular
                  ? "bg-primary hover:bg-primary/90 text-white"
                  : "bg-white/10 hover:bg-white/20 text-white border border-white/20"
              } transition-all duration-300 hover:shadow-lg group`}
            >
              {tier.cta}
              {tier.ctaLink.startsWith("http") && (
                <ExternalLink className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
              )}
            </Button>
          </Link>
        </div>
      </div>
    </motion.div>
  );
}

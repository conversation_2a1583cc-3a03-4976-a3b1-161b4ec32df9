"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import {
  Flame,
  Plus,
  Search,
  SortAsc,
  Users,
  X,
  Heart,
  Clock,
  Sparkles,
  ThumbsUp,
} from "lucide-react";
import { useRef, useState } from "react";
import { HubSearchDropdown } from "./hub-search-dropdown";
import { SortOptions } from "../constants";

export type ExcludedSortOptions = Exclude<
  SortOptions,
  SortOptions.MostUpvotedNew | SortOptions.MostRecentPopular
>;

interface HubFiltersProps {
  searchTerm: string;
  placeholder?: string;
  isLoading: boolean;
  sortBy: ExcludedSortOptions;
  hasSearched: boolean;
  onSearchTermUpdate: (term: string) => void;
  onSearchSubmit: (term: string) => void;
  handleSortChange: (sort: ExcludedSortOptions) => void;
}

export const SORT_OPTIONS = {
  [SortOptions.Trending]: {
    label: "Trending",
    icon: <Flame className="w-4 h-4 text-orange-500" />,
  },
  [SortOptions.Servers]: {
    label: "Most Connections",
    icon: <Users className="w-4 h-4 text-green-500" />,
  },
  [SortOptions.Created]: {
    label: "Newest First",
    icon: <Plus className="w-4 h-4 text-purple-500" />,
  },
  [SortOptions.Name]: {
    label: "Name (A-Z)",
    icon: <SortAsc className="w-4 h-4 text-gray-500" />,
  },
  [SortOptions.Upvotes]: {
    label: "Most Upvoted",
    icon: <Heart className="w-4 h-4 text-red-500" />,
  },
  [SortOptions.LastActive]: {
    label: "Recently Active",
    icon: <Clock className="w-4 h-4 text-blue-500" />,
  },
  [SortOptions.MostActive]: {
    label: "Most Active",
    icon: <Clock className="w-4 h-4 text-blue-500" />,
  },
  [SortOptions.MostRecentPopular]: {
    label: "Most Recent Popular",
    icon: <Sparkles className="w-4 h-4 text-yellow-500" />,
  },
  [SortOptions.MostUpvotedNew]: {
    label: "Most New Upvoted",
    icon: <ThumbsUp className="w-4 h-4 text-sky-500" />,
  },
};

export function HubFilters({
  searchTerm,
  placeholder,
  isLoading,
  sortBy,
  hasSearched,
  onSearchTermUpdate,
  onSearchSubmit,
  handleSortChange,
}: HubFiltersProps) {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [showDropdown, setShowDropdown] = useState(false);

  const handleSearchSubmit = (e?: React.FormEvent) => {
    e?.preventDefault();
    if (searchTerm.trim()) {
      onSearchSubmit(searchTerm);
      setShowDropdown(false);
    }
  };

  const handleSearchInput = (value: string) => {
    onSearchTermUpdate(value);
    setShowDropdown(true);
  };

  const getSortIcon = (
    sort: Exclude<
      SortOptions,
      SortOptions.MostUpvotedNew | SortOptions.MostRecentPopular
    >,
  ) => SORT_OPTIONS[sort].icon;
  const getSortLabel = (
    sort: Exclude<
      SortOptions,
      SortOptions.MostUpvotedNew | SortOptions.MostRecentPopular
    >,
  ) => SORT_OPTIONS[sort].label;

  return (
    <div className="space-y-4">
      <div
        className={cn(
          "bg-white dark:bg-gray-950 rounded-xl border border-gray-200 dark:border-gray-800 shadow-lg transition-all relative",
          isLoading && "opacity-80",
        )}
      >
        <div className="p-4 md:p-6">
          <form onSubmit={handleSearchSubmit} className="relative flex gap-2">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400 dark:text-gray-500" />
              <Input
                ref={searchInputRef}
                type="text"
                placeholder={placeholder}
                value={searchTerm}
                onChange={(e) => handleSearchInput(e.target.value)}
                onFocus={() => setShowDropdown(true)}
                className="pl-10 h-14 bg-gray-50 dark:bg-gray-900 border-gray-200 dark:border-gray-800 focus-visible:ring-primary/30 focus-visible:border-primary transition-all rounded-lg text-base"
                disabled={isLoading}
              />
              {searchTerm && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="cursor-pointer absolute right-2 top-1/2 transform -translate-y-1/2 h-10 w-10 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                  onClick={() => {
                    onSearchTermUpdate("");
                    setShowDropdown(false);
                  }}
                  disabled={isLoading}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}

              {/* search dropdown */}
              {showDropdown && searchTerm.trim() && (
                <div className="absolute left-0 right-0 top-full mt-1 z-50">
                  <HubSearchDropdown searchTerm={searchTerm} />
                </div>
              )}
            </div>

            <Button
              type="submit"
              className="h-14 w-14 bg-primary hover:bg-primary/90 gap-2 cursor-pointer"
              disabled={isLoading || !searchTerm.trim()}
            >
              <Search className="h-6 w-6" />
            </Button>
          </form>
        </div>
      </div>

      {/* Sort dropdown - Only shown when search is active */}
      {hasSearched && (
        <div className="flex justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="h-10 gap-2 bg-gray-50 dark:bg-gray-900 border-gray-200 dark:border-gray-800 cursor-pointer"
                disabled={isLoading}
              >
                {getSortIcon(sortBy)}
                <span>Sort by {getSortLabel(sortBy)}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {Object.keys(SORT_OPTIONS).map((sort) => (
                <DropdownMenuItem
                  key={sort}
                  onClick={() => handleSortChange(sort as ExcludedSortOptions)}
                  className="gap-2"
                >
                  {getSortIcon(sort as ExcludedSortOptions)}
                  <span>{getSortLabel(sort as ExcludedSortOptions)}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
    </div>
  );
}

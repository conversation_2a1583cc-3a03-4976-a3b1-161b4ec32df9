/**
 * tRPC API handler for Next.js App Router
 */
import { fetchRequestHandler } from '@trpc/server/adapters/fetch';
import { appRouter } from '@/server/routers';
import { createContext } from '@/server/trpc';
import type { NextRequest } from 'next/server';

/**
 * Handle tRPC requests
 */
const handler = async (req: NextRequest) => {
  try {
    return await fetchRequestHandler({
      endpoint: '/api/trpc',
      req,
      router: appRouter,
      createContext,
      onError:
        process.env.NODE_ENV === 'development'
          ? ({ path, error }) => {
              console.error(`❌ tRPC error on ${path ?? '<no-path>'}: ${error.message}`);
              console.error('Error details:', error);
            }
          : undefined,
    });
  } catch (error) {
    console.error('Unhandled tRPC error:', error);
    throw error;
  }
};

export { handler as GET, handler as POST };

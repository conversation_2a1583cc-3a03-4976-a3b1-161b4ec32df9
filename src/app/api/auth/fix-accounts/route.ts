import { auth } from "@/auth";
import { signOut } from "next-auth/react";
import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
	try {
		const session = await auth();

		// Only allow authenticated users
		if (!session?.user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const userId = session.user.id;

		// Find all accounts for this user
		const accounts = await prisma.account.findMany({
			where: {
				OR: [{ userId }, { providerAccountId: userId }],
				provider: "discord",
			},
		});

		for (const account of accounts) {
			if (
				account.expires_at &&
				account.expires_at < Math.floor(Date.now() / 1000)
			) {
				await prisma.account.delete({
					where: { id: account.id },
				});
        await signOut({ callbackUrl: "/login" });
        return NextResponse.json({
          message: "Deleted expired account, please login again.",
          account: { id: account.id, userId: account.userId },
        });
			}
		}

		// Update all accounts to ensure they're linked to the user
		const updatedAccounts = await Promise.all(
			accounts.map((account) =>
				prisma.account.update({
					where: { id: account.id },
					data: { userId },
				}),
			),
		);

		return NextResponse.json({
			message: "Fixed account records",
			accountsFixed: updatedAccounts.length,
			accounts: updatedAccounts.map((a) => ({ id: a.id, userId: a.userId })),
		});
	} catch (error) {
		console.error("Error fixing accounts:", error);
		return NextResponse.json(
			{ error: "Failed to fix accounts, please login again." },
			{ status: 500 },
		);
	}
}

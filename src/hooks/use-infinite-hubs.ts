"use client";

import { useInfiniteQuery } from "@/lib/tanstack-query";
import { fetchApi } from "@/lib/api";
import { useErrorNotification } from "./use-error-notification";
import type {
	SortOptions,
	ContentFilter,
	VerificationStatus,
} from "@/app/hubs/constants";
import type {
	Connection,
	Hub,
	HubModerator,
	HubReview,
	HubUpvote,
} from "@/lib/generated/prisma/client";

// Types
export interface SimplifiedHub extends Hub {
	moderators: (HubModerator & {
		user: { id: string; name: string | null; image: string | null } | null;
	})[];
	connections: Pick<Connection, "id" | "serverId" | "lastActive">[];
	upvotes: HubUpvote[];
	reviews: HubReview[];
	tags: { name: string }[];
}

export interface PaginationInfo {
	page: number;
	totalPages: number;
	totalItems: number;
	hasMore: boolean;
}

export interface HubsResponse {
	hubs: SimplifiedHub[];
	pagination: PaginationInfo;
}

// Enhanced filter options interface
export interface HubFilterOptions {
	search?: string;
	tags?: string[];
	sort?: SortOptions;
	contentFilter?: ContentFilter;
	verificationStatus?: VerificationStatus;
	language?: string;
	region?: string;
	minMembers?: number;
	maxMembers?: number;
}

// Query keys
export const infiniteHubKeys = {
	all: ["infiniteHubs"] as const,
	lists: () => [...infiniteHubKeys.all, "list"] as const,
	list: (filters: HubFilterOptions) =>
		[...infiniteHubKeys.lists(), filters] as const,
};

// Fetch hubs with pagination
export async function getHubs(
	params: HubFilterOptions & {
		skip?: number;
		limit?: number;
	},
): Promise<HubsResponse> {
	const queryParams = new URLSearchParams();

	if (params.search) {
		queryParams.append("search", params.search);
	}

	if (params.tags && params.tags.length > 0) {
		queryParams.append("tags", params.tags.join(","));
	}

	if (params.sort) {
		queryParams.append("sort", params.sort);
	}

	// Add new filter parameters
	if (params.contentFilter) {
		queryParams.append("contentFilter", params.contentFilter);
	}

	if (params.verificationStatus) {
		queryParams.append("verificationStatus", params.verificationStatus);
	}

	if (params.language) {
		queryParams.append("language", params.language);
	}

	if (params.region) {
		queryParams.append("region", params.region);
	}

	if (params.minMembers !== undefined) {
		queryParams.append("minMembers", params.minMembers.toString());
	}

	if (params.maxMembers !== undefined) {
		queryParams.append("maxMembers", params.maxMembers.toString());
	}

	if (params.skip !== undefined) {
		queryParams.append("skip", params.skip.toString());
	}

	if (params.limit !== undefined) {
		queryParams.append("limit", params.limit.toString());
	}

	return fetchApi<HubsResponse>(`/api/hubs?${queryParams.toString()}`);
}

// Hook for infinite query of hubs
export function useInfiniteHubs(
	params: HubFilterOptions & {
		limit?: number;
	},
) {
	const {
		search,
		tags,
		sort,
		contentFilter,
		verificationStatus,
		language,
		region,
		minMembers,
		maxMembers,
		limit = 12,
	} = params;

	const query = useInfiniteQuery({
		queryKey: infiniteHubKeys.list({
			search,
			tags,
			sort,
			contentFilter,
			verificationStatus,
			language,
			region,
			minMembers,
			maxMembers,
		}),
		queryFn: ({ pageParam = 0 }) =>
			getHubs({
				search,
				tags,
				sort,
				contentFilter,
				verificationStatus,
				language,
				region,
				minMembers,
				maxMembers,
				skip: pageParam,
				limit,
			}),
		initialPageParam: 0,
		getNextPageParam: (lastPage, _, lastPageParam) => {
			if (!lastPage.pagination.hasMore) return null;
			return lastPageParam + lastPage.hubs.length;
		},
		staleTime: 1000 * 60 * 5, // 5 minutes
	});

	// Handle error notification
	useErrorNotification({
		isError: query.isError,
		error: query.error,
		title: "Error",
		description: "Failed to load hubs",
	});

	// Flatten the pages of hubs into a single array and filter out duplicates
	const hubs =
		query.data?.pages.reduce<SimplifiedHub[]>((acc, page) => {
			// Filter out hubs that are already in the accumulated array
			const uniqueHubs = page.hubs.filter(
				(hub) => !acc.some((existingHub) => existingHub.id === hub.id),
			);
			// biome-ignore lint/performance/noAccumulatingSpread: <explanation>
			return [...acc, ...uniqueHubs];
		}, []) || [];

	// Determine if there are more hubs to load
	const hasMore = query.hasNextPage || false;

	return {
		...query,
		hubs,
		hasMore,
	};
}

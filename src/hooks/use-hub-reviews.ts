"use client";

import { useQuery, useMutation, useQueryClient } from "@/lib/tanstack-query";
import { useToast } from "@/components/ui/use-toast";
import { fetchApi } from "@/lib/api";
import { hubKeys } from "./use-hub";
import { useErrorNotification } from "./use-error-notification";

// Query keys
export const reviewKeys = {
  all: ["reviews"] as const,
  lists: () => [...reviewKeys.all, "list"] as const,
  list: (hubId: string) => [...reviewKeys.lists(), hubId] as const,
  details: () => [...reviewKeys.all, "detail"] as const,
  detail: (id: string) => [...reviewKeys.details(), id] as const,
};

// Fetch reviews for a hub
export async function getHubReviews(hubId: string) {
  return fetchApi(`/api/hubs/${hubId}/reviews`);
}

// Hook for fetching hub reviews
export function useHubReviews(hubId: string) {
  const query = useQuery({
    queryKey: reviewKeys.list(hubId),
    queryFn: () => getHubReviews(hubId),
  });

  // Handle error with useErrorNotification
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: "Error",
    description: "Failed to load reviews",
  });

  return query;
}

// Submit a review for a hub
export async function submitHubReview(hubId: string, data: { rating: number; comment: string }) {
  return fetchApi(`/api/hubs/${hubId}/reviews`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
}

// Hook for submitting a hub review
export function useSubmitHubReview(hubId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: { rating: number; comment: string }) => submitHubReview(hubId, data),
    onSuccess: () => {
      toast({
        title: "Review submitted",
        description: "Thank you for your feedback!",
        duration: 2000,
      });

      // Invalidate both the reviews and the hub queries
      queryClient.invalidateQueries({ queryKey: reviewKeys.list(hubId) });
      queryClient.invalidateQueries({ queryKey: hubKeys.detail(hubId) });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to submit review: ${error.message}`,
        variant: "destructive",
      });
    },
  });
}

"use client";

import { motion } from "motion/react";
import { useEffect, useState } from "react";

// Generate deterministic values based on index
function getParticleProps(index: number) {
  // Use prime numbers for better distribution
  const width = ((index * 7919) % 60) + 20; // 20-80
  const height = ((index * 6997) % 60) + 20; // 20-80
  const left = (index * 5039) % 100; // 0-100%
  const top = (index * 4993) % 100; // 0-100%
  const xMove = ((index * 3989) % 40) - 20; // -20 to 20
  const yMove = ((index * 3967) % 40) - 20; // -20 to 20
  const duration = (index % 5) + 10; // 10-14 seconds

  return { width, height, left, top, xMove, yMove, duration };
}

interface AnimatedPageHeroProps {
  title: string;
  description: string;
  stats?: Array<{
    value: string | number;
    label: string;
    icon: React.ReactNode;
  }>;
  gradientFrom?: string;
  gradientVia?: string;
  gradientTo?: string;
  particleColors?: string[];
}

export function AnimatedPageHero({
  title,
  description,
  stats = [],
  gradientFrom = "from-purple-900/30",
  gradientVia = "via-blue-900/20",
  gradientTo = "to-indigo-900/30",
  particleColors = ["bg-purple-500/20", "bg-blue-500/20", "bg-indigo-500/20"],
}: AnimatedPageHeroProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Only render animations after component is mounted on client
  const renderParticles = isMounted ? (
    <div className="absolute inset-0 z-0">
      {Array.from({ length: 15 }).map((_, i) => {
        const { width, height, left, top, xMove, yMove, duration } =
          getParticleProps(i);
        return (
          <motion.div
            key={i}
            className={`absolute rounded-full ${
              i % 3 === 0
                ? particleColors[0]
                : i % 3 === 1
                  ? particleColors[1]
                  : particleColors[2]
            }`}
            style={{
              width,
              height,
              left: `${left}%`,
              top: `${top}%`,
            }}
            animate={{
              x: [0, xMove],
              y: [0, yMove],
              opacity: [0.1, 0.3, 0.1],
            }}
            transition={{
              duration,
              repeat: Infinity,
              repeatType: "reverse",
            }}
          />
        );
      })}
    </div>
  ) : null;

  return (
    <div className="relative h-[25vh] md:h-[30vh] mb-8 overflow-hidden rounded-xl">
      {/* Background gradient */}
      <div
        className={`absolute inset-0 bg-gradient-to-br ${gradientFrom} ${gradientVia} ${gradientTo} z-0`}
      />

      {/* Animated particles - client-side only with deterministic values */}
      {renderParticles}

      {/* Content container */}
      <div className="relative z-10 h-full flex flex-col items-center justify-center px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-white mb-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1, duration: 0.5 }}
          >
            {title}
          </motion.h2>
          <motion.p
            className="text-lg text-gray-300 max-w-2xl mx-auto mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            {description}
          </motion.p>

          {stats.length > 0 && (
            <motion.div
              className="flex flex-wrap justify-center gap-6 mt-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="flex items-center gap-2 bg-gray-900/50 backdrop-blur-sm px-4 py-2 rounded-lg border border-gray-800/50"
                >
                  <div className="text-gray-300">{stat.icon}</div>
                  <span className="text-xl font-bold text-white">
                    {stat.value}
                  </span>
                  <span className="text-gray-400">{stat.label}</span>
                </div>
              ))}
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  );
}

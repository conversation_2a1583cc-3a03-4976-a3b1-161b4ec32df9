"use client";

import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import {
  AlertCircle,
  AlertTriangle,
  Bell,
  Check,
  Flag,
  Info,
  MessageSquare,
  Save,
  Shield,
  Users
} from "lucide-react";
import { SimpleDiscordChannelSelector } from "./simple-discord-channel-selector";
import { SimpleDiscordRoleSelector } from "./simple-discord-role-selector";
import { useState } from "react";

interface LogConfig {
  id?: string;
  hubId: string;
  modLogsChannelId?: string | null;
  modLogsRoleId?: string | null;
  joinLeavesChannelId?: string | null;
  joinLeavesRoleId?: string | null;
  appealsChannelId?: string | null;
  appealsRoleId?: string | null;
  reportsChannelId?: string | null;
  reportsRoleId?: string | null;
  networkAlertsChannelId?: string | null;
  networkAlertsRoleId?: string | null;
}

interface HubLoggingFormProps {
  hubId: string;
  initialLogConfig: LogConfig | null;
}

export function HubLoggingForm({
  hubId,
  initialLogConfig,
}: HubLoggingFormProps) {
  const { toast } = useToast();
  const [logConfig, setLogConfig] = useState<LogConfig>(
    initialLogConfig || {
      hubId,
      modLogsChannelId: null,
      modLogsRoleId: null,
      joinLeavesChannelId: null,
      joinLeavesRoleId: null,
      appealsChannelId: null,
      appealsRoleId: null,
      reportsChannelId: null,
      reportsRoleId: null,
      networkAlertsChannelId: null,
      networkAlertsRoleId: null,
    }
  );
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState("modLogs");

  // Helper to check if the config has changed
  const checkForChanges = (newConfig: LogConfig) => {
    if (!initialLogConfig) return true;
    
    const keys = [
      "modLogsChannelId", "modLogsRoleId",
      "joinLeavesChannelId", "joinLeavesRoleId",
      "appealsChannelId", "appealsRoleId",
      "reportsChannelId", "reportsRoleId",
      "networkAlertsChannelId", "networkAlertsRoleId"
    ];
    
    return keys.some(key => {
      const initialValue = initialLogConfig[key as keyof LogConfig];
      const newValue = newConfig[key as keyof LogConfig];
      return initialValue !== newValue;
    });
  };

  // Update a specific log type configuration
  const updateLogConfig = (
    logType: string,
    field: "channelId" | "roleId",
    value: string
  ) => {
    const fieldName = `${logType}${field.charAt(0).toUpperCase() + field.slice(1)}` as keyof LogConfig;
    const newConfig = { ...logConfig, [fieldName]: value || null };
    setLogConfig(newConfig);
    setHasChanges(checkForChanges(newConfig));
  };

  const handleSaveLogConfig = async () => {
    if (!hasChanges) return;

    setIsSaving(true);
    try {
      const response = await fetch(`/api/hubs/${hubId}/logging`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          logConfig,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to save logging configuration");
      }

      toast({
        title: "Logging configuration saved",
        description: "Hub logging settings have been updated successfully.",
      });

      // Update initial values to reflect the new state
      setHasChanges(false);
    } catch (error) {
      console.error("Error saving logging configuration:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to save logging configuration",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Get the status of a log type (Configured or Not Configured)
  const getLogTypeStatus = (logType: string) => {
    const channelId = logConfig[`${logType}ChannelId` as keyof LogConfig];
    return channelId ? "Configured" : "Not Configured";
  };

  // Get the icon for a log type
  const getLogTypeIcon = (logType: string) => {
    switch (logType) {
      case "modLogs":
        return <Shield className="h-5 w-5 text-indigo-400" />;
      case "joinLeaves":
        return <Users className="h-5 w-5 text-green-400" />;
      case "appeals":
        return <MessageSquare className="h-5 w-5 text-blue-400" />;
      case "reports":
        return <Flag className="h-5 w-5 text-red-400" />;
      case "networkAlerts":
        return <Bell className="h-5 w-5 text-amber-400" />;
      default:
        return <Info className="h-5 w-5 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl font-bold text-white">
                    Hub Logging Configuration
                    <div className="h-1 w-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full mt-1" />
                  </CardTitle>
                  <CardDescription className="text-gray-300 mt-2">
                    Monitor hub activity with customized logging channels
                  </CardDescription>
                </div>
                <Badge variant="outline" className="bg-blue-950/30 text-blue-300 border-blue-800/50 px-3 py-1">
                  {Object.keys(logConfig).filter(key => key.includes('ChannelId') && logConfig[key as keyof LogConfig]).length} / 5 Configured
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <Alert variant="default" className="bg-blue-950/30 border-blue-800/50 mb-6">
                <Info className="h-4 w-4 text-blue-500" />
                <AlertTitle>How Logging Works</AlertTitle>
                <AlertDescription className="text-gray-400">
                  Configure Discord channels to receive notifications about different hub activities. Each log type sends specific information to help you monitor and manage your hub effectively.
                </AlertDescription>
              </Alert>

              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <div className="relative mb-8">
                  <TabsList className="flex w-full overflow-x-auto no-scrollbar space-x-2 bg-transparent p-0">
                    <TabsTrigger
                      value="modLogs"
                      className="flex-1 relative rounded-md border border-gray-800 bg-gray-900/80 px-4 py-3 transition-all
                        data-[state=active]:border-indigo-500/50 data-[state=active]:bg-indigo-950/30 data-[state=active]:text-indigo-300
                        hover:bg-gray-800/80"
                    >
                      <div className="flex flex-col items-center space-y-1">
                        <Shield className="h-5 w-5 mb-1" />
                        <span className="text-xs font-medium">Moderation</span>
                        {getLogTypeStatus("modLogs") === "Configured" && (
                          <div className="absolute top-2 right-2 h-2 w-2 rounded-full bg-green-500" />
                        )}
                      </div>
                    </TabsTrigger>
                    <TabsTrigger
                      value="joinLeaves"
                      className="flex-1 relative rounded-md border border-gray-800 bg-gray-900/80 px-4 py-3 transition-all
                        data-[state=active]:border-green-500/50 data-[state=active]:bg-green-950/30 data-[state=active]:text-green-300
                        hover:bg-gray-800/80"
                    >
                      <div className="flex flex-col items-center space-y-1">
                        <Users className="h-5 w-5 mb-1" />
                        <span className="text-xs font-medium">Join/Leave</span>
                        {getLogTypeStatus("joinLeaves") === "Configured" && (
                          <div className="absolute top-2 right-2 h-2 w-2 rounded-full bg-green-500" />
                        )}
                      </div>
                    </TabsTrigger>
                    <TabsTrigger
                      value="appeals"
                      className="flex-1 relative rounded-md border border-gray-800 bg-gray-900/80 px-4 py-3 transition-all
                        data-[state=active]:border-blue-500/50 data-[state=active]:bg-blue-950/30 data-[state=active]:text-blue-300
                        hover:bg-gray-800/80"
                    >
                      <div className="flex flex-col items-center space-y-1">
                        <MessageSquare className="h-5 w-5 mb-1" />
                        <span className="text-xs font-medium">Appeals</span>
                        {getLogTypeStatus("appeals") === "Configured" && (
                          <div className="absolute top-2 right-2 h-2 w-2 rounded-full bg-green-500" />
                        )}
                      </div>
                    </TabsTrigger>
                    <TabsTrigger
                      value="reports"
                      className="flex-1 relative rounded-md border border-gray-800 bg-gray-900/80 px-4 py-3 transition-all
                        data-[state=active]:border-red-500/50 data-[state=active]:bg-red-950/30 data-[state=active]:text-red-300
                        hover:bg-gray-800/80"
                    >
                      <div className="flex flex-col items-center space-y-1">
                        <Flag className="h-5 w-5 mb-1" />
                        <span className="text-xs font-medium">Reports</span>
                        {getLogTypeStatus("reports") === "Configured" && (
                          <div className="absolute top-2 right-2 h-2 w-2 rounded-full bg-green-500" />
                        )}
                      </div>
                    </TabsTrigger>
                    <TabsTrigger
                      value="networkAlerts"
                      className="flex-1 relative rounded-md border border-gray-800 bg-gray-900/80 px-4 py-3 transition-all
                        data-[state=active]:border-amber-500/50 data-[state=active]:bg-amber-950/30 data-[state=active]:text-amber-300
                        hover:bg-gray-800/80"
                    >
                      <div className="flex flex-col items-center space-y-1">
                        <Bell className="h-5 w-5 mb-1" />
                        <span className="text-xs font-medium">Alerts</span>
                        {getLogTypeStatus("networkAlerts") === "Configured" && (
                          <div className="absolute top-2 right-2 h-2 w-2 rounded-full bg-green-500" />
                        )}
                      </div>
                    </TabsTrigger>
                  </TabsList>
                </div>

                {["modLogs", "joinLeaves", "appeals", "reports", "networkAlerts"].map((logType) => (
                  <TabsContent key={logType} value={logType} className="space-y-4">
                    <Card className="border-gray-800/50 bg-gradient-to-b from-gray-900/50 to-gray-950/50 overflow-hidden">
                      <div className={`h-1 w-full ${
                        logType === "modLogs" ? "bg-indigo-600" :
                        logType === "joinLeaves" ? "bg-green-600" :
                        logType === "appeals" ? "bg-blue-600" :
                        logType === "reports" ? "bg-red-600" :
                        "bg-amber-600"
                      }`} />
                      <CardHeader className="pb-2">
                        <div className="flex items-center space-x-2">
                          {getLogTypeIcon(logType)}
                          <div>
                            <CardTitle className="text-lg">
                              {logType === "modLogs" ? "Moderation Logs" :
                               logType === "joinLeaves" ? "Join/Leave Logs" :
                               logType === "appeals" ? "Appeal Logs" :
                               logType === "reports" ? "Report Logs" :
                               "Network Alert Logs"}
                            </CardTitle>
                            <CardDescription>
                              {logType === "modLogs" ? "Track all moderation actions like blacklisting and warnings" :
                               logType === "joinLeaves" ? "Monitor servers joining or leaving your hub" :
                               logType === "appeals" ? "Receive notifications about blacklist appeal requests" :
                               logType === "reports" ? "Get notified when content is reported by users" :
                               "Receive important system notifications and alerts"}
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <SimpleDiscordChannelSelector
                              hubId={hubId}
                              value={logConfig[`${logType}ChannelId` as keyof LogConfig] as string || ""}
                              onChange={(value: string) => updateLogConfig(logType, "channelId", value)}
                              label="Channel"
                              placeholder="Select a Discord channel"
                              description={`The Discord channel where ${
                                logType === "modLogs" ? "moderation logs" :
                                logType === "joinLeaves" ? "join/leave notifications" :
                                logType === "appeals" ? "appeal requests" :
                                logType === "reports" ? "user reports" :
                                "system alerts"
                              } will be sent`}
                            />
                          </div>

                          <div className="space-y-2">
                            <SimpleDiscordRoleSelector
                              hubId={hubId}
                              value={logConfig[`${logType}RoleId` as keyof LogConfig] as string || ""}
                              onChange={(value: string) => updateLogConfig(logType, "roleId", value)}
                              label="Role (Optional)"
                              placeholder="Select a Discord role to mention"
                              description={`A role to mention when ${
                                logType === "modLogs" ? "moderation actions occur" :
                                logType === "joinLeaves" ? "servers join or leave" :
                                logType === "appeals" ? "appeals are received" :
                                logType === "reports" ? "content is reported" :
                                "important alerts are sent"
                              }`}
                            />
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="bg-gray-900/30 border-t border-gray-800/50 flex justify-between pt-4">
                        <div className="text-xs text-gray-500 flex items-center">
                          <AlertCircle className="h-3.5 w-3.5 mr-1 text-gray-500" />
                          Make sure the bot has permission to send messages in this channel
                        </div>
                      </CardFooter>
                    </Card>
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
            <CardFooter className="border-t border-gray-800/50 pt-4">
              <Button
                className="w-full"
                onClick={handleSaveLogConfig}
                disabled={!hasChanges || isSaving}
              >
                {isSaving ? (
                  <>Saving...</>
                ) : (
                  <>
                    {hasChanges ? (
                      <Save className="h-4 w-4 mr-2" />
                    ) : (
                      <Check className="h-4 w-4 mr-2" />
                    )}
                    {hasChanges ? "Save Changes" : "Saved"}
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>

        <div className="lg:col-span-1">
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm sticky top-6">
            <CardHeader>
              <CardTitle>Logging Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-200">Channel Selection</h3>
                <p className="text-xs text-gray-400">
                  Choose private channels for sensitive information like moderation logs and reports.
                </p>
              </div>
              <Separator className="bg-gray-800/50" />
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-200">Role Mentions</h3>
                <p className="text-xs text-gray-400">
                  Use role mentions sparingly to avoid notification fatigue. Consider creating dedicated roles for different log types.
                </p>
              </div>
              <Separator className="bg-gray-800/50" />
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-200">Bot Permissions</h3>
                <p className="text-xs text-gray-400">
                  Ensure the InterChat bot has permission to view and send messages in the selected channels.
                </p>
              </div>
              <Separator className="bg-gray-800/50" />

              <Alert variant="default" className="bg-amber-950/30 border-amber-800/50">
                <AlertTriangle className="h-4 w-4 text-amber-500" />
                <AlertTitle>Important Note</AlertTitle>
                <AlertDescription className="text-gray-400 text-xs">
                  Make sure to use private channels for logs containing sensitive information, and ensure the bot has permission to send messages in the configured channels.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

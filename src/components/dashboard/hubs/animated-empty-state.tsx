"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { motion } from "motion/react";
import { PlusCircle, MessageSquare, Shield, Users } from "lucide-react";
import Link from "next/link";
import { useInView } from "react-intersection-observer";

interface AnimatedEmptyStateProps {
  type: "owned" | "managed" | "moderated";
}

export function AnimatedEmptyState({ type }: AnimatedEmptyStateProps) {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const getContent = () => {
    switch (type) {
      case "owned":
        return {
          title: "No Owned Hubs",
          description:
            "You don't own any hubs yet. Create your first hub to get started.",
          message:
            "As a hub owner, you'll have full control over settings, members, and connections.",
          icon: <MessageSquare className="h-16 w-16 text-indigo-500/50" />,
          showButton: true,
        };
      case "managed":
        return {
          title: "No Managed Hubs",
          description: "You don't manage any hubs yet.",
          message:
            "Hub owners can assign you as a manager to help with hub administration.",
          icon: <Shield className="h-16 w-16 text-blue-500/50" />,
          showButton: false,
        };
      case "moderated":
        return {
          title: "No Moderated Hubs",
          description: "You don't moderate any hubs yet.",
          message:
            "Hub owners can assign you as a moderator to help with content moderation.",
          icon: <Users className="h-16 w-16 text-purple-500/50" />,
          showButton: false,
        };
      default:
        return {
          title: "No Hubs",
          description: "You don't have any hubs yet.",
          message: "Create your first hub to get started.",
          icon: <MessageSquare className="h-16 w-16 text-gray-500/50" />,
          showButton: true,
        };
    }
  };

  const content = getContent();

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="border-gray-800 bg-gradient-to-b from-gray-900/50 to-gray-900/30 backdrop-blur-sm">
        <CardHeader className="pb-2 flex flex-col items-center text-center">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={
              inView ? { scale: 1, opacity: 1 } : { scale: 0.8, opacity: 0 }
            }
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-4"
          >
            {content.icon}
          </motion.div>
          <CardTitle className="text-xl">{content.title}</CardTitle>
          <CardDescription>{content.description}</CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-gray-400 mb-6">{content.message}</p>
          {content.showButton && (
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={
                inView ? { scale: 1, opacity: 1 } : { scale: 0.9, opacity: 0 }
              }
              transition={{ duration: 0.5, delay: 0.4 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                asChild
                className="bg-gradient-to-r from-primary-alt to-primary hover:from-indigo-500 hover:to-primary border-none"
              >
                <Link href="/dashboard/hubs/create">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Create Hub
                </Link>
              </Button>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}

"use client";

import { ChevronRight } from "lucide-react";
import Link from "next/link";
import BannerSection from "./hub-detail/BannerSection";
import MainContent from "./hub-detail/MainContent";
import Sidebar from "./hub-detail/Sidebar";
import { SimplifiedHub } from "@/hooks/use-infinite-hubs";

export function HubDetailView({ hub }: { hub: SimplifiedHub }) {
  return (
    <div className="container max-w-7xl mx-auto">
      <div className="px-4 mb-6">
        <nav className="flex" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2">
            <li>
              <Link
                href="/hubs"
                className="text-gray-500 hover:text-primary/80 transition-colors"
              >
                Hubs
              </Link>
            </li>
            <li>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </li>
            <li className="text-gray-900 dark:text-gray-300 font-medium">
              {hub.name}
            </li>
          </ol>
        </nav>
      </div>

      <BannerSection hub={hub} />

      <div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <MainContent hub={hub} />
          <Sidebar hub={hub} />
        </div>
      </div>
    </div>
  );
}

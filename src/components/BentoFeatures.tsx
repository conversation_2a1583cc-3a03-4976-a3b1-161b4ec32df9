"use client";

import {
	<PERSON><PERSON><PERSON>,
	CheckCircle2,
	Compass,
	Globe,
	Lock,
	Search,
	Shield,
	MessageSquare,
	Users,
	Zap,
} from "lucide-react";
import { motion } from "motion/react";
import Image from "next/image";
import Link from "next/link";
import { useInView } from "react-intersection-observer";
import { <PERSON><PERSON> } from "./button";
import { GridPattern } from "./magicui/grid-pattern";

// Feature interface
interface Feature {
	title: string;
	description: string;
	icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
	benefits?: string[];
	imageSrc?: string;
	imageAlt?: string;
	learnMoreLink?: string;
	gradient: string;
	size?: "small" | "medium" | "large" | "wide" | "tall";
}

// Features data (no changes here, but ensure paths and content are correct)
const features: Feature[] = [
	{
		title: "Cross-Server Chat 🌍",
		description:
			"Link channels across Discord servers to create shared chat experiences that keep conversations flowing 24/7.",
		icon: Globe,
		benefits: [
			"Instantly connect to active communities",
			"Chat with members from other servers in real-time",
			"Messages sync across all connected servers",
		],
		imageSrc: "/features/cross-server-chat.png",
		imageAlt: "Active chat flowing between multiple connected Discord servers",
		learnMoreLink: "/docs/hub/creating-hub",
		gradient: "from-emerald-500/20 to-teal-500/30",
		size: "large",
	},

	{
		title: "Hub Discovery 🔍",
		description:
			"Find thriving communities that align with your interests using our intuitive discovery system.",
		icon: Search,
		imageSrc: "/features/HubDiscovery.png",
		imageAlt:
			"InterChat interface for discovering and joining shared community hubs",
		learnMoreLink: "/hubs",
		gradient: "from-fuchsia-500/20 to-purple-500/30",
		size: "large", // Small card with image will use adjusted image sizing
	},
	{
		title: "Private Hubs 🔒",
		description:
			"Create invite-only shared channels for your inner circle or specific partners.",
		icon: Lock,
		learnMoreLink: "/docs/hubs",
		gradient: "from-indigo-500/20 to-violet-500/30",
		size: "small",
	},
	{
		title: "Advanced Moderation 🛡️",
		description:
			"Let InterChat's AI moderation handle unwanted content so you can focus on your community.",
		icon: Shield,
		imageSrc: "/features/NSFWDetection.svg",
		imageAlt:
			"InterChat AI moderation tools acting as a bouncer for Discord servers",
		learnMoreLink: "/docs",
		gradient: "from-rose-500/20 to-pink-500/30",
		size: "wide",
	},
  {
		title: "Real-time Reactions ⚡",
		description:
			"Use emoji reactions across servers in your hub with instant synchronization.",
		icon: Zap,
		gradient: "from-amber-500/20 to-orange-500/30",
		size: "small",
	},
	{
		title: "Bring ideas to hubs 🌐",
		description:
			"Host shared tournaments, AMAs, or art collabs across multiple servers simultaneously.",
		icon: Compass,
		imageSrc: "/features/events.png",
		imageAlt:
			"Diagram showing a large-scale cross-server event managed by InterChat",
		learnMoreLink: "/hubs/search",
		gradient: "from-sky-500/20 to-blue-500/30",
		size: "large",
	},
	{
		title: "Community Growth 👥",
		description:
			"Expand your reach and grow your community by connecting with like-minded servers.",
		icon: Users,
		gradient: "from-green-500/20 to-emerald-500/30",
		size: "small",
	},
	{
		title: "Custom Welcome Messages 💬",
		description:
			"Personalize the experience for new servers joining your hub with custom welcome messages. Craft unique greetings and onboarding information to make every new connection feel special.", // Added more text to fill 'tall' card
		icon: MessageSquare,
		gradient: "from-blue-500/20 to-cyan-500/30",
		size: "small",
	},
];

// BentoCard component
const BentoCard = ({ feature, index }: { feature: Feature; index: number }) => {
	const [ref, inView] = useInView({
		triggerOnce: true,
		threshold: 0.1, // Start animation when 10% of the card is visible
	});

	// Determine size classes - responsive for mobile
	// On mobile (default grid-cols-1), col-span-1 is full width.
	// sm: applies to sm screens and up.
	const sizeClasses = {
		small: "col-span-1 row-span-1", // Mobile: 1x1. sm+: Defined by grid (e.g., 1x1 in 2-col, 1x1 in 4-col)
		medium: "col-span-1 row-span-2 sm:col-span-1 sm:row-span-2", // Mobile: 1x2. sm+: 1x2
		large: "col-span-1 row-span-2 sm:col-span-2 sm:row-span-2", // Mobile: 1x2. sm+: 2x2
		wide: "col-span-1 row-span-1 sm:col-span-2 sm:row-span-1", // Mobile: 1x1. sm+: 2x1
		tall: "col-span-1 row-span-2 sm:col-span-1 sm:row-span-2", // Mobile: 1x2. sm+: 1x2
	};

	const currentSize = feature.size || "medium"; // Default to medium
	const appliedSizeClass = sizeClasses[currentSize];

	return (
		<motion.div
			ref={ref}
			initial={{ opacity: 0, y: 30 }}
			animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
			transition={{ duration: 0.6, delay: index * 0.12, ease: "easeOut" }}
			className={`${appliedSizeClass} group relative overflow-hidden rounded-2xl border border-neutral-800/60 bg-gradient-to-br ${feature.gradient} p-6 transition-all duration-300 hover:border-neutral-700/80 hover:shadow-xl hover:-translate-y-1.5 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)]`}
		>
			{/* Background pattern */}
			<div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-center opacity-[0.07] group-hover:opacity-[0.12] transition-opacity duration-300" />

			{/* Content */}
			<div className="relative z-10 h-full flex flex-col">
				{/* Icon */}
				<div className="mb-4">
					<div
						className={`p-2.5 rounded-full bg-gradient-to-br ${feature.gradient} bg-opacity-50 w-fit shadow-md group-hover:bg-opacity-60 transition-colors duration-300`}
					>
						<feature.icon className="w-6 h-6 md:w-7 md:h-7 text-white/90" />
					</div>
				</div>

				{/* Title */}
				<h3 className="text-lg sm:text-xl md:text-2xl font-semibold mb-2.5">
					{feature.title}
				</h3>

				{/* Description */}
				<p
					className={`text-gray-300/80 text-sm md:text-base mb-4 leading-relaxed ${
						!feature.imageSrc &&
						(currentSize === "tall" || currentSize === "medium")
							? "flex-grow"
							: ""
					}`}
				>
					{feature.description}
				</p>

				{/* Benefits (if available) */}
				{feature.benefits && feature.benefits.length > 0 && (
					<ul className="space-y-1.5 mb-5 text-xs sm:text-sm">
						{feature.benefits.map((benefit) => (
							<li key={`${feature.title}-${benefit}`} className="flex items-start text-gray-400/90">
								<CheckCircle2 className="w-3.5 h-3.5 md:w-4 md:h-4 mr-2 mt-0.5 text-emerald-400 flex-shrink-0" />
								<span>{benefit}</span>
							</li>
						))}
					</ul>
				)}

				{/* Image (if available) */}
				{feature.imageSrc && (
					<div
						className={`relative overflow-hidden rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-300 my-auto ${
							// my-auto to center if space allows
							currentSize === "small"
								? "aspect-[4/3] max-h-32 sm:max-h-40"
								: currentSize === "wide"
									? "aspect-[16/9] max-h-48 sm:max-h-56"
									: "aspect-[16/9] max-h-56 sm:max-h-64" // For large, medium, tall
						} w-full`}
					>
						<Image
							src={feature.imageSrc}
							alt={feature.imageAlt || feature.title}
							fill
							className="object-cover transition-transform duration-500 group-hover:scale-[1.03]"
							sizes="(max-width: 640px) 90vw, (max-width: 1024px) 45vw, 25vw"
						/>
					</div>
				)}

				{/* Learn More Link (if available) */}
				{feature.learnMoreLink && (
					<div className="mt-auto pt-4">
						{/* mt-auto pushes it to the bottom */}
						<Link
							href={feature.learnMoreLink}
							className="group/button inline-block"
						>
							<Button
								variant="link"
								className="p-0 text-sky-400 hover:text-sky-300 flex items-center gap-1.5 text-sm font-medium"
							>
								Learn more
								<ArrowRight className="w-4 h-4 transition-transform duration-300 group-hover/button:translate-x-1" />
							</Button>
						</Link>
					</div>
				)}
			</div>
		</motion.div>
	);
};

// Main BentoFeatures component
export function BentoFeatures() {
	return (
		<section
			className="relative overflow-hidden pt-20 pb-28 md:pt-28 md:pb-36"
			id="features"
		>
			<GridPattern
				width={70}
				height={70}
				x={-1}
				y={-1}
				className="stroke-primary/10 fill-transparent opacity-50 z-0"
				strokeDasharray="5 5"
			/>

      {/* Simplified gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b to-transparent from-gray-950" />

			{/* Content container */}
			<div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
				{/* Section header */}
				<div className="text-center mb-16 md:mb-20">
					<motion.h2
						initial={{ opacity: 0, y: -20 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, amount: 0.5 }}
						transition={{ duration: 0.5, ease: "easeOut" }}
						className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white via-primary-alt/90 to-white tracking-tight"
					>
						Features that{" "}
						<span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-400 to-orange-400">
							Connect
						</span>{" "}
						Communities
					</motion.h2>

					<motion.p
						initial={{ opacity: 0, y: 20 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, amount: 0.5 }}
						transition={{ duration: 0.5, delay: 0.15, ease: "easeOut" }}
						className="max-w-3xl mx-auto text-lg md:text-xl text-gray-300/80 leading-relaxed relative z-10 px-6 py-5 rounded-xl bg-gray-800/50 border border-white/10 shadow-lg"
					>
						InterChat provides the tools to create shared chat experiences and
						keep your members engaged across different servers.
					</motion.p>
				</div>

				{/* Bento grid */}
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 md:gap-6 lg:gap-8">
					{features.map((feature, index) => (
						<BentoCard key={feature.title} feature={feature} index={index} />
					))}
				</div>

				{/* CTA */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					viewport={{ once: true, amount: 0.5 }}
					transition={{ duration: 0.5, delay: 0.3, ease: "easeOut" }}
					className="mt-20 md:mt-24 text-center"
				>
					<Link href="/docs">
						<Button
							size="lg"
							className="bg-gradient-to-r from-primary to-primary-alt hover:opacity-90 text-white text-base font-semibold px-8 py-3.5 rounded-lg shadow-lg hover:shadow-primary/40 transition-all duration-300"
						>
							Explore All Features
							<ArrowRight className="ml-2.5 w-5 h-5" />
						</Button>
					</Link>
				</motion.div>
			</div>
		</section>
	);
}

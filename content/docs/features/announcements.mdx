---
title: Announcements
description: Send important messages to all members of your hub
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Steps } from 'fumadocs-ui/components/steps'

# Hub Announcements

The announcements feature allows hub owners and moderators to send important messages to all channels connected to a hub simultaneously.

## Understanding Announcements

Hub announcements are special messages that:

- Are sent to all connected channels in a hub
- Stand out visually from regular messages
- Can include formatted text and images
- Are identified as official hub communications

<Callout type="info">
  Announcements are perfect for important updates, rule changes, events, or any information that all hub members should see.
</Callout>

## Creating Announcements

<Steps>
  ### Use the announce command
  
  Type `/hub announce hub:YourHubName` in any channel.
  
  ### Write your announcement
  
  A modal will appear where you can type your announcement message.
  
  Announcements support:
  - Markdown formatting
  - Discord emoji
  - Up to 4000 characters
  
  ### Send the announcement
  
  After writing your message, click "Submit" to send the announcement to all connected channels.
  
  <Callout type="warning">
    There is a 1-minute cooldown between announcements to prevent spam.
  </Callout>
</Steps>

## Announcement Appearance

When an announcement is sent, it appears in all connected channels with:

- A distinctive formatting to stand out from regular messages
- The hub name and icon
- The full announcement content

## Best Practices for Announcements

### When to Use Announcements

Announcements are best used for:

- Important rule changes
- Upcoming events or activities
- Significant hub updates
- Addressing widespread issues
- Welcoming new servers to the hub

### Writing Effective Announcements

For maximum impact:

1. **Be concise**: Keep announcements clear and to the point
2. **Use formatting**: Utilize headers, bold text, and lists for readability
3. **Prioritize information**: Put the most important details first
4. **Include next steps**: If action is required, clearly state what users should do
5. **Consider timing**: Send announcements when most users are likely to be active

<Callout type="tip">
  For regular communication, use normal messages. Reserve announcements for truly important information to avoid announcement fatigue.
</Callout>

## Permissions

The following roles can send hub announcements:

- Hub Owner
- Hub Manager
- Hub Moderator

Server administrators cannot send hub-wide announcements unless they also have one of the hub roles listed above.

## Announcement Limitations

To prevent abuse and ensure announcements remain valuable:

- There is a 1-minute cooldown between announcements
- Announcements are limited to 4000 characters
- Images and attachments must follow the hub's content rules
- Excessive or inappropriate use of announcements may result in moderation action

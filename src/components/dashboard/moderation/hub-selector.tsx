"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";

interface Hub {
  id: string;
  name: string;
}

interface HubSelectorProps {
  hubs: Hub[];
  selectedHubId: string;
}

export function HubSelector({ hubs, selectedHubId }: HubSelectorProps) {
  const router = useRouter();

  const handleValueChange = (value: string) => {
    const url = new URL(window.location.href);
    if (value === "all") {
      url.searchParams.delete("hubId");
    } else {
      url.searchParams.set("hubId", value);
    }

    // Preserve the page parameter if it exists
    const page = url.searchParams.get("page");
    if (page && page !== "1") {
      url.searchParams.set("page", "1"); // Reset to page 1 when changing hubs
    }

    router.push(url.pathname + url.search);
  };

  return (
    <Select defaultValue={selectedHubId} onValueChange={handleValueChange}>
      <SelectTrigger className="w-full bg-gray-800/50 border-gray-700/50 focus:ring-indigo-500/50">
        <SelectValue placeholder="Select Hub">
          {selectedHubId === "all"
            ? "All Hubs"
            : hubs.find((h) => h.id === selectedHubId)?.name || "Select Hub"}
        </SelectValue>
      </SelectTrigger>
      <SelectContent className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50">
        <SelectItem value="all">All Hubs</SelectItem>
        {hubs.map((hub) => (
          <SelectItem key={hub.id} value={hub.id}>
            {hub.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

/**
 * API utility functions for data fetching with Tanstack Query
 */

// Import the types from the client component
import { BasicHubConnection, ServerData } from "@/app/dashboard/hubs/[hubId]/connections/client";

// Types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

// Generic fetch function with error handling
export async function fetchApi<T>(
  url: string,
  options?: RequestInit
): Promise<T> {
  const response = await fetch(url, options);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.error || `API error: ${response.status} ${response.statusText}`
    );
  }

  return response.json();
}

// Hub connections API functions
// Using ServerData from client component

export interface ConnectionData {
  id: string;
  hubId: string;
  serverId: string;
  channelId: string;
  connected: boolean;
  compact: boolean;
  createdAt: string | Date;
  lastActive: string | Date;
  joinRequestsDisabled: boolean;
  server: ServerData | null;
}

export interface ConnectionsResponse {
  connections: ConnectionData[];
}

export async function getHubConnections(hubId: string): Promise<(BasicHubConnection & { server: ServerData | null })[]> {
  const data = await fetchApi<ConnectionsResponse>(`/api/hubs/${hubId}/connections`);

  // Transform the connections to ensure they have all required fields
  return data.connections.map((conn) => ({
    ...conn,
    // Ensure dates are properly formatted
    createdAt: conn.createdAt instanceof Date ? conn.createdAt : new Date(conn.createdAt),
    lastActive: conn.lastActive instanceof Date ? conn.lastActive : new Date(conn.lastActive),
    // Ensure server has all required fields
    server: conn.server
      ? {
          ...conn.server,
          createdAt: new Date(),
          premiumStatus: false,
          updatedAt: new Date(),
          inviteCode: null,
          messageCount: 0,
          lastMessageAt: new Date(),
        }
      : null,
  }));
}

export async function removeConnection(connectionId: string): Promise<void> {
  await fetchApi(`/api/dashboard/connections/${connectionId}`, {
    method: "DELETE",
  });
}

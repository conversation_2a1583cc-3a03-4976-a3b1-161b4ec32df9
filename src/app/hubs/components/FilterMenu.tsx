"use client";

import { useState } from "react";
import { Filter, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuPortal,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { ContentFilter, VerificationStatus, LANGUAGES, REGIONS } from "@/app/hubs/constants";

interface FilterMenuProps {
  contentFilter: ContentFilter;
  verificationStatus: VerificationStatus;
  language?: string;
  region?: string;
  minServers?: number;
  maxServers?: number;
  onFilterChange: (
    filterType: string,
    value: ContentFilter | VerificationStatus | string | number | undefined
  ) => void;
  onClearFilters: () => void;
}

export function FilterMenu({
  contentFilter,
  verificationStatus,
  language,
  region,
  minServers,
  maxServers,
  onFilterChange,
  onClearFilters,
}: FilterMenuProps) {
  const [serverRange, setServerRange] = useState<[number, number]>([
    minServers || 0,
    maxServers || 300,
  ]);

  // Count active filters
  const activeFilterCount = [
    contentFilter !== ContentFilter.All ? 1 : 0,
    verificationStatus !== VerificationStatus.All ? 1 : 0,
    language && language !== "all" ? 1 : 0,
    region && region !== "all" ? 1 : 0,
    minServers !== undefined || maxServers !== undefined ? 1 : 0,
  ].reduce((a, b) => a + b, 0);

  // Handle server range change
  const handleServerRangeChange = (values: number[]) => {
    setServerRange([values[0], values[1]]);
  };

  // Apply server range filter
  const applyServerRange = () => {
    onFilterChange("minServers", serverRange[0]);
    onFilterChange("maxServers", serverRange[1]);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="h-12 border border-gray-700/50 bg-gray-800/50 text-white hover:bg-gray-700/50 hover:border-gray-600 shadow-lg shadow-primary/5 transition-all duration-200"
        >
          <div className="relative mr-2">
            <Filter className="w-5 h-5 relative z-10" />
            {activeFilterCount > 0 && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full animate-pulse" />
            )}
          </div>
          Filters
          {activeFilterCount > 0 && (
            <Badge className="ml-2 bg-primary text-white">
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64 bg-gray-900 border-gray-700 shadow-xl shadow-black/20" sideOffset={5}>
        <DropdownMenuLabel className="text-sm font-medium">Filter Options</DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-gray-700" />

        {/* Content Filter */}
        <DropdownMenuGroup>
          <DropdownMenuLabel className="text-xs text-gray-400 font-normal">
            Content Type
          </DropdownMenuLabel>
          <DropdownMenuRadioGroup
            value={contentFilter}
            onValueChange={(value) =>
              onFilterChange("contentFilter", value as ContentFilter)
            }
          >
            <DropdownMenuRadioItem
              value={ContentFilter.All}
              className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800"
            >
              All Content
            </DropdownMenuRadioItem>
            <DropdownMenuRadioItem
              value={ContentFilter.SFW}
              className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800"
            >
              SFW Only
            </DropdownMenuRadioItem>
            <DropdownMenuRadioItem
              value={ContentFilter.NSFW}
              className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800"
            >
              NSFW Only
            </DropdownMenuRadioItem>
          </DropdownMenuRadioGroup>
        </DropdownMenuGroup>

        <DropdownMenuSeparator className="bg-gray-700" />

        {/* Verification Status */}
        <DropdownMenuGroup>
          <DropdownMenuLabel className="text-xs text-gray-400 font-normal">
            Verification Status
          </DropdownMenuLabel>
          <DropdownMenuRadioGroup
            value={verificationStatus}
            onValueChange={(value) =>
              onFilterChange("verificationStatus", value as VerificationStatus)
            }
          >
            <DropdownMenuRadioItem
              value={VerificationStatus.All}
              className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800"
            >
              All Hubs
            </DropdownMenuRadioItem>
            <DropdownMenuRadioItem
              value={VerificationStatus.Verified}
              className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800"
            >
              Verified Only
            </DropdownMenuRadioItem>
            <DropdownMenuRadioItem
              value={VerificationStatus.Partnered}
              className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800"
            >
              Partnered Only
            </DropdownMenuRadioItem>
            <DropdownMenuRadioItem
              value={VerificationStatus.VerifiedOrPartnered}
              className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800"
            >
              Verified or Partnered
            </DropdownMenuRadioItem>
          </DropdownMenuRadioGroup>
        </DropdownMenuGroup>

        <DropdownMenuSeparator className="bg-gray-700" />

        {/* Language Submenu */}
        <DropdownMenuSub>
          <DropdownMenuSubTrigger className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
            <span>Language</span>
            {language && language !== "all" && (
              <Badge className="ml-2 bg-primary/20 text-primary">
                {LANGUAGES.find((l) => l.code === language)?.name || language}
              </Badge>
            )}
          </DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent className="bg-gray-900 border-gray-700 max-h-[300px] overflow-y-auto" sideOffset={8}>
              <DropdownMenuRadioGroup
                value={language || "all"}
                onValueChange={(value) => onFilterChange("language", value)}
              >
                <DropdownMenuRadioItem value="all" className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
                  All Languages
                </DropdownMenuRadioItem>
                {LANGUAGES.map((lang) => (
                  <DropdownMenuRadioItem
                    key={lang.code}
                    value={lang.code}
                    className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800"
                  >
                    {lang.name}
                  </DropdownMenuRadioItem>
                ))}
              </DropdownMenuRadioGroup>
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>

        {/* Region Submenu */}
        <DropdownMenuSub>
          <DropdownMenuSubTrigger className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
            <span>Region</span>
            {region && region !== "all" && (
              <Badge className="ml-2 bg-primary/20 text-primary">
                {REGIONS.find((r) => r.code === region)?.name || region}
              </Badge>
            )}
          </DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent className="bg-gray-900 border-gray-700" sideOffset={8}>
              <DropdownMenuRadioGroup
                value={region || "all"}
                onValueChange={(value) => onFilterChange("region", value)}
              >
                <DropdownMenuRadioItem value="all" className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
                  All Regions
                </DropdownMenuRadioItem>
                {REGIONS.map((reg) => (
                  <DropdownMenuRadioItem
                    key={reg.code}
                    value={reg.code}
                    className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800"
                  >
                    {reg.name}
                  </DropdownMenuRadioItem>
                ))}
              </DropdownMenuRadioGroup>
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>

        {/* Server Count Range */}
        <DropdownMenuSeparator className="bg-gray-700" />
        <DropdownMenuLabel className="text-xs text-gray-400 font-normal">
          Server Count
        </DropdownMenuLabel>

        <div className="px-4 py-3">
          <div className="flex justify-between items-center mb-2">
            <span className="text-xs text-gray-300">
              {serverRange[0]} - {serverRange[1] === 300 ? "300+" : serverRange[1]}
            </span>
            <Button
              variant="default"
              size="sm"
              className="h-6 px-3 text-xs bg-primary text-white hover:bg-primary/90"
              onClick={applyServerRange}
            >
              Apply
            </Button>
          </div>
          <Slider
            defaultValue={[serverRange[0], serverRange[1]]}
            value={[serverRange[0], serverRange[1]]}
            max={300}
            step={10}
            onValueChange={handleServerRangeChange}
            className="my-2"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>0</span>
            <span>150</span>
            <span>300</span>
          </div>
        </div>

        <div className="px-4 pb-3 grid grid-cols-3 gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="h-auto py-1 text-xs hover:bg-gray-800 hover:text-white"
            onClick={() => {
              setServerRange([0, 50]);
              onFilterChange("minServers", 0);
              onFilterChange("maxServers", 50);
            }}
          >
            Small<br/>(0-50)
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-auto py-1 text-xs hover:bg-gray-800 hover:text-white"
            onClick={() => {
              setServerRange([50, 150]);
              onFilterChange("minServers", 50);
              onFilterChange("maxServers", 150);
            }}
          >
            Medium<br/>(50-150)
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-auto py-1 text-xs hover:bg-gray-800 hover:text-white"
            onClick={() => {
              setServerRange([150, 300]);
              onFilterChange("minServers", 150);
              onFilterChange("maxServers", 300);
            }}
          >
            Large<br/>(150+)
          </Button>
        </div>

        <DropdownMenuSeparator className="bg-gray-700" />

        {/* Clear Filters */}
        {activeFilterCount > 0 && (
          <div className="p-2">
            <Button
              variant="ghost"
              size="sm"
              className="w-full text-gray-400 hover:text-white hover:bg-gray-800 cursor-pointer flex items-center justify-center"
              onClick={onClearFilters}
            >
              <X className="h-4 w-4 mr-2" />
              Clear all filters
            </Button>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

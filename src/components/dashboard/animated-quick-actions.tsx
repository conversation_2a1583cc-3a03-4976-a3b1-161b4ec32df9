"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { motion } from "motion/react";
import {
  ArrowRight,
  HomeIcon,
  MessageSquare,
  Plus,
  Shield,
  Zap,
} from "lucide-react";
import Link from "next/link";
import { useInView } from "react-intersection-observer";

export function AnimatedQuickActions() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, x: 20 },
    show: { opacity: 1, x: 0, transition: { duration: 0.5 } },
  };

  const actions = [
    {
      title: "Create Hub",
      description: "Start a new community",
      icon: Plus,
      href: "/dashboard/hubs/create",
      color: "bg-purple-500/10 text-purple-400 border-purple-500/20",
    },
    {
      title: "Manage Servers",
      description: "Configure Discord servers",
      icon: HomeIcon,
      href: "/dashboard/servers",
      color: "bg-blue-500/10 text-blue-400 border-blue-500/20",
    },
    {
      title: "View Hubs",
      description: "Manage communities",
      icon: MessageSquare,
      href: "/dashboard/hubs",
      color: "bg-indigo-500/10 text-indigo-400 border-indigo-500/20",
    },
    {
      title: "Moderation",
      description: "Keep hubs safe",
      icon: Shield,
      href: "/dashboard/moderation/reports",
      color: "bg-pink-500/10 text-pink-400 border-pink-500/20",
    },
  ];

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={inView ? "show" : "hidden"}
      variants={container}
      className="h-full"
    >
      <Card className="border-gray-800 bg-gradient-to-b from-gray-900/50 to-gray-900/30 backdrop-blur-sm h-full">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-yellow-400" />
            <CardTitle className="text-xl font-bold">Quick Actions</CardTitle>
          </div>
          <CardDescription>Common dashboard tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {actions.map((action) => (
              <motion.div key={action.title} variants={item}>
                <Link href={action.href}>
                  <div className="flex items-center justify-between p-4 rounded-md bg-gray-800/30 hover:bg-gray-800/50 cursor-pointer transition-all duration-200 border border-gray-800/50 hover:border-gray-700 group">
                    <div className="flex items-center gap-3">
                      <div
                        className={`h-10 w-10 rounded-full ${action.color} flex items-center justify-center`}
                      >
                        <action.icon className="h-5 w-5" />
                      </div>
                      <div>
                        <p className="text-base font-medium">{action.title}</p>
                        <p className="text-sm text-gray-400">
                          {action.description}
                        </p>
                      </div>
                    </div>
                    <motion.div
                      initial={{ x: 0 }}
                      whileHover={{ x: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-white transition-colors duration-200" />
                    </motion.div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

"use client";

import { useState, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Search,
  X,
  Globe,
  ArrowDownAZ,
  Flame,
  Clock,
  ThumbsUp,
  MessageCirclePlus,
  PlusCircle,
  Activity,
  Tag as TagIcon,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { SortOptions, ContentFilter, VerificationStatus, POPULAR_TAGS } from "../constants";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { EnhancedFilterMenu } from "./EnhancedFilterMenu";
import { HubSearchDropdown } from "./hub-search-dropdown";

const sortOptions = [
  {
    label: "Trending",
    value: SortOptions.Trending,
    icon: <Flame className="w-4 h-4 mr-2" />,
  },
  {
    label: "Most Active",
    value: SortOptions.MostActive,
    icon: <Activity className="w-4 h-4 mr-2" />,
  },
  {
    label: "Most Recent",
    value: SortOptions.MostRecentPopular,
    icon: <Clock className="w-4 h-4 mr-2" />,
  },
  {
    label: "Most Upvoted",
    value: SortOptions.MostUpvotedNew,
    icon: <ThumbsUp className="w-4 h-4 mr-2" />,
  },
  {
    label: "Alphabetical",
    value: SortOptions.Name,
    icon: <ArrowDownAZ className="w-4 h-4 mr-2" />,
  },
  {
    label: "Recently Active",
    value: SortOptions.LastActive,
    icon: <MessageCirclePlus className="w-4 h-4 mr-2" />,
  },
  {
    label: "Most Connections",
    value: SortOptions.Servers,
    icon: <Globe className="w-4 h-4 mr-2" />,
  },
  {
    label: "Newest First",
    value: SortOptions.Created,
    icon: <PlusCircle className="w-4 h-4 mr-2" />,
  },
];

export function SearchHeader() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState(
    searchParams.get("search") || "",
  );
  const [selectedTags, setSelectedTags] = useState<string[]>(
    searchParams.get("tags")?.split(",").filter(Boolean) || [],
  );
  const currentSort = searchParams.get("sort") || SortOptions.Trending;

  // New filter states
  const [contentFilter, setContentFilter] = useState<ContentFilter>(
    (searchParams.get("contentFilter") as ContentFilter) || ContentFilter.All
  );
  const [verificationStatus, setVerificationStatus] = useState<VerificationStatus>(
    (searchParams.get("verificationStatus") as VerificationStatus) || VerificationStatus.All
  );
  const [language, setLanguage] = useState<string | undefined>(
    searchParams.get("language") || undefined
  );
  const [region, setRegion] = useState<string | undefined>(
    searchParams.get("region") || undefined
  );
  const [minServers, setMinServers] = useState<number | undefined>(
    searchParams.get("minServers") ? Number(searchParams.get("minServers")) : undefined
  );
  const [maxServers, setMaxServers] = useState<number | undefined>(
    searchParams.get("maxServers") ? Number(searchParams.get("maxServers")) : undefined
  );

  const getSortLabel = (value: string) => {
    return (
      sortOptions.find((option) => option.value === value)?.label || "Trending"
    );
  };

  const getSortIcon = (value: string) => {
    return (
      sortOptions.find((option) => option.value === value)?.icon || (
        <Flame className="w-4 h-4 mr-2" />
      )
    );
  };

  const handleSearch = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();

      const params = new URLSearchParams(searchParams.toString());

      if (searchTerm) {
        params.set("search", searchTerm);
      } else {
        params.delete("search");
      }

      if (selectedTags.length > 0) {
        params.set("tags", selectedTags.join(","));
      } else {
        params.delete("tags");
      }

      // Add new filter parameters
      if (contentFilter !== ContentFilter.All) {
        params.set("contentFilter", contentFilter);
      } else {
        params.delete("contentFilter");
      }

      if (verificationStatus !== VerificationStatus.All) {
        params.set("verificationStatus", verificationStatus);
      } else {
        params.delete("verificationStatus");
      }

      if (language) {
        params.set("language", language);
      } else {
        params.delete("language");
      }

      if (region) {
        params.set("region", region);
      } else {
        params.delete("region");
      }

      if (minServers !== undefined) {
        params.set("minServers", minServers.toString());
      } else {
        params.delete("minServers");
      }

      if (maxServers !== undefined) {
        params.set("maxServers", maxServers.toString());
      } else {
        params.delete("maxServers");
      }

      router.push(`/hubs/search?${params.toString()}`);
    },
    [
      searchTerm,
      selectedTags,
      contentFilter,
      verificationStatus,
      language,
      region,
      minServers,
      maxServers,
      searchParams,
      router
    ],
  );

  const handleSortChange = useCallback(
    (sort: string) => {
      const params = new URLSearchParams(searchParams.toString());

      if (sort && sort !== SortOptions.Trending) {
        params.set("sort", sort);
      } else {
        params.delete("sort");
      }

      router.push(`/hubs/search?${params.toString()}`);
    },
    [searchParams, router],
  );

  const toggleTag = useCallback((tag: string) => {
    setSelectedTags((prev) =>
      prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag],
    );
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback((
    filterType: string,
    value: ContentFilter | VerificationStatus | string | number | undefined
  ) => {
    switch (filterType) {
      case "contentFilter":
        setContentFilter(value as ContentFilter);
        break;
      case "verificationStatus":
        setVerificationStatus(value as VerificationStatus);
        break;
      case "language":
        setLanguage(value === "all" ? undefined : value as string);
        break;
      case "region":
        setRegion(value === "all" ? undefined : value as string);
        break;
      case "minServers":
        setMinServers(value as number);
        break;
      case "maxServers":
        setMaxServers(value as number);
        break;
      default:
        break;
    }
  }, []);

  const clearFilters = useCallback(() => {
    setSearchTerm("");
    setSelectedTags([]);
    setContentFilter(ContentFilter.All);
    setVerificationStatus(VerificationStatus.All);
    setLanguage(undefined);
    setRegion(undefined);
    setMinServers(undefined);
    setMaxServers(undefined);
    router.push("/hubs/search");
  }, [router]);

  return (
    <div className="w-full border-b border-gray-800/30 backdrop-blur-sm bg-gradient-to-b from-gray-900/80 to-gray-900/40">
      <div className="container mx-auto px-4 py-10">
        {/* Header with animated elements */}
        <div className="flex flex-col items-center justify-center mb-8">
          <div className="flex items-center justify-center mb-4 relative">
            <div className="absolute -inset-4 bg-gradient-to-r from-primary/10 via-primary/5 to-primary-alt/10 blur-xl rounded-full animate-pulse"
                 style={{ animationDuration: "4s" }} />
            <div className="relative flex items-center">
              <div className="relative mr-4">
                <div className="absolute inset-0 rounded-full bg-primary/30 blur-xl animate-pulse"
                     style={{ animationDuration: "3s" }} />
                <Globe className="w-12 h-12 text-primary relative" />
              </div>
              <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white via-purple-300 to-primary">
                Discover Hubs
              </h2>
            </div>
          </div>
          <p className="text-gray-300 text-center max-w-2xl text-lg">
            Find and join active Discord hubs with InterChat Hubs.
            <span className="hidden md:inline"> Connect your server to these thriving cross-server chatrooms.</span>
          </p>
        </div>

        {/* Enhanced search form with dropdown */}
        <div className="relative w-full max-w-4xl mx-auto">
          <form onSubmit={handleSearch} className="w-full">
            <div className="flex flex-col md:flex-row gap-3">
              {/* Search input with dropdown */}
              <div className="relative flex-grow">
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 pointer-events-none">
                  <div className="absolute inset-0 bg-primary/20 rounded-full blur-md animate-pulse"
                       style={{ animationDuration: "2s" }} />
                  <Search className="relative w-5 h-5 text-white" />
                </div>
                <Input
                  type="text"
                  placeholder="Search for gaming, art, anime hubs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 h-14 bg-gray-800/50 border border-gray-700/50 hover:border-gray-600 focus:border-primary focus:ring-2 focus:ring-primary/30 text-white rounded-xl shadow-lg shadow-primary/5 text-base"
                />
                {searchTerm && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => setSearchTerm("")}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white cursor-pointer"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}

                {/* Search dropdown */}
                {searchTerm.trim() && (
                  <div className="absolute left-0 right-0 top-full mt-1 z-[100]" style={{ zIndex: 9999 }}>
                    <HubSearchDropdown
                      searchTerm={searchTerm}
                      onTagSelect={(tag) => {
                        toggleTag(tag);
                        setSearchTerm("");
                      }}
                      onSearch={(term) => {
                        setSearchTerm(term);
                        // Create a synthetic event that mimics a form submission
                        const syntheticEvent = { preventDefault: () => {} };
                        handleSearch(syntheticEvent as React.FormEvent);
                      }}
                    />
                  </div>
                )}
              </div>

              {/* Filter controls */}
              <div className="flex gap-2 flex-wrap md:flex-nowrap">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className="h-14 border border-gray-700/50 bg-gray-800/50 text-white hover:bg-gray-700/50 hover:border-gray-600 shadow-lg shadow-primary/5 transition-all duration-200 rounded-xl"
                    >
                      <div className="relative mr-2">
                        <TagIcon className="w-5 h-5 relative z-10" />
                        {selectedTags.length > 0 && (
                          <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full animate-pulse" />
                        )}
                      </div>
                      <span className="hidden sm:inline">Tags</span>
                      {selectedTags.length > 0 && (
                        <Badge className="ml-1 bg-primary text-white">
                          {selectedTags.length}
                        </Badge>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-64 bg-gray-900 border-gray-700 shadow-xl shadow-black/20 z-[9999]" style={{ zIndex: 9999 }}>
                    <DropdownMenuLabel>Popular Tags</DropdownMenuLabel>
                    <DropdownMenuSeparator className="bg-gray-700" />
                    <div className="p-3 max-h-[300px] overflow-y-auto">
                      <div className="flex flex-wrap gap-2">
                        {POPULAR_TAGS.map((tag) => (
                          <Badge
                            key={tag}
                            className={cn(
                              "cursor-pointer hover:bg-gray-700 transition-colors py-1.5 px-3",
                              selectedTags.includes(tag)
                                ? "bg-primary text-white"
                                : "bg-gray-800 text-gray-300",
                            )}
                            onClick={() => toggleTag(tag)}
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    {selectedTags.length > 0 && (
                      <>
                        <DropdownMenuSeparator className="bg-gray-700" />
                        <div className="p-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="w-full text-gray-400 hover:text-white cursor-pointer"
                            onClick={() => setSelectedTags([])}
                          >
                            Clear tags
                          </Button>
                        </div>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>

                <EnhancedFilterMenu
                  contentFilter={contentFilter}
                  verificationStatus={verificationStatus}
                  language={language}
                  region={region}
                  minServers={minServers}
                  maxServers={maxServers}
                  onFilterChange={handleFilterChange}
                  onClearFilters={() => {
                    setContentFilter(ContentFilter.All);
                    setVerificationStatus(VerificationStatus.All);
                    setLanguage(undefined);
                    setRegion(undefined);
                    setMinServers(undefined);
                    setMaxServers(undefined);
                  }}
                />

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className="h-14 border border-gray-700/50 bg-gray-800/50 text-white hover:bg-gray-700/50 hover:border-gray-600 shadow-lg shadow-primary/5 transition-all duration-200 rounded-xl"
                    >
                      {getSortIcon(currentSort as string)}
                      <span className="hidden sm:inline">{getSortLabel(currentSort as string)}</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-gray-900 border-gray-700 shadow-xl shadow-black/20 z-[9999]" style={{ zIndex: 9999 }}>
                    {sortOptions.map((option) => (
                      <DropdownMenuItem
                        key={option.value}
                        className={cn(
                          "cursor-pointer flex items-center",
                          currentSort === option.value
                            ? "bg-primary/20 text-primary"
                            : "text-gray-300",
                        )}
                        onClick={() => handleSortChange(option.value)}
                      >
                        {option.icon}
                        {option.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button
                  type="submit"
                  className="h-14 bg-gradient-to-r from-primary to-primary-alt hover:from-primary/90 hover:to-primary-alt/90 text-white font-medium shadow-lg shadow-primary/10 transition-all duration-200 cursor-pointer rounded-xl"
                >
                  <Search className="w-5 h-5 md:mr-2" />
                  <span className="hidden md:inline">Search</span>
                </Button>
              </div>
            </div>

            {/* Active filters display */}
            {(searchTerm ||
              selectedTags.length > 0 ||
              contentFilter !== ContentFilter.All ||
              verificationStatus !== VerificationStatus.All ||
              language ||
              region ||
              minServers !== undefined ||
              maxServers !== undefined ||
              currentSort !== SortOptions.Trending) && (
              <div className="flex flex-wrap items-center gap-2 mt-6 bg-gray-800/30 p-4 rounded-xl border border-gray-700/30 backdrop-blur-sm shadow-lg">
                <span className="text-sm text-gray-300 font-medium mr-2">
                  Active filters:
                </span>

                <div className="flex flex-wrap gap-2 flex-grow">
                  {/* Search term filter */}
                  {searchTerm && (
                    <Badge className="bg-primary/20 text-primary border border-primary/20 flex items-center gap-1 shadow-sm shadow-primary/5 px-3 py-1.5">
                      <Search className="w-3 h-3 mr-1" />
                      {searchTerm}
                      <button
                        type="button"
                        onClick={() => setSearchTerm("")}
                        className="ml-1"
                      >
                        <X className="w-3 h-3 hover:text-white transition-colors" />
                      </button>
                    </Badge>
                  )}

                  {/* Tags filters */}
                  {selectedTags.map((tag) => (
                    <Badge
                      key={tag}
                      className="bg-primary/20 text-primary border border-primary/20 flex items-center gap-1 shadow-sm shadow-primary/5 px-3 py-1.5"
                    >
                      <TagIcon className="w-3 h-3 mr-1" />
                      {tag}
                      <button
                        type="button"
                        onClick={() => toggleTag(tag)}
                        className="ml-1"
                      >
                        <X className="w-3 h-3 hover:text-white transition-colors" />
                      </button>
                    </Badge>
                  ))}

                  {/* Content filter */}
                  {contentFilter !== ContentFilter.All && (
                    <Badge className="bg-primary/20 text-primary border border-primary/20 flex items-center gap-1 shadow-sm shadow-primary/5 px-3 py-1.5">
                      Content: {contentFilter === ContentFilter.SFW ? "SFW" : "NSFW"}
                      <button
                        type="button"
                        onClick={() => setContentFilter(ContentFilter.All)}
                        className="ml-1"
                      >
                        <X className="w-3 h-3 hover:text-white transition-colors" />
                      </button>
                    </Badge>
                  )}

                  {/* Verification status filter */}
                  {verificationStatus !== VerificationStatus.All && (
                    <Badge className="bg-primary/20 text-primary border border-primary/20 flex items-center gap-1 shadow-sm shadow-primary/5 px-3 py-1.5">
                      {verificationStatus === VerificationStatus.Verified
                        ? "Verified"
                        : verificationStatus === VerificationStatus.Partnered
                          ? "Partnered"
                          : "Verified/Partnered"}
                      <button
                        type="button"
                        onClick={() => setVerificationStatus(VerificationStatus.All)}
                        className="ml-1"
                      >
                        <X className="w-3 h-3 hover:text-white transition-colors" />
                      </button>
                    </Badge>
                  )}

                  {/* Language filter */}
                  {language && (
                    <Badge className="bg-primary/20 text-primary border border-primary/20 flex items-center gap-1 shadow-sm shadow-primary/5 px-3 py-1.5">
                      Language: {language}
                      <button
                        type="button"
                        onClick={() => setLanguage(undefined)}
                        className="ml-1"
                      >
                        <X className="w-3 h-3 hover:text-white transition-colors" />
                      </button>
                    </Badge>
                  )}

                  {/* Region filter */}
                  {region && (
                    <Badge className="bg-primary/20 text-primary border border-primary/20 flex items-center gap-1 shadow-sm shadow-primary/5 px-3 py-1.5">
                      Region: {region}
                      <button
                        type="button"
                        onClick={() => setRegion(undefined)}
                        className="ml-1"
                      >
                        <X className="w-3 h-3 hover:text-white transition-colors" />
                      </button>
                    </Badge>
                  )}

                  {/* Server count filter */}
                  {(minServers !== undefined || maxServers !== undefined) && (
                    <Badge className="bg-primary/20 text-primary border border-primary/20 flex items-center gap-1 shadow-sm shadow-primary/5 px-3 py-1.5">
                      Members: {minServers || 0} - {maxServers === 10000 ? "∞" : maxServers}
                      <button
                        type="button"
                        onClick={() => {
                          setMinServers(undefined);
                          setMaxServers(undefined);
                        }}
                        className="ml-1"
                      >
                        <X className="w-3 h-3 hover:text-white transition-colors" />
                      </button>
                    </Badge>
                  )}

                  {/* Sort filter */}
                  {currentSort !== SortOptions.Trending && (
                    <Badge className="bg-primary/20 text-primary border border-primary/20 flex items-center gap-1 shadow-sm shadow-primary/5 px-3 py-1.5">
                      {getSortIcon(currentSort as string)}
                      {getSortLabel(currentSort as string)}
                      <button
                        type="button"
                        onClick={() => handleSortChange(SortOptions.Trending)}
                        className="ml-1"
                      >
                        <X className="w-3 h-3 hover:text-white transition-colors" />
                      </button>
                    </Badge>
                  )}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-gray-300 hover:text-white text-xs border-gray-700/50 hover:bg-gray-700/50 cursor-pointer ml-auto"
                  onClick={clearFilters}
                >
                  Clear all
                </Button>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
}

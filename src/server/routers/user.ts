/**
 * User router for tRPC
 */
import { z } from 'zod';
import { router, protectedProcedure } from '../trpc';
import prisma from '@/lib/prisma';

export const userRouter = router({
  // Search for users
  search: protectedProcedure
    .input(
      z.object({
        query: z.string().min(1),
        limit: z.number().optional().default(10),
      })
    )
    .query(async ({ input }) => {
      const { query, limit } = input;

      if (!query) {
        return { users: [] };
      }

      // Search for users by name
      const users = await prisma.user.findMany({
        where: {
          name: {
            contains: query,
            mode: 'insensitive',
          },
        },
        select: {
          id: true,
          name: true,
          image: true,
        },
        take: limit,
      });

      return { users };
    }),

  // Get accessible hubs for the current user
  getAccessibleHubs: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session.user.id;

      // Get hubs where user is owner
      const ownedHubs = await prisma.hub.findMany({
        where: { ownerId: userId },
        select: {
          id: true,
          name: true,
          iconUrl: true,
        },
      });

      // Get hubs where user is a moderator or manager
      const moderatedHubs = await prisma.hubModerator.findMany({
        where: { userId },
        select: {
          role: true,
          hub: {
            select: {
              id: true,
              name: true,
              iconUrl: true,
            },
          },
        },
      });

      // Format the response
      const accessibleHubs = [
        ...ownedHubs.map(hub => ({
          ...hub,
          role: 'OWNER' as const,
        })),
        ...moderatedHubs.map(mod => ({
          ...mod.hub,
          role: mod.role,
        })),
      ];

      return { hubs: accessibleHubs };
    }),
});

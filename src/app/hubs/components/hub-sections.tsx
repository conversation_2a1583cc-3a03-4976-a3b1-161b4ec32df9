"use client";

import { <PERSON><PERSON> } from "@/components/ui/button"; // Assuming shadcn/ui button
import { useRouter } from "next/navigation";
import { useTransition } from "react"; // Added useCallback
import { HubCard } from "./hub-card"; // Assuming HubCard is used
// SimplifiedHub might need adjustment if it was only for HubResults/HubGrid
// If HubCard uses the same structure, keep it. Otherwise, adjust/remove.
import { SortOptions } from "../constants";
import { SimplifiedHub } from "@/hooks/use-infinite-hubs";

// --- Type Definitions ---
// Assuming SimplifiedHub is the correct type for the data props and HubCard
// If not, adjust this type definition.
// interface SimplifiedHub { ... }

interface HubSectionsProps {
  mostRecentPopular: { hubs: SimplifiedHub[]; totalCount: number };
  mostUpvotedNew: { hubs: SimplifiedHub[]; totalCount: number };
  recentHubs: SimplifiedHub[];
  trendingHubs: { hubs: SimplifiedHub[]; totalCount: number };
}

// --- SectionHeader Component (Remains the same) ---
function SectionHeader({ title, sort }: { title: string; sort: SortOptions }) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const handleViewAll = () => {
    const params = new URLSearchParams();
    params.set("sort", sort);
    startTransition(() => {
      router.push(`/hubs/search?${params.toString()}`);
    });
  };

  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 md:mb-6 gap-3 sm:gap-4">
      <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-300 bg-clip-text text-transparent">
        {title}
      </h2>
      <Button
        variant="outline"
        size="lg"
        onClick={handleViewAll}
        disabled={isPending}
        className="border-purple-300/50 text-purple-200/90 hover:bg-purple-500/10 hover:text-purple-100 focus-visible:ring-offset-background focus-visible:ring-purple-400 cursor-pointer"
      >
        {isPending ? "Loading..." : "View All"}
      </Button>
    </div>
  );
}

export function HubSections({
  mostRecentPopular,
  mostUpvotedNew,
  recentHubs,
  trendingHubs,
}: HubSectionsProps) {
  // Define sections (remains the same)
  const sections = [
    {
      id: "recentPopular",
      title: "Most Popular Hubs",
      sort: SortOptions.MostRecentPopular,
      data: mostRecentPopular,
      gradient: "from-purple-900/10 to-pink-900/10",
      border: "border-purple-500/20",
    },
    {
      id: "upvotedNew",
      title: "Most Upvoted New Hubs",
      sort: SortOptions.MostUpvotedNew,
      data: mostUpvotedNew,
      gradient: "from-blue-900/10 to-purple-900/10",
      border: "border-blue-500/20",
    },
    {
      id: "recent",
      title: "Recently Created Hubs",
      sort: SortOptions.Created,
      data: { hubs: recentHubs, totalCount: recentHubs.length },
      gradient: "from-indigo-900/10 to-blue-900/10",
      border: "border-indigo-500/20",
    },
    {
      id: "trending",
      title: "Trending Hubs",
      sort: SortOptions.Trending,
      data: trendingHubs,
      gradient: "from-violet-900/10 to-indigo-900/10",
      border: "border-violet-500/20",
    },
  ];

  return (
    <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
      <div className="space-y-10 md:space-y-16">
        {/* Hub Sections Area - Always displayed */}
        <div className="space-y-10 md:space-y-16">
          {sections.map((section) => (
            <section
              key={section.id}
              aria-labelledby={`${section.id}-heading`}
              className={`rounded-2xl bg-gradient-to-br ${section.gradient} p-6 md:p-8 border ${section.border}`}
            >
              <h2
                id={`${section.id}-heading`}
                className="sr-only text-3xl font-bold"
              >
                {section.title}
              </h2>
              <SectionHeader title={section.title} sort={section.sort} />
              {section.data && section.data.hubs.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                  {/* Ensure HubCard is memoized for performance if needed */}
                  {section.data.hubs.map((hub) => (
                    <HubCard key={hub.id} hub={hub} />
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center md:text-left">
                  No hubs found for this category right now.
                </p>
              )}
            </section>
          ))}
        </div>
      </div>
    </main>
  );
}

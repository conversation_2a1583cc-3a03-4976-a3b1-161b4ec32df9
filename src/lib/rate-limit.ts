import { NextRequest, NextResponse } from "next/server";

interface RateLimitContextBase {
  limit: number;
  remaining: number;
  reset: number;
}

export interface RateLimitContext extends RateLimitContextBase {
  success: boolean;
}

// In-memory store for rate limit data
// In production, you'd want to use Redis or another distributed store
const ratelimitCache = new Map<string, RateLimitContextBase>();

export async function rateLimit(
  request: NextRequest,
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  identifier: string = request.ip || "anonymous", // NOTE: request.ip is provided by vercel
  limit: number = 10,
  timeframe: number = 60, // in seconds
): Promise<RateLimitContext> {
  const now = Math.floor(Date.now() / 1000);
  const key = `${identifier}:${request.nextUrl.pathname}`;

  // Get the existing rate limit data or create a new one
  const rateLimitData = ratelimitCache.get(key) || {
    limit,
    remaining: limit,
    reset: now + timeframe,
  };

  // If the reset time has passed, reset the counter
  if (now > rateLimitData.reset) {
    rateLimitData.remaining = limit;
    rateLimitData.reset = now + timeframe;
  }

  // Check if we've hit the rate limit
  const success = rateLimitData.remaining > 0;

  // If we haven't hit the limit, decrement the remaining count
  if (success) {
    rateLimitData.remaining--;
  }

  // Store the updated rate limit data
  ratelimitCache.set(key, rateLimitData);

  return {
    ...rateLimitData,
    success,
  };
}

export function rateLimitResponse(
  context: RateLimitContext,
): NextResponse<{ error: string }> {
  return NextResponse.json(
    { error: "Rate limit exceeded" },
    {
      status: 429,
      headers: {
        "X-RateLimit-Limit": context.limit.toString(),
        "X-RateLimit-Remaining": context.remaining.toString(),
        "X-RateLimit-Reset": context.reset.toString(),
      },
    },
  );
}

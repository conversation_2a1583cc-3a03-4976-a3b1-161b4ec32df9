import { type NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function GET(request: NextRequest) {
	const searchTerm = request.nextUrl.searchParams.get("term");

	if (!searchTerm) {
		return NextResponse.json({ hubs: [] });
	}

	try {
		const hubs = await prisma.hub.findMany({
			where: {
				private: false,
				OR: [
					{ name: { contains: searchTerm, mode: "insensitive" } },
					{ description: { contains: searchTerm, mode: "insensitive" } },
					{ tags: { some: { name: searchTerm } } },
				],
			},
			include: {
				connections: {
					where: { connected: true },
					orderBy: { lastActive: "desc" },
					select: {
						id: true,
						serverId: true,
						connected: true,
						compact: true,
						createdAt: true,
						lastActive: true,
						joinRequestsDisabled: true,
						server: {
							select: {
								id: true,
								name: true,
							},
						},
					},
				},
				upvotes: true,
				moderators: { include: { user: true } },
			},
			take: 12,
		});

		return NextResponse.json({ hubs });
	} catch (error) {
		console.error("Error searching hubs:", error);
		return NextResponse.json(
			{ error: "Failed to search hubs" },
			{ status: 500 },
		);
	}
}

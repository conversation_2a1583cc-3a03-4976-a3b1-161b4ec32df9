import { HubsHero } from "./components/HubsHero";
import HubContentLoading from "./components/HubContentLoading";

/**
 * Loading state for the entire hubs page
 * Shows the actual HubsHero component (which doesn't need loading state)
 * and the loading skeleton for the data-dependent content
 */
export default function HubsLoading() {
  return (
    <>
      {/* Render the actual Hero component since it doesn't need loading state */}
      <HubsHero />

      {/* Use the same loading component as the Suspense fallback */}
      <HubContentLoading />
    </>
  );
}

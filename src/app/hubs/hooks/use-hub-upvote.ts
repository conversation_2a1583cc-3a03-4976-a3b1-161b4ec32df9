import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { HubUpvote } from "@/lib/generated/prisma/client";

export function useHubUpvote(hubId: string, initialUpvotes: HubUpvote[]) {
  const { data: session } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const [liked, setLiked] = useState(false);
  const [upvoteCount, setUpvoteCount] = useState(initialUpvotes.length);

  useEffect(() => {
    if (session?.user?.id) {
      setLiked(
        initialUpvotes.some((upvote) => upvote.userId === session.user.id)
      );
    }
  }, [session, initialUpvotes]);

  const handleUpvote = useCallback(
    async (e?: React.MouseEvent) => {
      e?.preventDefault();
      e?.stopPropagation();

      if (!session?.user) {
        toast({
          title: "Authentication required",
          description: "Please sign in to upvote hubs",
        });
        router.push("/login");
        return;
      }

      // Optimistic UI update
      const wasLiked = liked;
      setLiked(!wasLiked);
      setUpvoteCount((prevCount) => (wasLiked ? prevCount - 1 : prevCount + 1));

      try {
        const response = await fetch(`/api/hubs/${hubId}/upvote`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error("Failed to update upvote");
        }

        const { upvoted: newLikeState } = await response.json();

        toast({
          title: newLikeState ? "Upvoted hub" : "Removed upvote",
          description: newLikeState
            ? "Thanks for your support!"
            : "You've removed your upvote",
          duration: 2000,
        });

        router.refresh(); // Refresh server components
      } catch {
        // Revert on error
        setLiked(wasLiked);
        setUpvoteCount((prevCount) =>
          wasLiked ? prevCount + 1 : prevCount - 1
        );

        toast({
          title: "Error",
          description: "Failed to update upvote status",
          variant: "destructive",
        });
      }
    },
    [hubId, liked, router, session, toast]
  );

  return {
    liked,
    upvoteCount,
    handleUpvote,
  };
}

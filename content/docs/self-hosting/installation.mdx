---
title: Installation Guide
description: Step-by-step instructions for installing InterChat
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Steps } from 'fumadocs-ui/components/steps'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'

# Installation Guide

This guide will walk you through the process of installing and setting up your own instance of InterChat.

<Callout type="info">
  These instructions are for Linux-based systems. The process may differ for other operating systems.
</Callout>

## Prerequisites

Before you begin, ensure you have:

- A server running Linux (Ubuntu 20.04+ recommended)
- Root or sudo access
- A Discord application with a bot token
- A MongoDB Atlas account and cluster
- Basic knowledge of command line and server administration

## Installation Steps

<Steps>
  ### Install system dependencies

  First, install the necessary system dependencies:

  ```bash
  # Update package lists
  sudo apt update

  # Install dependencies
  sudo apt install -y git curl build-essential
  ```

  ### Install Node.js

  InterChat requires Node.js and npm runtime:

  <Tabs items={['Using package manager', 'Using NVM']}>
    <Tab>
      ```bash
      # Install Node.js
      curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
      sudo apt install -y nodejs

      # Install Bun
      curl -fsSL https://bun.sh/install | bash
      source ~/.bashrc  # or restart your terminal
      ```
    </Tab>
    <Tab>
      ```bash
      # Install NVM
      curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash
      source ~/.bashrc  # or restart your terminal

      # Install Node.js
      nvm install 18
      nvm use 18

      # Install Bun
      curl -fsSL https://bun.sh/install | bash
      source ~/.bashrc  # or restart your terminal
      ```
    </Tab>
  </Tabs>

  ### Set up MongoDB Atlas

  InterChat uses MongoDB Atlas for its database:

  1. Create a [MongoDB Atlas account](https://www.mongodb.com/cloud/atlas/register)
  2. Create a new cluster (the free tier is sufficient for small deployments)
  3. Set up a database user with read/write permissions
  4. Configure network access to allow connections from your server's IP
  5. Get your connection string from the Atlas dashboard

  Your connection string will look like this:
  ```
  mongodb+srv://username:<EMAIL>/interchat?retryWrites=true&w=majority
  ```

  ### Install Redis

  InterChat uses Redis for caching and session management:

  ```bash
  # Install Redis
  sudo apt install -y redis-server

  # Start and enable Redis
  sudo systemctl start redis-server
  sudo systemctl enable redis-server
  ```

  ### Clone the InterChat repository

  ```bash
  # Clone the repository
  git clone https://github.com/discord-interchat/InterChat.git
  cd InterChat
  ```

  ### Install dependencies

  ```bash
  # Install dependencies
  npm install
  ```

  ### Configure environment variables

  Create a `.env` file in the root directory and edit it:

  ```bash
  vim .env # or use any other text editor
  ```

  Fill in the required environment variables (see [Configuration Guide](/docs/self-hosting/configuration) for details).

  ### Set up the database

  ```bash
  # Generate Prisma client
  npx prisma generate
  ```

  ### Build the application

  ```bash
  # Build the application
  npm run build
  npm run locale-types

  # Sync commands and emojis
  npm run sync:commands --private --public
  npm run sync:emojis
  ```

  ### Start InterChat

  <Tabs items={['Development mode', 'Production mode']}>
    <Tab>
      ```bash
      # Start in development mode
      npm dev
      ```
    </Tab>
    <Tab>
      ```bash
      # Install PM2 for process management
      npm install -g pm2

      # Start in production mode
      npm run start:prod

      # Ensure it starts on boot
      pm2 startup
      pm2 save
      ```
    </Tab>
  </Tabs>
</Steps>

## Verifying Installation

To verify that InterChat is running correctly:

1. Check the logs for any errors:
   ```bash
   # If using PM2
   pm2 logs interchat
   ```

2. Invite your bot to a Discord server and test basic commands like `/stats`.

3. Try creating a hub with `/hub create` and connecting a channel with `/connect`.

<Callout type="tip">
  If you encounter issues, check the logs for error messages and refer to the [troubleshooting section](#troubleshooting) below.
</Callout>

## Updating InterChat

To update your InterChat instance to the latest version:

```bash
# Pull the latest changes
git pull

# Install dependencies
npm install
npm run locale-types

# Build the application
npm run build

# Sync commands and emojis
npm run sync:commands --private --public
npm run sync:emojis

# Restart the application
pm2 restart interchat
```

## Troubleshooting

### Common Issues

1. **Database connection errors**:
   - Check your MongoDB Atlas connection string in the `.env` file
   - Ensure your IP address is whitelisted in the MongoDB Atlas Network Access settings
   - Verify that your MongoDB Atlas user has the correct permissions

2. **Redis connection errors**:
   - Ensure Redis is running: `sudo systemctl status redis-server`
   - Check your Redis configuration in the `.env` file

3. **Discord API errors**:
   - Verify your bot token is correct
   - Ensure your bot has the necessary permissions
   - Check that you've enabled the required Gateway Intents in the Discord Developer Portal

4. **Build errors**:
   - Ensure you have the correct version of Node.js
   - Try deleting `node_modules`, `build`, `tsconfig.tsbuildinfo` and reinstalling dependencies

For more help, join our [support server](https://discord.gg/cgYgC6YZyX) or check the [GitHub issues](https://github.com/discord-interchat/InterChat/issues).

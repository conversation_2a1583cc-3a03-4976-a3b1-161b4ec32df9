"use client";

import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare } from "lucide-react";
import { useSession } from "next-auth/react";
import { useState } from "react";
import WriteReviewForm from "./WriteReviewForm";

interface ClientReviewSectionProps {
  hubId: string;
}

export default function ClientReviewSection({ hubId }: ClientReviewSectionProps) {
  const { data: session } = useSession();
  const [showForm, setShowForm] = useState(false);

  if (!session?.user) {
    return (
      <div className="flex flex-col items-center justify-center p-6 bg-gray-800/30 rounded-lg border border-gray-700/30 mb-6">
        <MessageSquare className="h-8 w-8 text-gray-500 mb-3" />
        <p className="text-center text-gray-300 mb-2">
          Sign in to write a review
        </p>
        <Button
          variant="outline"
          className="mt-2 border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 text-gray-200"
          asChild
        >
          <a href={`/login?callbackUrl=/hubs/${hubId}`}>Sign In</a>
        </Button>
      </div>
    );
  }

  return (
    <>
      {showForm ? (
        <WriteReviewForm hubId={hubId} />
      ) : (
        <div className="flex justify-center mb-6">
          <Button
            onClick={() => setShowForm(true)}
            className="bg-gradient-to-r from-primary to-primary-alt text-white font-medium py-2 px-4 rounded-lg hover:opacity-90 transition-opacity"
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            Write a Review
          </Button>
        </div>
      )}
    </>
  );
}

"use client";

import { AnimatedHeroBackground } from "@/components/AnimatedHeroBackground";
import { InterChatDemo } from "@/components/chat-demo/DemoComponent";
import { Button } from "@/components/ui/button";
import {
  ArrowRight,
  Globe,
  LayoutDashboard,
  Sparkles,
  Users,
} from "lucide-react";
import { motion } from "motion/react";
import { memo, useEffect, useState } from "react";

export const HeroSection = memo(() => {
  // Use state to ensure consistent client-side rendering
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Only render the full component on the client side
  if (!isMounted) {
    return (
      <section
        className="relative overflow-hidden pt-0 pb-20 md:pb-28 bg-gradient-to-b from-gray-900 to-gray-950 border-b border-gray-800/30"
        id="hero"
      >
        <div className="container mx-auto px-4 relative z-10 pt-24 md:pt-28">
          {/* Empty placeholder with same height to prevent layout shift */}
          <div className="min-h-[600px]" />
        </div>
      </section>
    );
  }

  return (
    <section
      className="relative overflow-hidden pt-0 pb-20 md:pb-28 bg-gradient-to-b from-gray-900 to-gray-950 border-b border-gray-800/30"
      id="hero"
    >
      {/* Animated background */}
      <AnimatedHeroBackground />
      <div className="absolute inset-0 bg-mesh-gradient opacity-30 mix-blend-overlay z-0" />

      <div className="container mx-auto px-4 relative z-10 pt-24 md:pt-28">
        {/* Hero Content */}
        <div className="flex flex-col lg:flex-row items-center gap-16">
          <div className="flex-1">
            <motion.h1
              className="text-4xl text-start lg:text-6xl font-bold mb-7 tracking-tight leading-tight bg-clip-text text-transparent bg-gradient-to-r from-white via-primary-alt to-primary transform-gpu"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              Connected Communities, One Message Away
            </motion.h1>

            <motion.p
              className="text-lg text-start mb-8 text-gray-300 leading-relaxed max-w-2xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              Unite your Discord server with a thriving network of like-minded communities.
              One channel. Hundreds of conversations. Endless possibilities.
            </motion.p>

            {/* CTA buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-5 mb-10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Button
                size="lg"
                className="h-14 px-8 rounded-full bg-gradient-to-r from-primary to-primary-alt hover:from-primary-alt hover:to-primary text-white font-medium cursor-pointer shadow-xl shadow-primary/25 hover:shadow-2xl hover:shadow-primary/35 transition-all duration-300 transform hover:-translate-y-1 border-none"
              >
                <a
                  href="/invite"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center"
                  aria-label="Add InterChat to your Discord server"
                >
                  Add InterChat Now
                  <ArrowRight
                    className="ml-3 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1"
                    aria-hidden="true"
                  />
                </a>
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="h-14 px-8 rounded-full text-white bg-gray-800 border border-gray-700/70 hover:bg-gray-700 hover:border-primary/50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
              >
                <a
                  href="/dashboard"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center"
                  aria-label="InterChat Dashboard"
                >
                  Dashboard
                  <LayoutDashboard
                    className="ml-3 h-5 w-5"
                    aria-hidden="true"
                  />
                </a>
              </Button>
            </motion.div>

            {/* Stats indicators */}
            <motion.div
              className="flex flex-wrap justify-start gap-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <motion.div
                className="flex items-center gap-3 text-gray-200 bg-gray-800 px-4 py-2 rounded-full border border-gray-700/50 shadow-md"
                whileHover={{ scale: 1.05, y: -2 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Sparkles className="w-5 h-5 text-yellow-400" />
                <span>30+ Hubs Chatting Now</span>
              </motion.div>
              <motion.div
                className="flex items-center gap-3 text-gray-200 bg-gray-800 px-4 py-2 rounded-full border border-gray-700/50 shadow-md"
                whileHover={{ scale: 1.05, y: -2 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Users className="w-5 h-5 text-blue-400" />
                <span>10,000+ Discord Servers</span>
              </motion.div>
              <motion.div
                className="flex items-center gap-3 text-gray-200 bg-gray-800 px-4 py-2 rounded-full border border-gray-700/50 shadow-md"
                whileHover={{ scale: 1.05, y: -2 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Globe className="w-5 h-5 text-green-400" />
                <span>Worldwide Communities</span>
              </motion.div>
            </motion.div>
          </div>

          {/* Demo container */}
          <motion.div
            className="flex-1 relative transform-gpu"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            style={{ willChange: "transform" }}
          >
            <div className="absolute -inset-1 bg-gradient-to-r from-primary-alt/50 to-primary/50 rounded-xl blur-md opacity-75 transform-gpu animate-pulse-slow" />

            {/* Single decorative element - reduced for performance */}
            <div className="absolute -bottom-6 sm:-bottom-8 -left-6 sm:-left-8 w-14 sm:w-20 h-14 sm:h-20 bg-primary-alt/20 rounded-full blur-lg z-0" />

            <div className="relative bg-[#313338] p-3 sm:p-5 rounded-xl shadow-2xl border border-gray-700/50">
              {/* Add alt text or aria-label if InterChatDemo contains meaningful visual info */}
              <InterChatDemo />
              <div className="absolute -bottom-3 sm:-bottom-4 -right-3 sm:-right-4 bg-gradient-to-r from-primary to-primary-alt px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg text-xs sm:text-sm font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center gap-1.5">
                <span className="h-1.5 w-1.5 bg-green-500 rounded-full animate-pulse hidden sm:block" />
                Live Preview
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
});

HeroSection.displayName = "HeroSection";

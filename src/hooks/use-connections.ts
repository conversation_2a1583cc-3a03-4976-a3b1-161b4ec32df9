"use client";

import { useQuery, useMutation, useQueryClient } from "@/lib/tanstack-query";
import { getHubConnections, removeConnection, ConnectionData } from "@/lib/api";
import { BasicHubConnection, ServerData } from "@/app/dashboard/hubs/[hubId]/connections/client";
import { useToast } from "@/components/ui/use-toast";
import { useErrorNotification } from "./use-error-notification";

// Query keys
export const connectionKeys = {
  all: ["connections"] as const,
  lists: () => [...connectionKeys.all, "list"] as const,
  list: (hubId: string) => ["connections", hubId] as const, // Simplified to match server prefetching
  details: () => [...connectionKeys.all, "detail"] as const,
  detail: (id: string) => [...connectionKeys.details(), id] as const,
};

// Hook for fetching connections
export function useConnections(hubId: string) {
  const query = useQuery<(BasicHubConnection & { server: ServerData | null })[]>({
    queryKey: connectionKeys.list(hubId),
    queryFn: () => getHubConnections(hubId),
    staleTime: 60 * 1000, // 1 minute stale time
    gcTime: 5 * 60 * 1000, // 5 minutes before garbage collection
    select: (data) => {
      // Sort connections by lastActive date (most recent first)
      return [...data].sort((a, b) => {
        return new Date(b.lastActive).getTime() - new Date(a.lastActive).getTime();
      });
    },
  });

  // Handle error with useErrorNotification
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: "Error",
    description: "Failed to load connections",
  });

  return query;
}

// Hook for removing a connection
export function useRemoveConnection() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: removeConnection,
    onMutate: async (connectionId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: connectionKeys.lists() });

      // Get the current connections from the cache
      const previousConnections = queryClient.getQueriesData({
        queryKey: connectionKeys.lists(),
      });

      // Optimistically update the cache
      queryClient.setQueriesData(
        { queryKey: connectionKeys.lists() },
        (old: unknown) => {
          if (!old) return old;
          if (Array.isArray(old)) {
            return old.filter((conn: ConnectionData) => conn.id !== connectionId);
          }
          return old;
        }
      );

      return { previousConnections };
    },
    onSuccess: () => {
      toast({
        description: "Connection removed successfully",
      });
    },
    onError: (error, _, context) => {
      // Revert the optimistic update
      if (context?.previousConnections) {
        context.previousConnections.forEach(([queryKey, previousData]) => {
          queryClient.setQueryData(queryKey, previousData);
        });
      }

      toast({
        variant: "destructive",
        title: "Error",
        description: `Failed to remove connection: ${error.message}`,
      });
    },
    onSettled: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: connectionKeys.lists() });
    },
  });
}

"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

export function ConnectToHubsButton() {
  const handleClick = () => {
    // Find the connections tab and click it
    document
      .querySelector('[data-value="connections"]')
      ?.dispatchEvent(new MouseEvent("click"));
  };

  return (
    <Button
      asChild
      className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
    >
      <Link href="#connections" onClick={handleClick}>
        Connect to Hubs
      </Link>
    </Button>
  );
}

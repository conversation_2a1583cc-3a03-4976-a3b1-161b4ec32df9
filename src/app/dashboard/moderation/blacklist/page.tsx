import { auth } from "@/auth";
import { <PERSON>listHero } from "@/components/dashboard/moderation/blacklist-hero";
import { HubSelector } from "@/components/dashboard/moderation/hub-selector";
import { PaginationControls } from "@/components/dashboard/moderation/pagination-controls";
import { UnderlinedTabs } from "@/components/dashboard/underlined-tabs";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { TabsContent } from "@/components/ui/tabs";
import { PermissionLevel } from "@/lib/constants";
import { Hub, Infraction, User } from "@/lib/generated/prisma/client";
import { getUserHubPermission, getUserHubs } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { formatDistanceToNow } from "date-fns";
import { ArrowLeft, Clock, Home, PlusCircle, Shield } from "lucide-react";
import { <PERSON>ada<PERSON> } from "next";
import Image from "next/image";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";
import { PermissionCheck } from "./permission-check";

export interface FullInfraction extends Infraction {
  hub: {
    id: Hub["id"];
    name: Hub["name"];
    iconUrl: Hub["iconUrl"];
  };
  moderator: {
    name: User["name"];
    image: User["image"];
  } | null;
  user: {
    name: User["name"];
    image: User["image"];
  } | null;
}

export const metadata: Metadata = {
  title: "Blacklist | InterChat Dashboard",
  description: "Manage blacklisted users and servers for your InterChat hubs",
};

// Helper function to calculate duration
function getDuration(createdAt: Date, expiresAt: Date | null): string {
  if (!expiresAt) return "Permanent";

  const durationMs = expiresAt.getTime() - createdAt.getTime();
  const durationDays = Math.round(durationMs / (1000 * 60 * 60 * 24));

  if (durationDays <= 1) return "1 day";
  if (durationDays <= 7) return `${durationDays} days`;
  if (durationDays <= 30) return `${Math.ceil(durationDays / 7)} weeks`;
  if (durationDays <= 365) return `${Math.ceil(durationDays / 30)} months`;
  return `${Math.ceil(durationDays / 365)} years`;
}

export default async function BlacklistPage(props: {
  searchParams: Promise<{ hubId?: string; page?: string }>;
}) {
  const searchParams = await props.searchParams;
  const session = await auth();

  // Use the permission check component
  await PermissionCheck({ hubId: searchParams.hubId });

  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard/moderation/blacklist");
  }

  const userHubs = await getUserHubs(session.user.id);

  // Filter hubs where user has at least moderator permissions
  const moderatedHubs = userHubs.filter(
    (hub) => hub.permissionLevel >= PermissionLevel.MODERATOR,
  );

  if (moderatedHubs.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="sm" className="mr-2" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">Blacklist</h1>
        </div>
        <Card className="border-gray-800 bg-[#0f1117]">
          <CardHeader>
            <CardTitle>No Moderation Access</CardTitle>
            <CardDescription>
              You don&apos;t have moderation permissions for any hubs.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-400 mb-4">
              You need to be a hub owner, manager, or moderator to access the
              blacklist.
            </p>
            <Button asChild>
              <Link href="/dashboard/hubs">View My Hubs</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Filter blacklist based on selected hub
  const selectedHubId = searchParams.hubId || "all";

  // Additional security check - if a specific hubId is provided, verify the user has permission
  if (selectedHubId !== "all") {
    const permissionLevel = await getUserHubPermission(session.user.id, selectedHubId);
    if (permissionLevel < PermissionLevel.MODERATOR) {
      return notFound();
    }
  }

  const page = parseInt(searchParams.page || "1");
  const pageSize = 15;
  const skip = (page - 1) * pageSize;

  // Fetch blacklisted users and servers from the database with pagination
  const blacklistedItems = await prisma.infraction.findMany({
    where: {
      type: "BLACKLIST",
      status: "ACTIVE",
      hubId:
        selectedHubId !== "all"
          ? selectedHubId
          : {
              in: moderatedHubs.map((hub) => hub.id),
            },
    },
    include: {
      hub: {
        select: {
          id: true,
          name: true,
          iconUrl: true,
        },
      },
      moderator: {
        select: {
          id: true,
          name: true,
          image: true,
        },
      },
      user: {
        select: {
          id: true,
          name: true,
          image: true,
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
    skip,
    take: pageSize,
    
  });

  // Get total count of blacklisted users
  const totalBlacklistedUsers = await prisma.infraction.count({
    where: {
      type: "BLACKLIST",
      status: "ACTIVE",
      userId: { not: null },
      hubId:
        selectedHubId !== "all"
          ? selectedHubId
          : {
              in: moderatedHubs.map((hub) => hub.id),
            },
    },
    
  });

  // Get total count of blacklisted servers
  const totalBlacklistedServers = await prisma.infraction.count({
    where: {
      type: "BLACKLIST",
      status: "ACTIVE",
      serverId: { not: null },
      hubId:
        selectedHubId !== "all"
          ? selectedHubId
          : {
              in: moderatedHubs.map((hub) => hub.id),
            },
    },
    
  });

  // Get total count for pagination
  const totalCount = totalBlacklistedUsers + totalBlacklistedServers;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Separate user and server infractions for the current page
  const blacklistedUsers = blacklistedItems.filter((item) => item.userId);
  const blacklistedServers = blacklistedItems.filter((item) => item.serverId);

  return (
    <div className="space-y-6">
      {/* Animated Hero Section */}
      <BlacklistHero
        totalBlacklisted={totalCount}
        blacklistedUsers={totalBlacklistedUsers}
        blacklistedServers={totalBlacklistedServers}
      />
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl font-bold tracking-tight">Blacklist</h1>

        <div className="flex flex-col sm:flex-row items-center gap-4 sm:gap-2 w-full sm:w-auto">
          <div className="w-full sm:w-auto">
            <HubSelector hubs={moderatedHubs} selectedHubId={selectedHubId} />
          </div>

          <Button
            asChild
            className="bg-gradient-to-r from-red-600 to-purple-600 hover:from-red-600/80 hover:to-purple-600/80 border-none w-full sm:w-auto"
          >
            <Link href="/dashboard/moderation/blacklist/add">
              <PlusCircle className="h-4 w-4 mr-2" />
              Add to Blacklist
            </Link>
          </Button>
        </div>
      </div>
      <UnderlinedTabs
        defaultValue="users"
        className="w-full space-y-6"
        tabs={[
          {
            value: "users",
            label: `Users (${totalBlacklistedUsers})`,
            color: "red",
          },
          {
            value: "servers",
            label: `Servers (${totalBlacklistedServers})`,
            color: "purple",
          },
        ]}
      >
        <TabsContent value="users" className="space-y-4">
          {blacklistedUsers.length > 0 ? (
            <div className="space-y-4">
              {blacklistedUsers.map((infraction) => (
                <BlacklistedUserCard
                  key={infraction.id}
                  infraction={infraction}
                />
              ))}
            </div>
          ) : (
            <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>No Blacklisted Users</CardTitle>
                <CardDescription>
                  There are no users on the blacklist for the selected hub(s).
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-400 mb-4">
                  When you blacklist users, they will appear here.
                </p>
                <Button
                  asChild
                  className="bg-gradient-to-r from-red-600 to-purple-600 hover:from-red-600/80 hover:to-purple-600/80 border-none"
                >
                  <Link href="/dashboard/moderation/blacklist/add?type=user">
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Add User to Blacklist
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="servers" className="space-y-4">
          {blacklistedServers.length > 0 ? (
            <div className="space-y-4">
              {blacklistedServers.map((infraction) => (
                <BlacklistedServerCard
                  key={infraction.id}
                  infraction={infraction}
                />
              ))}
            </div>
          ) : (
            <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>No Blacklisted Servers</CardTitle>
                <CardDescription>
                  There are no servers on the blacklist for the selected hub(s).
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-400 mb-4">
                  When you blacklist servers, they will appear here.
                </p>
                <Button
                  asChild
                  className="bg-gradient-to-r from-red-600 to-purple-600 hover:from-red-600/80 hover:to-purple-600/80 border-none"
                >
                  <Link href="/dashboard/moderation/blacklist/add?type=server">
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Add Server to Blacklist
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </UnderlinedTabs>
      {/* Pagination */}
      <PaginationControls
        currentPage={page}
        totalPages={totalPages}
        selectedHubId={selectedHubId}
      />
    </div>
  );
}

function BlacklistedUserCard({ infraction }: { infraction: FullInfraction }) {
  const blacklistedOn = formatDistanceToNow(new Date(infraction.createdAt), {
    addSuffix: true,
  });

  const expiresIn = infraction.expiresAt
    ? formatDistanceToNow(new Date(infraction.expiresAt), {
        addSuffix: true,
      })
    : "Never";

  const duration = getDuration(
    new Date(infraction.createdAt),
    infraction.expiresAt,
  );

  return (
    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded-full border-2 border-gray-700/50 overflow-hidden">
              <Image
                src={infraction.hub.iconUrl}
                alt={infraction.hub.name}
                width={32}
                height={32}
                className="object-cover"
                style={{ width: "100%", height: "100%" }}
                unoptimized
              />
            </div>
            <CardTitle className="text-lg">{infraction.hub.name}</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-sm text-gray-400 flex items-center gap-1">
              <Clock className="h-3 w-3 text-gray-500" />
              Blacklisted {blacklistedOn}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full border-2 border-red-500/20 overflow-hidden">
            <Image
              src={
                infraction.user?.image ||
                "https://api.dicebear.com/7.x/shapes/svg?seed=user"
              }
              alt={infraction.user?.name || "Unknown User"}
              width={40}
              height={40}
              className="object-cover"
              style={{ width: "100%", height: "100%" }}
              unoptimized
            />
          </div>
          <div>
            <div className="font-medium">
              <Link
                href={`https://discordlookup.com/user/${infraction.userId}`}
                target="_blank"
                className="hover:text-red-400 transition-colors"
              >
                {infraction.user?.name || "Unknown User"}
              </Link>
            </div>
            <div className="text-xs text-gray-400">
              User ID: {infraction.userId}
            </div>
          </div>
        </div>

        <div className="p-3 rounded-md bg-gray-900/50 border border-gray-800/50">
          <div className="text-sm mb-2 text-gray-400">Reason:</div>
          <div className="text-sm">{infraction.reason}</div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Duration:</span> {duration}
          </div>
          <div>
            <span className="text-gray-400">Expires:</span> {expiresIn}
          </div>
          <div>
            <span className="text-gray-400">Added by:</span>{" "}
            {infraction.moderator?.name || "Unknown"}
          </div>
        </div>

        <div className="flex gap-2 justify-end">
          <Button
            variant="outline"
            className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-blue-400 transition-all"
            asChild
          >
            <Link
              href={`/dashboard/moderation/blacklist/extend/${infraction.id}`}
            >
              <Clock className="h-4 w-4 mr-2" />
              Extend
            </Link>
          </Button>
          <Button
            variant="outline"
            className="border-red-700/30 bg-red-950/20 text-red-400 hover:bg-red-900/30 hover:text-red-300 hover:border-red-700/50 transition-all"
            asChild
          >
            <Link
              href={`/dashboard/moderation/blacklist/remove/${infraction.id}`}
            >
              <Shield className="h-4 w-4 mr-2" />
              Remove
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function BlacklistedServerCard({ infraction }: { infraction: FullInfraction }) {
  const blacklistedOn = formatDistanceToNow(new Date(infraction.createdAt), {
    addSuffix: true,
  });

  const expiresIn = infraction.expiresAt
    ? formatDistanceToNow(new Date(infraction.expiresAt), {
        addSuffix: true,
      })
    : "Never";

  const duration = getDuration(
    new Date(infraction.createdAt),
    infraction.expiresAt,
  );

  return (
    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded-full border-2 border-gray-700/50 overflow-hidden">
              <Image
                src={infraction.hub.iconUrl}
                alt={infraction.hub.name}
                width={32}
                height={32}
                className="object-cover"
                style={{ width: "100%", height: "100%" }}
                unoptimized
              />
            </div>
            <CardTitle className="text-lg">{infraction.hub.name}</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-sm text-gray-400 flex items-center gap-1">
              <Clock className="h-3 w-3 text-gray-500" />
              Blacklisted {blacklistedOn}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full border-2 border-purple-500/20 overflow-hidden bg-gray-800 flex items-center justify-center">
            <Home className="h-5 w-5 text-purple-400" />
          </div>
          <div>
            <div className="font-medium">
              <Link
                href={`https://discordlookup.com/server/${infraction.serverId}`}
                target="_blank"
                className="hover:text-purple-400 transition-colors"
              >
                {infraction.serverName || "Unknown Server"}
              </Link>
            </div>
            <div className="text-xs text-gray-400">
              Server ID: {infraction.serverId}
            </div>
          </div>
        </div>

        <div className="p-3 rounded-md bg-gray-900/50 border border-gray-800/50">
          <div className="text-sm mb-2 text-gray-400">Reason:</div>
          <div className="text-sm">{infraction.reason}</div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Duration:</span> {duration}
          </div>
          <div>
            <span className="text-gray-400">Expires:</span> {expiresIn}
          </div>
          <div>
            <span className="text-gray-400">Added by:</span>{" "}
            {infraction.moderator?.name || "Unknown"}
          </div>
        </div>

        <div className="flex gap-2 justify-end">
          <Button
            variant="outline"
            className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-purple-400 transition-all"
            asChild
          >
            <Link
              href={`/dashboard/moderation/blacklist/extend/${infraction.id}`}
            >
              <Clock className="h-4 w-4 mr-2" />
              Extend
            </Link>
          </Button>
          <Button
            variant="outline"
            className="border-purple-700/30 bg-purple-950/20 text-purple-400 hover:bg-purple-900/30 hover:text-purple-300 hover:border-purple-700/50 transition-all"
            asChild
          >
            <Link
              href={`/dashboard/moderation/blacklist/remove/${infraction.id}`}
            >
              <Shield className="h-4 w-4 mr-2" />
              Remove
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

"use client";

import { pricingTiers } from "@/lib/pricing-constants";

export function PricingSchema() {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://interchat.tech";
  
  // Create schema.org markup for pricing
  const pricingSchema = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "InterChat Discord Bot",
    "description": "Connect Discord servers with cross-server chat, community hubs, and advanced moderation tools.",
    "image": `${baseUrl}/interchat.png`,
    "offers": {
      "@type": "AggregateOffer",
      "priceCurrency": "USD",
      "lowPrice": "0",
      "highPrice": "9.99",
      "offerCount": pricingTiers.length,
      "offers": pricingTiers.map(tier => ({
        "@type": "Offer",
        "name": tier.name,
        "description": tier.description,
        "price": tier.price === "Free" ? "0" : tier.price.replace("$", ""),
        "priceCurrency": "USD",
        "url": `${baseUrl}/pricing#${tier.id}`,
        "availability": "https://schema.org/InStock"
      }))
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(pricingSchema) }}
    />
  );
}

import prisma from "@/lib/prisma";
import type { Prisma } from "@/lib/generated/prisma/client";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";
import { HUBS_PER_PAGE, SortOptions, ContentFilter, VerificationStatus } from "./constants";

const TRENDING_WINDOW_DAYS = 7;
const NEW_HUB_WINDOW_DAYS = 30; // For 'Most Upvoted New Hubs'
const RECENT_ACTIVITY_WINDOW_DAYS = 14; // For 'Most Recent Popular'

const WEIGHT_RECENT_CONNECTION = 10;
const WEIGHT_RECENT_UPVOTE = 5;
const WEIGHT_RECENT_REVIEW = 3;
// Decay factors
const TRENDING_DECAY_BASE = 2.0; // Added to age in hours before power
const TRENDING_DECAY_RATE = 0.5; // The exponent (lower = slower decay)

export interface FilterOptions {
	search?: string;
	tags?: string[];
	contentFilter?: ContentFilter;
	verificationStatus?: VerificationStatus;
	language?: string;
	region?: string;
	minServers?: number;
	maxServers?: number;
}

export function buildWhereClause({
	search,
	tags,
	contentFilter = ContentFilter.All,
	verificationStatus = VerificationStatus.All,
	language,
	region,
	minServers,
	maxServers,
}: FilterOptions): Prisma.HubWhereInput {
	const searchTerms = search?.trim().split(/\s+/).filter(Boolean) || [];

	const filterConditions: Prisma.HubWhereInput[] = [
		{ private: false }, // Always filter out private hubs
	];

	// Filter by tags
	if (tags && tags.length > 0) {
		filterConditions.push({
			tags: { some: { name: { in: tags } } },
		});
	}

	// Filter by content type (SFW/NSFW)
	if (contentFilter !== ContentFilter.All) {
		filterConditions.push({
			nsfw: contentFilter === ContentFilter.NSFW,
		});
	}

	// Filter by verification status
	if (verificationStatus !== VerificationStatus.All) {
		if (verificationStatus === VerificationStatus.VerifiedOrPartnered) {
			filterConditions.push({
				OR: [
					{ verified: true },
					{ partnered: true },
				],
			});
		} else if (verificationStatus === VerificationStatus.Verified) {
			filterConditions.push({ verified: true });
		} else if (verificationStatus === VerificationStatus.Partnered) {
			filterConditions.push({ partnered: true });
		}
	}

	// Filter by language
	if (language && language !== 'all') {
		filterConditions.push({ language });
	}

	// Filter by region
	if (region && region !== 'all') {
		filterConditions.push({ region });
	}

	// Filter by server count range (using connection count)
	if (minServers !== undefined || maxServers !== undefined) {
		// We'll add a basic filter for connections
		// The actual count filtering will be done after the query
		// This ensures we at least get hubs with connections
		if (minServers !== undefined && minServers > 0) {
			filterConditions.push({
				connections: {
					some: {
						connected: true
					}
				}
			});
		}

		// Note: We can't directly filter by count in the where clause
		// We'll handle the actual min/max server filtering in the getSortedHubs function
	}

	// Search terms
	if (searchTerms.length > 0) {
		// biome-ignore lint/complexity/noForEach: <explanation>
		searchTerms.forEach((term) => {
			filterConditions.push({
				OR: [
					{ name: { contains: term, mode: "insensitive" } },
					{ description: { contains: term, mode: "insensitive" } },
					{ tags: { some: { name: { contains: term, mode: "insensitive" } } } },
					{
						moderators: {
							some: {
								OR: [
									{ userId: term }, // Maybe check if term looks like a user ID?
									{ user: { name: { contains: term, mode: "insensitive" } } },
								],
							},
						},
					},
				],
			});
		});
	}

	return { AND: filterConditions };
}

// --- Main Sorting Function ---
export async function getSortedHubs(
	whereClause: Prisma.HubWhereInput,
	skip: number,
	sort: SortOptions,
	minServers?: number,
	maxServers?: number,
) {
	const now = new Date();
	const trendingWindowStart = new Date(
		now.getTime() - TRENDING_WINDOW_DAYS * 24 * 60 * 60 * 1000,
	);
	const newHubWindowStart = new Date(
		now.getTime() - NEW_HUB_WINDOW_DAYS * 24 * 60 * 60 * 1000,
	);
	const recentActivityWindowStart = new Date(
		now.getTime() - RECENT_ACTIVITY_WINDOW_DAYS * 24 * 60 * 60 * 1000,
	);

	// Get total count based on the initial filter (for pagination)
	let totalCount = await prisma.hub.count({ where: whereClause });

	// Flag to indicate if we need post-query filtering for server counts
	const needsServerCountFiltering = minServers !== undefined || maxServers !== undefined;

	let hubIds: string[] = [];
	let hubs: SimplifiedHub[] = [];

	// Define standard includes needed for display
	const standardIncludes = {
		_count: {
			select: {
				connections: { where: { connected: true } },
				upvotes: true,
				reviews: true,
			},
		},
		moderators: {
			include: { user: {select: { id: true, name: true, image: true }} },
		},
		reviews: true,
		connections: {
			where: { connected: true },
			select: { id: true, serverId: true, lastActive: true },
		},
		upvotes: true,
		tags: { select: { name: true } },
	} as const;

	// --- Sorting Logic ---

	if (sort === SortOptions.Trending) {
		// **Trending Sort (Application-Side Scoring)**
		// 1. Fetch IDs, creation dates, and recent activity counts for ALL matching hubs
		// Warning: This can be memory-intensive if `whereClause` matches millions of hubs.
		// Consider adding a LIMIT here if needed (e.g., only score the top 5000 most recently active hubs)
		const hubsForScoring = await prisma.hub.findMany({
			where: whereClause,
			include: {
				...standardIncludes,
				_count: {
					select: {
						connections: {
							where: {
								connected: true,
								lastActive: { gte: trendingWindowStart },
							},
						},
						upvotes: { where: { createdAt: { gte: trendingWindowStart } } },
						reviews: { where: { createdAt: { gte: trendingWindowStart } } },
					},
				},
			},
			orderBy: { lastActive: "desc" },
		});

		// 2. Calculate trending score for each hub
		const scoredHubs = hubsForScoring.map((hub) => {
			const recentConnectionsScore =
				(hub._count?.connections ?? 0) * WEIGHT_RECENT_CONNECTION;
			const recentUpvotesScore =
				(hub._count?.upvotes ?? 0) * WEIGHT_RECENT_UPVOTE;
			const recentReviewsScore =
				(hub._count?.reviews ?? 0) * WEIGHT_RECENT_REVIEW;
			// Use message activity based on lastActive
			const messageActivity =
				(now.getTime() - hub.lastActive.getTime() < 7 * 24 * 60 * 60 * 1000) ? 10 : 0;

			const ageInHours = Math.max(
				1,
				(now.getTime() - hub.createdAt.getTime()) / (1000 * 60 * 60),
			); // Ensure age is at least 1 hour
			const timeDecay =
				1 / (ageInHours + TRENDING_DECAY_BASE) ** TRENDING_DECAY_RATE;

			const trendingScore =
				(recentConnectionsScore + recentUpvotesScore + recentReviewsScore + messageActivity) *
				timeDecay;

			return {
				id: hub.id,
				trendingScore,
			};
		});

		// 3. Sort by trending score
		scoredHubs.sort((a, b) => b.trendingScore - a.trendingScore);

		// 4. Get the IDs for the current page
		hubIds = scoredHubs.slice(skip, skip + HUBS_PER_PAGE).map((h) => h.id);

		// 5. Fetch full data for the hubs on the current page, maintaining the sorted order
		if (hubIds.length > 0) {
			const hubsOnPage = await prisma.hub.findMany({
				where: { id: { in: hubIds } },
				include: standardIncludes,
			});
			// Re-order according to hubIds, as findMany doesn't guarantee order for `in` clause
			hubs = hubIds
				.map((id) => hubsOnPage.find((hub) => hub.id === id))
				.filter(Boolean) as SimplifiedHub[];

			// Apply server count filtering if needed
			if (needsServerCountFiltering) {
				hubs = filterHubsByServerCount(hubs, minServers, maxServers);
				// Recalculate total count if we filtered
				totalCount = await getFilteredTotalCount(whereClause, minServers, maxServers);
			}
		}
	} else if (sort === SortOptions.MostActive) {
		// Most Active sort - based on lastActive
		const hubsWithActivity = await prisma.hub.findMany({
			where: whereClause,
			include: standardIncludes,
			orderBy: [
				{ lastActive: "desc" },
			],
			skip,
			take: HUBS_PER_PAGE,
		});

		// Type assertion to handle the type mismatch
		hubs = hubsWithActivity as unknown as SimplifiedHub[];

		// Apply server count filtering if needed
		if (needsServerCountFiltering) {
			hubs = filterHubsByServerCount(hubs, minServers, maxServers);
			// Recalculate total count if we filtered
			totalCount = await getFilteredTotalCount(whereClause, minServers, maxServers);
		}
	} else {
		// **Database-Side Sorting for other options**
		let orderBy:
			| Prisma.HubOrderByWithRelationInput
			| Prisma.HubOrderByWithRelationInput[]
			| undefined;
		let finalWhereClause = whereClause;

		switch (sort) {
			case SortOptions.MostUpvotedNew:
				// Filter for hubs created within the 'new' window, sort by total upvotes
				finalWhereClause = {
					AND: [whereClause, { createdAt: { gte: newHubWindowStart } }],
				};
				orderBy = { upvotes: { _count: "desc" } };
				break;

			case SortOptions.MostRecentPopular:
				// Filter for hubs active recently, sort by total upvotes
				finalWhereClause = {
					AND: [
						whereClause,
						{ lastActive: { gte: recentActivityWindowStart } },
					],
				};
				orderBy = { upvotes: { _count: "desc" } };
				break;

			case SortOptions.Created:
				orderBy = { createdAt: "desc" };
				break;
			case SortOptions.Name:
				orderBy = { name: "asc" };
				break;
			case SortOptions.Upvotes:
				orderBy = { upvotes: { _count: "desc" } };
				break;
			case SortOptions.Servers:
				orderBy = { connections: { _count: "desc" } };
				break;
			case SortOptions.LastActive:
				orderBy = { lastActive: "desc" };
				break;
			default:
				orderBy = { createdAt: "desc" };
		}

		// For sorts that modify the where clause, we need to recalculate totalCount
		if (
			sort === SortOptions.MostUpvotedNew ||
			sort === SortOptions.MostRecentPopular
		) {
			totalCount = await prisma.hub.count({ where: finalWhereClause });
		}

		// Type assertion to handle the type mismatch
		hubs = await prisma.hub.findMany({
			where: finalWhereClause,
			include: standardIncludes,
			orderBy,
			skip,
			take: HUBS_PER_PAGE,
		}) as unknown as SimplifiedHub[];

		// Apply server count filtering if needed
		if (needsServerCountFiltering) {
			hubs = filterHubsByServerCount(hubs, minServers, maxServers);
			// Recalculate total count if we filtered
			totalCount = await getFilteredTotalCount(whereClause, minServers, maxServers);
		}
	}

	return { hubs, totalCount };
}

// Helper function to filter hubs by server count
function filterHubsByServerCount(
	hubs: SimplifiedHub[],
	minServers?: number,
	maxServers?: number
): SimplifiedHub[] {
	if (!minServers && !maxServers) return hubs;

	return hubs.filter(hub => {
		// Count connected servers - all connections in the result are already connected
		const connectedServerCount = hub.connections?.length || 0;

		// Apply min filter if specified
		if (minServers !== undefined && connectedServerCount < minServers) {
			return false;
		}

		// Apply max filter if specified
		if (maxServers !== undefined && connectedServerCount > maxServers) {
			return false;
		}

		return true;
	});
}

// Helper function to get the total count after server count filtering
async function getFilteredTotalCount(
	whereClause: Prisma.HubWhereInput,
	minServers?: number,
	maxServers?: number
): Promise<number> {
	// If no server count filtering, return the original count
	if (!minServers && !maxServers) {
		return prisma.hub.count({ where: whereClause });
	}

	// Otherwise, we need to fetch all matching hubs and filter them
	const allHubs = await prisma.hub.findMany({
		where: whereClause,
		include: {
			connections: {
				where: { connected: true },
				select: { id: true }
			}
		}
	});

	// Filter by server count
	const filteredHubs = allHubs.filter(hub => {
		const connectedServerCount = hub.connections?.length || 0;

		// Apply min filter if specified
		if (minServers !== undefined && connectedServerCount < minServers) {
			return false;
		}

		// Apply max filter if specified
		if (maxServers !== undefined && connectedServerCount > maxServers) {
			return false;
		}

		return true;
	});

	return filteredHubs.length;
}

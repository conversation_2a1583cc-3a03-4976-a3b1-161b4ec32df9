"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { PermissionLevel } from "@/lib/constants";
import { formatDistanceToNow } from "date-fns";
import { motion } from "motion/react";
import { Clock, Globe, Home, Lock, MessageSquare, Users } from "lucide-react";
import Link from "next/link";
import { useInView } from "react-intersection-observer";

interface HubWithPermission {
  id: string;
  name: string;
  description: string;
  iconUrl: string;
  connections: { id: string }[];
  upvotes: { id: string }[];
  lastActive: Date | null;
  private: boolean;
  permissionLevel: PermissionLevel;
}

interface AnimatedHubCardProps {
  hub: HubWithPermission;
  index: number;
}

export function AnimatedHubCard({ hub, index }: AnimatedHubCardProps) {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const lastActive = hub.lastActive
    ? formatDistanceToNow(new Date(hub.lastActive), { addSuffix: true })
    : "Never";

  // Determine card color based on permission level
  const getCardColor = () => {
    switch (hub.permissionLevel) {
      case PermissionLevel.OWNER:
        return "from-purple-500/10 to-purple-900/5 border-purple-500/20 hover:border-purple-500/40";
      case PermissionLevel.MANAGER:
        return "from-blue-500/10 to-blue-900/5 border-blue-500/20 hover:border-blue-500/40";
      case PermissionLevel.MODERATOR:
        return "from-indigo-500/10 to-indigo-900/5 border-indigo-500/20 hover:border-indigo-500/40";
      default:
        return "from-gray-500/10 to-gray-900/5 border-gray-500/20 hover:border-gray-500/40";
    }
  };

  // Determine role badge based on permission level
  const getRoleBadge = () => {
    switch (hub.permissionLevel) {
      case PermissionLevel.OWNER:
        return (
          <span className="px-2 py-0.5 text-xs rounded-full bg-purple-500/20 text-purple-300 border border-purple-500/30">
            Owner
          </span>
        );
      case PermissionLevel.MANAGER:
        return (
          <span className="px-2 py-0.5 text-xs rounded-full bg-blue-500/20 text-blue-300 border border-blue-500/30">
            Manager
          </span>
        );
      case PermissionLevel.MODERATOR:
        return (
          <span className="px-2 py-0.5 text-xs rounded-full bg-indigo-500/20 text-indigo-300 border border-indigo-500/30">
            Moderator
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: index * 0.05 }}
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      className="h-full"
    >
      <Card
        className={`border bg-gradient-to-b ${getCardColor()} transition-all duration-300 overflow-hidden flex flex-col h-full min-h-[280px]`}
      >
        <CardHeader className="pb-2 relative">
          <div className="absolute top-3 right-3">{getRoleBadge()}</div>
          <div className="flex items-center gap-2 sm:gap-3">
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <Avatar className="h-10 w-10 sm:h-12 sm:w-12 border-2 border-gray-700/50 flex-shrink-0">
                <AvatarImage
                  src={
                    hub.iconUrl?.includes(".gif")
                      ? hub.iconUrl
                      : hub.iconUrl || "/images/default-hub.png"
                  }
                  alt={hub.name}
                  className="object-cover"
                  style={{ width: "100%", height: "100%" }}
                />
                <AvatarFallback className="bg-gray-800 text-gray-400">
                  {hub.name.substring(0, 2)}
                </AvatarFallback>
              </Avatar>
            </motion.div>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-base sm:text-lg truncate">{hub.name}</CardTitle>
              <CardDescription className="line-clamp-1 text-xs sm:text-sm">
                {hub.description}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="py-3 sm:py-4 flex-grow">
          <div className="space-y-2 sm:space-y-3 text-xs sm:text-sm max-w-full">
            <div className="flex justify-between items-center w-full">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-5 w-5 sm:h-6 sm:w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  {hub.private ? (
                    <Lock className="h-3 w-3 text-gray-400" />
                  ) : (
                    <Globe className="h-3 w-3 text-gray-400" />
                  )}
                </div>
                <span className="hidden sm:inline">Status</span>
                <span className="sm:hidden">Status</span>
              </span>
              <span className="flex items-center text-gray-200">
                {hub.private ? "Private" : "Public"}
              </span>
            </div>
            <div className="flex justify-between items-center w-full">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-5 w-5 sm:h-6 sm:w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  <Home className="h-3 w-3 text-gray-400" />
                </div>
                <span className="hidden sm:inline">Connections</span>
                <span className="sm:hidden">Conn.</span>
              </span>
              <span className="text-gray-200">{hub.connections.length}</span>
            </div>
            <div className="flex justify-between items-center w-full">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-5 w-5 sm:h-6 sm:w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  <Users className="h-3 w-3 text-gray-400" />
                </div>
                <span className="hidden sm:inline">Upvotes</span>
                <span className="sm:hidden">Upvotes</span>
              </span>
              <span className="text-gray-200">{hub.upvotes.length}</span>
            </div>
            <div className="flex justify-between items-center w-full">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-5 w-5 sm:h-6 sm:w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  <Clock className="h-3 w-3 text-gray-400" />
                </div>
                <span className="hidden sm:inline">Last Active</span>
                <span className="sm:hidden">Active</span>
              </span>
              <span className="text-gray-200 text-right truncate max-w-[120px] sm:max-w-none">{lastActive}</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-0 pb-3 sm:pb-4 border-t border-gray-800/50">
          <Button
            asChild
            className="w-full bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 border-none"
          >
            <Link href={`/dashboard/hubs/${hub.id}`}>
              <MessageSquare className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Manage Hub</span>
              <span className="sm:hidden">Manage</span>
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
}

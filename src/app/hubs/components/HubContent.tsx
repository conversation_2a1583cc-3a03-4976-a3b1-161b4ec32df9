"use server";

import { FeaturedHubsContainer } from "./FeaturedHubsContainer";
import { HubSections } from "./hub-sections";
import { HubListingSchema } from "./HubsSchema";
import { getSortedHubs } from "../utils";
import prisma from "@/lib/prisma";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";
import { SortOptions } from "../constants";

/**
 * Fetch recently created hubs
 */
async function getRecentHubs(): Promise<SimplifiedHub[]> {
	return await prisma.hub.findMany({
		where: { private: false },
		include: {
			connections: {
				where: { connected: true },
				select: { id: true, serverId: true, lastActive: true },
			},
			moderators: {
				include: { user: { select: { id: true, name: true, image: true } } },
			},
			upvotes: true,
			reviews: true,
			tags: { select: { name: true } },
		},
		orderBy: { createdAt: "desc" },
		take: 6,
	});
}

export default async function HubContent() {
	// Fetch all hub data in parallel for better performance
	const [mostRecentPopular, mostUpvotedNew, recentHubs, trendingHubs] =
		await Promise.all([
			getSortedHubs({ private: false }, 0, SortOptions.MostRecentPopular),
			getSortedHubs({ private: false }, 0, SortOptions.MostUpvotedNew),
			getRecentHubs(),
			getSortedHubs({ private: false }, 0, SortOptions.Trending),
		]);

	// Get the base URL for structured data
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://interchat.tech";

	// Create a deduplicated list of all hubs for the schema
	const allUniqueHubs = [
		...mostRecentPopular.hubs,
		...mostUpvotedNew.hubs,
		...recentHubs,
		...trendingHubs.hubs,
	].filter(
		(hub, index, self) => index === self.findIndex((h) => h.id === hub.id),
	);

	return (
		<>
			{/* Featured hubs carousel */}
			<FeaturedHubsContainer />

			{/* Structured data for SEO */}
			<HubListingSchema hubs={allUniqueHubs} baseUrl={baseUrl} />

			{/* Main hub sections */}
			<HubSections
				mostRecentPopular={mostRecentPopular}
				mostUpvotedNew={mostUpvotedNew}
				recentHubs={recentHubs}
				trendingHubs={trendingHubs}
			/>
		</>
	);
}

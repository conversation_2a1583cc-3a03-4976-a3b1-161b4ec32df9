"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON>ip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Globe, Info, Search, Sparkles, Users } from "lucide-react";
import { motion } from "motion/react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { AnimatedBackground } from "./AnimatedBackground";

export function HubsHero() {
  const [searchTerm, setSearchTerm] = useState("");
  const [showHelp, setShowHelp] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Check if this is the user's first visit
  useEffect(() => {
    const hasVisitedBefore = localStorage.getItem("hasVisitedHubsPage");
    if (!hasVisitedBefore) {
      localStorage.setItem("hasVisitedHubsPage", "true");
      // Show help tooltip after a short delay
      setTimeout(() => setShowHelp(true), 1000);
    }
  }, []);

  // Get current sort from URL or default to trending
  const searchParams = useSearchParams();
  const currentSort = searchParams.get("sort") || "trending";

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      router.push(
        `/hubs/search?search=${encodeURIComponent(searchTerm.trim())}&sort=${currentSort}`,
      );
    }
  };

  return (
    <div className="relative overflow-hidden pt-0 bg-gradient-to-b from-black to-gray-950 border-b border-gray-800/30">
      {/* Background elements */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/10 via-gray-900/80 to-gray-950 opacity-80" />
      <AnimatedBackground />

      {/* Decorative elements */}
      <div className="absolute top-20 left-1/4 w-72 h-72 bg-primary/20 rounded-full blur-3xl opacity-20" />
      <div className="absolute bottom-10 right-1/4 w-80 h-80 bg-primary-alt/20 rounded-full blur-3xl opacity-20" />

      {/* Content */}
      <div className="container mx-auto px-4 pt-20 pb-16 md:pt-24 md:pb-20 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Main heading with enhanced gradient */}
          <motion.h1
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white via-primary-alt to-primary drop-shadow-sm"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Find Your Cross-Server Hub On InterChat
          </motion.h1>

          {/* Subtitle with improved styling */}
          <motion.p
            className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            Discover and join thriving cross-server hubs on InterChat. Connect with active
            communities and grow your Discord server.
          </motion.p>

          {/* Search form with enhanced styling and accessibility */}
          <motion.div
            className="mb-10"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <form
              onSubmit={handleSearch}
              className="flex max-w-xl mx-auto relative"
              aria-label="Search for Discord hubs"
            >
              <div className="relative flex-grow shadow-lg">
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 pointer-events-none">
                  <div className="absolute inset-0 bg-primary/20 rounded-full blur-md animate-pulse" />
                  <Search
                    className="text-white h-5 w-5 relative"
                    aria-hidden="true"
                  />
                </div>
                <input
                  ref={searchInputRef}
                  type="search"
                  placeholder="Search for gaming, art, anime hubs..."
                  className="w-full h-14 pl-12 pr-4 rounded-xl bg-gray-800/70 backdrop-blur-md border border-gray-700/70 focus:border-primary focus:ring-2 focus:ring-primary/50 text-white transition-all duration-300"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  aria-label="Search for InterChat hubs"
                  autoComplete="off"
                />
              </div>
              <Button
                type="submit"
                className="h-14 px-6 ml-2 rounded-xl bg-gradient-to-r from-primary to-primary-alt hover:from-primary-alt hover:to-primary text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5"
                aria-label="Submit search"
                disabled={!searchTerm.trim()}
              >
                Search
              </Button>

              {/* Help tooltip */}
              <TooltipProvider>
                <Tooltip open={showHelp} onOpenChange={setShowHelp}>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-[-50px] top-1/2 transform -translate-y-1/2 bg-gray-800/70 hover:bg-gray-700/70 text-gray-300 hover:text-white"
                      onClick={() => setShowHelp(!showHelp)}
                      aria-label="Show search help"
                    >
                      <Info className="h-5 w-5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="max-w-xs p-4 bg-gray-900 border-gray-700 text-gray-200">
                    <p className="mb-2 font-medium">Find Discord Chat-Rooms</p>
                    <p className="text-sm mb-2">Search for hubs by name, description, or browse by popular tags below.</p>
                    <p className="text-sm">Each hub connects multiple Discord servers together for cross-server chat.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </form>
          </motion.div>

          {/* Stats with enhanced styling and accessibility */}
          <motion.div
            className="flex flex-wrap justify-center gap-4 md:gap-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            aria-label="Platform statistics"
          >
            <motion.div
              className="flex items-center gap-3 text-gray-200 bg-gray-800/40 backdrop-blur-sm px-4 py-2 rounded-xl border border-gray-700/50 shadow-md"
              whileHover={{ scale: 1.05, y: -2 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Sparkles className="w-5 h-5 text-yellow-400" aria-hidden="true" />
              <span>30+ Active Hubs</span>
            </motion.div>
            <motion.div
              className="flex items-center gap-3 text-gray-200 bg-gray-800/40 backdrop-blur-sm px-4 py-2 rounded-xl border border-gray-700/50 shadow-md"
              whileHover={{ scale: 1.05, y: -2 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Users className="w-5 h-5 text-blue-400" aria-hidden="true" />
              <span>10,000+ Servers</span>
            </motion.div>
            <motion.div
              className="flex items-center gap-3 text-gray-200 bg-gray-800/40 backdrop-blur-sm px-4 py-2 rounded-xl border border-gray-700/50 shadow-md"
              whileHover={{ scale: 1.05, y: -2 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Globe className="w-5 h-5 text-green-400" aria-hidden="true" />
              <span>Global Communities</span>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

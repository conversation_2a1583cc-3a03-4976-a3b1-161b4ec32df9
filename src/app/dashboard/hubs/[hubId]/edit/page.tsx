"use client";

import { DeleteHubDialog } from "@/components/dashboard/hubs/delete-hub-dialog";
import { HubNavigationTabs } from "@/components/dashboard/hubs/hub-navigation-tabs";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft, ArrowUp, ArrowDown, Loader2, Save, Trash2 } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function HubEditPage() {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [isPrivate, setIsPrivate] = useState(true);
  const [welcomeMessage, setWelcomeMessage] = useState("");
  const [rules, setRules] = useState<string[]>([]);
  const [newRule, setNewRule] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isOwner, setIsOwner] = useState(false);
  const { toast } = useToast();
  const { hubId } = useParams();
  const router = useRouter();

  const supportedWelcomeVariables = [
    "{user}",
    "{hubName}",
    "{serverName}",
    "{memberCount}",
    "{totalConnections}",
  ];

  const formattedWelcomeVariablesList = supportedWelcomeVariables.map(
    (variable) => (
      <code
        key={variable}
        className="bg-primary/20 px-2 py-1 rounded cursor-pointer inline-block hover:bg-primary/30 transition-colors"
        onClick={() => setWelcomeMessage(welcomeMessage + variable)}
      >
        {variable}
      </code>
    )
  );

  // Fetch hub data
  useEffect(() => {
    const fetchHub = async () => {
      try {
        const response = await fetch(`/api/hubs/${hubId}`);

        if (!response.ok) {
          throw new Error("Failed to fetch hub data");
        }

        const data = await response.json();

        setName(data.hub.name);
        setDescription(data.hub.description);
        setIsPrivate(data.hub.private);
        setWelcomeMessage(data.hub.welcomeMessage || "");
        setRules(data.hub.rules || []);
        setIsOwner(data.hub.isOwner === true);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching hub:", error);
        toast({
          title: "Error",
          description: "Failed to load hub data. Please try again.",
          variant: "destructive",
        });
        router.push(`/dashboard/hubs/${hubId}`);
      }
    };

    fetchHub();
  }, [hubId, toast, router]);

  const handleAddRule = () => {
    if (newRule.trim() && !rules.includes(newRule.trim())) {
      setRules([...rules, newRule.trim()]);
      setNewRule("");
    }
  };

  const handleRemoveRule = (index: number) => {
    setRules(rules.filter((_, i) => i !== index));
  };

  const handleMoveRuleUp = (index: number) => {
    if (index === 0) return; // Can't move up if already at the top
    const newRules = [...rules];
    [newRules[index - 1], newRules[index]] = [newRules[index], newRules[index - 1]];
    setRules(newRules);
  };

  const handleMoveRuleDown = (index: number) => {
    if (index === rules.length - 1) return; // Can't move down if already at the bottom
    const newRules = [...rules];
    [newRules[index], newRules[index + 1]] = [newRules[index + 1], newRules[index]];
    setRules(newRules);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/hubs/${hubId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          description,
          private: isPrivate,
          welcomeMessage: welcomeMessage || null,
          rules,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update hub");
      }

      toast({
        title: "Hub Updated",
        description: "Your hub has been updated successfully.",
      });

      // Refresh the page to see the updated data
      router.refresh();
    } catch (error) {
      console.error("Error updating hub:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to update hub",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href={`/dashboard/hubs/${hubId}`}>
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">
            Edit Hub
          </h1>
        </div>
        {isOwner && <DeleteHubDialog hubId={hubId as string} hubName={name} />}
      </div>
      <HubNavigationTabs
        hubId={hubId as string}
        currentTab="edit"
        canModerate={isOwner}
        canEdit={true}
      />
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Hub Details</CardTitle>
            <CardDescription>
              Update your hub&apos;s information and settings.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Hub Name</Label>
              <Input
                id="name"
                placeholder="Enter hub name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                minLength={3}
                maxLength={32}
                className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50"
              />
              <p className="text-xs text-gray-400">
                Choose a unique name between 3-32 characters.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your hub"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                required
                minLength={10}
                maxLength={500}
                className="min-h-[100px] bg-gray-800/50 border-gray-700/50"
              />
              <p className="text-xs text-gray-400">
                Provide a clear description between 10-500 characters.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="welcomeMessage">Welcome Message <span className="text-gray-400">(Optional)</span></Label>
              <Textarea
                id="welcomeMessage"
                placeholder="Welcome message for new members"
                value={welcomeMessage}
                onChange={(e) => setWelcomeMessage(e.target.value)}
                maxLength={1000}
                className="min-h-[100px] bg-gray-800/50 border-gray-700/50 resize-y whitespace-pre-wrap"
              />
              <div className="text-xs text-gray-400">
                <p className="mb-1">This message will be shown to new members when they join your hub.</p>
                <p>You can use these variables:</p>
                <div className="flex flex-wrap gap-2 mt-1 max-w-full overflow-hidden">
                  {formattedWelcomeVariablesList}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Hub Rules</Label>
              <div className="flex flex-col sm:flex-row gap-2">
                <Textarea
                  placeholder="Add a rule"
                  value={newRule}
                  onChange={(e) => setNewRule(e.target.value)}
                  className="bg-gray-800/50 border-gray-700/50 min-h-[60px] resize-y flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleAddRule}
                  disabled={!newRule.trim()}
                  className="sm:self-start"
                >
                  Add
                </Button>
              </div>
              <div className="mt-2 space-y-2">
                {rules.length > 0 ? (
                  rules.map((rule, index) => (
                    <div
                      key={index}
                      className="flex flex-col sm:flex-row items-start sm:justify-between p-2 rounded bg-gray-800/50 border border-gray-700/50 gap-2"
                    >
                      <div className="flex items-start gap-2">
                        <div className="flex flex-col gap-1">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="cursor-pointer h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-700/50"
                                  onClick={() => handleMoveRuleUp(index)}
                                  disabled={index === 0}
                                >
                                  <ArrowUp className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Move rule up</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="cursor-pointer h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-700/50"
                                  onClick={() => handleMoveRuleDown(index)}
                                  disabled={index === rules.length - 1}
                                >
                                  <ArrowDown className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Move rule down</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <div className="flex-1">
                          <div className="text-xs text-gray-400 mb-1">Rule {index + 1}</div>
                          <span className="text-sm whitespace-pre-wrap">
                            {rule}
                          </span>
                        </div>
                      </div>
                      <div className="self-end sm:self-start">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveRule(index)}
                                className="cursor-pointer text-red-500 hover:text-red-400 hover:bg-red-500/10"
                              >
                                <Trash2 className="h-4 w-4 mr-1" />
                                Remove
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Delete this rule</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-400">No rules added yet.</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Switch
                  id="private"
                  checked={isPrivate}
                  onCheckedChange={setIsPrivate}
                />
                <Label htmlFor="private">Private Hub</Label>
              </div>
              <p className="text-xs text-gray-400">
                Private hubs are only visible to members and require you to send
                an invite to join.
              </p>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="cursor-pointer w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>

            {isOwner && (
              <div className="w-full pt-4 border-t border-gray-800">
                <h3 className="text-lg font-semibold text-red-500 mb-2">
                  Danger Zone
                </h3>
                <p className="text-sm text-gray-400 mb-4">
                  Once you delete a hub, there is no going back. Please be
                  certain.
                </p>
                <DeleteHubDialog hubId={hubId as string} hubName={name} />
              </div>
            )}
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}

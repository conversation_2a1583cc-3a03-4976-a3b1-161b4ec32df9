import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { auth } from "@/auth";
import { getUserHubPermission } from "@/lib/permissions";
import { PermissionLevel } from "@/lib/constants";

export async function DELETE(
  request: NextRequest,
  props: { params: Promise<{ hubId: string; connectionId: string }> },
) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId, connectionId } = await props.params;

    // Check if user has permission to manage this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);

    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json(
        {
          error: "You don't have permission to manage connections for this hub",
        },
        { status: 403 },
      );
    }

    // Check if connection exists
    const connection = await prisma.connection.findUnique({
      where: {
        id: connectionId,
      },
      
    });

    if (!connection) {
      return NextResponse.json(
        { error: "Connection not found" },
        { status: 404 },
      );
    }

    // Check if connection belongs to the specified hub
    if (connection.hubId !== hubId) {
      return NextResponse.json(
        { error: "Connection does not belong to this hub" },
        { status: 403 },
      );
    }

    // Delete the connection
    await prisma.connection.delete({
      where: {
        id: connectionId,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error removing connection:", error);
    return NextResponse.json(
      { error: "Failed to remove connection" },
      { status: 500 },
    );
  }
}

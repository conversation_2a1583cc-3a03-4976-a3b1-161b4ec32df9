import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";

// Schema for creating an announcement
const createAnnouncementSchema = z.object({
  title: z.string().min(1).max(100),
  content: z.string().min(1).max(1000),
  thumbnailUrl: z.string().url().optional(),
  imageUrl: z.string().url().optional(),
});

// GET all announcements
export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get all announcements ordered by creation date (newest first)
    const announcements = await prisma.announcement.findMany({
      orderBy: { createdAt: "desc" },
    });

    return NextResponse.json(announcements);
  } catch (error) {
    console.error("Error fetching announcements:", error);
    return NextResponse.json(
      { error: "Failed to fetch announcements" },
      { status: 500 }
    );
  }
}

// POST (create) a new announcement
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the user has the specific admin ID
    const ADMIN_USER_ID = "701727675311587358";

    if (session.user.id !== ADMIN_USER_ID) {
      return NextResponse.json(
        { error: "Only authorized administrators can create announcements" },
        { status: 403 }
      );
    }

    const data = await request.json();

    // Validate the request body
    const validationResult = createAnnouncementSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid announcement data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Create the announcement
    const announcement = await prisma.announcement.create({
      data: validationResult.data,
    });

    return NextResponse.json(announcement, { status: 201 });
  } catch (error) {
    console.error("Error creating announcement:", error);
    return NextResponse.json(
      { error: "Failed to create announcement" },
      { status: 500 }
    );
  }
}

// DELETE an announcement
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the user has the specific admin ID
    const ADMIN_USER_ID = "701727675311587358";

    if (session.user.id !== ADMIN_USER_ID) {
      return NextResponse.json(
        { error: "Only authorized administrators can delete announcements" },
        { status: 403 }
      );
    }

    const searchParams = new URL(request.url).searchParams;
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Announcement ID is required" },
        { status: 400 }
      );
    }

    // Delete the announcement
    await prisma.announcement.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting announcement:", error);
    return NextResponse.json(
      { error: "Failed to delete announcement" },
      { status: 500 }
    );
  }
}
